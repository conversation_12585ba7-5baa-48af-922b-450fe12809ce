#!/usr/bin/env python3
"""Script to download and process ACAPS areas of control data.

This script:
1. Downloads ACAPS control zone data from HDX
2. Processes bi-weekly updates into time series
3. Creates monthly aligned dataset for analysis
4. Generates summary statistics
"""

import logging
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.data.hdx_client import HDXClient
from yemen_market.data.acaps_processor import ACAPSProcessor
from yemen_market.utils import setup_logging

# Set up logging
setup_logging()
logger = logging.getLogger(__name__)


def download_acaps_data():
    """Download ACAPS control zone data from HDX."""
    logger.info("Initializing HDX client for ACAPS data download")
    
    client = HDXClient()
    
    # Search for ACAPS Yemen areas of control dataset
    # The dataset ID might be different, we'll search for it
    dataset_ids = [
        "yemen-areas-of-control",
        "yemen-acaps-areas-control",
        "acaps-yemen-analysis-hub"
    ]
    
    for dataset_id in dataset_ids:
        try:
            logger.info(f"Trying to download dataset: {dataset_id}")
            filepath = client.download_dataset(
                dataset_id,
                resource_filter=lambda r: 'control' in r.get('name', '').lower()
                                       or 'areas' in r.get('name', '').lower()
            )
            if filepath:
                logger.info(f"Successfully downloaded ACAPS data: {filepath}")
                return filepath
        except Exception as e:
            logger.warning(f"Failed to download {dataset_id}: {e}")
            continue
    
    logger.error("Could not download ACAPS data from any known dataset ID")
    return None


def process_acaps_data():
    """Process ACAPS control zone data."""
    logger.info("Starting ACAPS data processing")
    
    processor = ACAPSProcessor()
    
    # Process all files in the ACAPS directory
    logger.info("Processing all ACAPS control files")
    control_data = processor.process_all_files()
    
    if control_data.empty:
        logger.warning("No ACAPS data found to process")
        return
    
    logger.info(f"Processed {len(control_data)} control zone records")
    logger.info(f"Date range: {control_data['date'].min()} to {control_data['date'].max()}")
    logger.info(f"Number of districts: {control_data[['governorate', 'district']].drop_duplicates().shape[0]}")
    
    # Create time series
    logger.info("Creating control zone time series")
    time_series = processor.create_control_time_series(control_data)
    
    if not time_series.empty:
        # Count control changes
        n_changes = time_series['control_changed'].sum()
        logger.info(f"Total control changes detected: {n_changes}")
        
        # Show transition types
        transitions = time_series[time_series['control_changed']]['transition_type'].value_counts()
        logger.info("Control zone transitions:")
        for transition, count in transitions.items():
            logger.info(f"  {transition}: {count}")
    
    # Align to monthly frequency
    logger.info("Aligning data to monthly frequency")
    monthly_data = processor.align_to_monthly(time_series)
    
    if not monthly_data.empty:
        logger.info(f"Created monthly dataset with {len(monthly_data)} records")
        
        # Show control zone distribution
        control_dist = monthly_data['control_zone'].value_counts()
        logger.info("Monthly control zone distribution:")
        for zone, count in control_dist.items():
            logger.info(f"  {zone}: {count} district-months")
    
    # Save processed data
    logger.info("Saving processed data")
    saved_files = processor.save_processed_data(control_data, time_series, monthly_data)
    
    logger.info("Saved files:")
    for file_type, path in saved_files.items():
        logger.info(f"  {file_type}: {path}")
    
    # Generate summary statistics
    logger.info("Generating summary statistics")
    summary = processor.generate_summary_stats(monthly_data)
    
    if not summary.empty:
        logger.info("Summary statistics:")
        for _, row in summary.iterrows():
            logger.info(f"  {row['metric']}: {row['value']}")
    
    logger.info("ACAPS data processing complete")


def main():
    """Main execution function."""
    logger.info("=" * 60)
    logger.info("ACAPS Control Zone Data Processing")
    logger.info("=" * 60)
    
    # First try to download latest data
    logger.info("\nStep 1: Downloading ACAPS data from HDX")
    download_result = download_acaps_data()
    
    if download_result:
        logger.info(f"Download successful: {download_result}")
    else:
        logger.info("Download failed, will process existing files if available")
    
    # Process the data
    logger.info("\nStep 2: Processing ACAPS control zone data")
    process_acaps_data()
    
    logger.info("\nProcessing complete!")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.error(f"Error in ACAPS processing: {e}", exc_info=True)
        sys.exit(1)