#!/usr/bin/env python3
"""Build integrated panel dataset from processed components."""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.yemen_market.data.panel_builder import PanelDatasetBuilder
from src.yemen_market.utils.logging import setup_logging, info, success, timer

def main():
    """Build integrated panel dataset."""
    setup_logging("panel_builder")
    
    with timer("build_panel"):
        info("Building integrated panel dataset")
        
        # Initialize builder
        builder = PanelDatasetBuilder()
        
        # Build the panel
        panel_df = builder.build_integrated_panel()
        
        # Save
        output_dir = Path("data/processed/panels")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_path = output_dir / "integrated_panel.parquet"
        panel_df.to_parquet(output_path, index=False)
        
        success(f"Saved integrated panel to {output_path}")
        info(f"Panel shape: {panel_df.shape}")
        info(f"Date range: {panel_df['date'].min()} to {panel_df['date'].max()}")

if __name__ == "__main__":
    main()
