#!/usr/bin/env python3
"""Download initial datasets from HDX for Yemen market integration analysis.

This script downloads all required datasets from the Humanitarian Data Exchange:
- WFP food prices for Yemen
- ACAPS areas of control
- Administrative boundaries
- ACLED conflict data (if available)
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.data.hdx_client import HDXClient
from yemen_market.utils.logging import (
    bind, timer, progress, log_metric,
    info, warning, error, debug
)

# Set up logging context
bind(script="download_data")


def download_all_datasets():
    """Download all required datasets from HDX."""
    with timer("download_all_datasets"):
        info("=" * 60)
        info("Yemen Market Integration - Data Download")
        info("=" * 60)
        
        # Initialize HDX client
        info("\nInitializing HDX client...")
        client = HDXClient()
        
        # Track download results
        results = {}
        
        # 1. Download WFP food prices (includes exchange rates)
        info("\n" + "="*40)
        info("Downloading: WFP food price data")
        info("="*40)
        try:
            df = client.download_wfp_food_prices()
            if df is not None:
                results['wfp_food_prices'] = {
                    'status': 'success',
                    'shape': df.shape,
                    'columns': list(df.columns)
                }
                info(f"✅ Success: Downloaded {df.shape[0]} rows, {df.shape[1]} columns")
                info(f"   Columns: {', '.join(df.columns[:5])}...")
                log_metric("wfp_rows", df.shape[0])
                log_metric("wfp_columns", df.shape[1])
            else:
                results['wfp_food_prices'] = {'status': 'failed'}
                warning("⚠️  Failed: No data downloaded")
        except Exception as e:
            results['wfp_food_prices'] = {'status': 'error', 'error': str(e)}
            error(f"❌ Error: {e}")
        
        # 2. Download administrative boundaries
        info("\n" + "="*40)
        info("Downloading: Administrative boundaries")
        info("="*40)
        try:
            boundary_files = client.download_admin_boundaries()
            if boundary_files:
                results['admin_boundaries'] = {
                    'status': 'success',
                    'files': list(boundary_files.keys()),
                    'paths': boundary_files
                }
                info(f"✅ Success: Downloaded {len(boundary_files)} boundary files")
                for name, path in boundary_files.items():
                    info(f"   - {name}: {path}")
                log_metric("boundary_files", len(boundary_files))
            else:
                results['admin_boundaries'] = {'status': 'failed'}
                warning("⚠️  Failed: No boundary files downloaded")
        except Exception as e:
            results['admin_boundaries'] = {'status': 'error', 'error': str(e)}
            error(f"❌ Error: {e}")
        
        # 3. Download ACLED conflict data
        info("\n" + "="*40)
        info("Downloading: ACLED conflict data")
        info("="*40)
        try:
            acled_path = client.download_acled_conflict_data()
            if acled_path:
                results['acled_conflict'] = {
                    'status': 'success',
                    'path': str(acled_path),
                    'file': acled_path.name
                }
                info(f"✅ Success: Downloaded ACLED data to {acled_path}")
                log_metric("acled_downloaded", 1)
            else:
                results['acled_conflict'] = {'status': 'failed'}
                warning("⚠️  Failed: No ACLED data downloaded")
        except Exception as e:
            results['acled_conflict'] = {'status': 'error', 'error': str(e)}
            error(f"❌ Error: {e}")
        
        # 4. Download other datasets using generic method
        other_datasets = {
            'acaps_control': {
                'id': 'yemen-areas-of-control',
                'description': 'ACAPS territorial control data'
            }
        }
        
        for name, dataset_info in other_datasets.items():
            info(f"\n{'='*40}")
            info(f"Downloading: {dataset_info['description']}")
            info(f"Dataset ID: {dataset_info['id']}")
            info(f"{'='*40}")
            
            try:
                dataset = client.get_dataset(dataset_info['id'])
                if dataset:
                    # Get metadata
                    metadata = client.get_metadata(dataset_info['id'])
                    results[name] = {
                        'status': 'success',
                        'metadata': metadata,
                        'resources': len(dataset.get_resources()) if dataset else 0
                    }
                    info(f"✅ Success: Found dataset with {results[name]['resources']} resources")
                    if metadata:
                        info(f"   Title: {metadata.get('title')}")
                        info(f"   Updated: {metadata.get('updated')}")
                else:
                    results[name] = {'status': 'failed'}
                    warning("⚠️  Failed: Dataset not found")
            except Exception as e:
                results[name] = {'status': 'error', 'error': str(e)}
                error(f"❌ Error: {e}")
        
        # Summary report
        info("\n" + "=" * 60)
        info("Download Summary")
        info("=" * 60)
        
        successful = sum(1 for r in results.values() if r.get('status') == 'success')
        info(f"\nTotal datasets: {len(results)}")
        info(f"Successfully downloaded: {successful}")
        info(f"Failed: {len(results) - successful}")
        
        log_metric("datasets_total", len(results))
        log_metric("datasets_successful", successful)
        log_metric("datasets_failed", len(results) - successful)
        
        info("\nDetailed results:")
        for name, result in results.items():
            if result.get('status') == 'success':
                info(f"  ✅ {name}: Success")
            else:
                info(f"  ❌ {name}: {result.get('error', 'Failed')}")
        
        return results


def verify_downloads(results):
    """Verify that essential datasets were downloaded."""
    essential = ['wfp_food_prices', 'admin_boundaries']
    missing_essential = []
    
    for dataset in essential:
        if dataset not in results or results[dataset].get('status') != 'success':
            missing_essential.append(dataset)
    
    if missing_essential:
        error(f"\n⚠️  Missing essential datasets: {', '.join(missing_essential)}")
        error("These datasets are required for the analysis pipeline.")
        return False
    
    info("\n✅ All essential datasets downloaded successfully!")
    return True


def main():
    """Main execution function."""
    try:
        with timer("main_execution"):
            info("Starting Yemen market integration data download")
            
            # Download all datasets
            results = download_all_datasets()
            
            # Verify essential datasets
            if verify_downloads(results):
                info("\n" + "=" * 60)
                info("✅ Data download completed successfully!")
                info("=" * 60)
                info("\nNext steps:")
                info("1. Run scripts/process_wfp_data.py to process price data")
                info("2. Run scripts/process_acaps_data.py to process control zones")
                info("3. Run scripts/process_acled_data.py to process conflict data")
                info("4. Run scripts/run_spatial_joins.py to map markets to zones")
                info("5. Run scripts/build_panel_datasets.py to create analysis panels")
                return 0
            else:
                error("\n❌ Some essential datasets failed to download.")
                error("Please check your internet connection and try again.")
                return 1
                
    except KeyboardInterrupt:
        info("\n\nDownload interrupted by user")
        return 1
    except Exception as e:
        error(f"\nUnexpected error: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    sys.exit(main())