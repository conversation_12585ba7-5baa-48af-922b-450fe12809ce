# 📋 Claude Code Quick Reference

## 🚨 MUST READ FILES (In Order)

1. **`.claude/development_rules.md`** - MANDATORY rules for development
2. **`CLAUDE.md`** - Main project guide with critical rules at top
3. **`.claude/tasks/week_3_4_sprint.md`** - Current tasks
4. **`.claude/project_memory.md`** - Full context and status

## 🎯 Current Status
- **Phase**: Week 3-4 (EDA & Feature Engineering)
- **Progress**: 35% overall
- **Active**: Processing ACAPS data, creating visualizations

## ⚡ Quick Commands

```python
# ALWAYS start modules with:
from yemen_market.utils.logging import (
    info, debug, warning, error,
    timer, progress, log_data_shape,
    bind
)
bind(module=__name__)

# Time operations:
with timer("operation_name"):
    # code here

# Show progress:
with progress("Task", total=n) as update:
    for item in items:  # ALL items!
        process(item)
        update(1)
```

## 🚫 NEVER DO
- Create temporary files that won't be reused
- Use print() or basic logging  
- Skip data/steps to save time
- Sample data instead of processing all
- Leave failed attempts in the codebase

## ✅ ALWAYS DO
- Use enhanced logging everywhere
- Process ALL data with progress bars
- Delete temporary files immediately
- Complete every step fully
- Keep only essential files

## 📁 Directory Rules
- `scripts/` - ONLY 5 essential pipeline scripts
- `src/` - Production code only
- `examples/` - All demos and experiments
- `tests/` - All test files

## 🔧 Essential Scripts (Keep These Only)
1. `download_data.py`
2. `process_wfp_data.py` 
3. `process_acaps_data.py`
4. `run_spatial_joins.py`
5. `build_panel_datasets.py`

Everything else should be in examples/ or deleted!
