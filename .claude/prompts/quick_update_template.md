# Quick Claude Code Update Script

Copy and paste this into Claude Code for routine documentation updates:

```
Update Yemen Market Integration .claude/ folder and CLAUDE.md:

TODAY'S UPDATES:
- Implemented Week 5 sprint code with econometric corrections
- Fixed <PERSON> bootstrap, added threshold constraints
- Created dual-track model runners and notebooks
- All code ready for execution

SPECIFIC TASKS:
1. Update .claude/project_memory.md:
   - Add Week 5 implementation to session accomplishments
   - Update model status from "Not started" to "Framework complete, ready to run"
   - Add new scripts created

2. Update .claude/ACTIVE_CONTEXT.md:
   - Current phase: Week 5-6 Model Implementation
   - Mark threshold VECM as implemented
   - Mark Bayesian simplified model as implemented
   - Next: Run on actual data and compare results

3. Update .claude/tasks/week_5_6_sprint.md:
   - Mark model implementation tasks as complete
   - Note that empirical runs are pending

4. Keep CLAUDE.md critical rules unchanged
   - Just update current phase to "Week 5-6: Model Implementation"

5. Ensure no duplication - each fact in one place only

Please make updates while maintaining documentation hierarchy and standards.
```

## Even Quicker Version

```
Quick update for .claude/ - Week 5 complete:
- ✅ Threshold VECM with corrected bootstrap
- ✅ Simplified Bayesian regime model  
- ✅ Model comparison framework
- ✅ All scripts and notebooks ready
- 📍 Next: Run models on actual data

Update: project_memory.md (accomplishments), ACTIVE_CONTEXT.md (current status), week_5_6_sprint.md (mark complete)
```

## For Daily Standup Updates

```
Daily update [DATE]:
DONE: [What was completed]
DOING: [Current task]
BLOCKERS: [Any issues]
NEXT: [Tomorrow's priority]

Update .claude/ACTIVE_CONTEXT.md and current sprint file.
```
