#!/bin/bash
# Setup script for Yemen Market Integration virtual environment

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Yemen Market Integration - Virtual Environment Setup${NC}"
echo "======================================================="

# Check Python version
PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
echo -e "${YELLOW}Current Python version: ${PYTHON_VERSION}${NC}"

# Check if Python 3.13 warning needed
if [[ "$PYTHON_VERSION" == 3.13.* ]]; then
    echo -e "${YELLOW}Warning: Python 3.13 detected. Some packages might have compatibility issues.${NC}"
    echo -e "${YELLOW}Recommended: Python 3.10 or 3.11 for best compatibility.${NC}"
    echo -e "${YELLOW}Proceeding with Python 3.13 - some packages may require workarounds.${NC}"
fi

# Remove existing virtual environments
echo -e "\n${YELLOW}Cleaning up existing virtual environments...${NC}"
if [ -d "venv" ]; then
    rm -rf venv
    echo "Removed old venv directory"
fi
if [ -d ".venv" ]; then
    rm -rf .venv
    echo "Removed old .venv directory"
fi

# Create new virtual environment
echo -e "\n${GREEN}Creating new virtual environment...${NC}"
python3 -m venv venv

# Activate virtual environment
echo -e "\n${GREEN}Activating virtual environment...${NC}"
source venv/bin/activate

# Upgrade pip, setuptools, and wheel
echo -e "\n${GREEN}Upgrading pip, setuptools, and wheel...${NC}"
pip install --upgrade pip setuptools wheel

# Install dependencies
echo -e "\n${GREEN}Installing dependencies from requirements.txt...${NC}"
pip install -r requirements.txt

# Install project in editable mode
echo -e "\n${GREEN}Installing project in editable mode...${NC}"
pip install -e .

# Install development dependencies
echo -e "\n${GREEN}Installing development dependencies...${NC}"
pip install -e ".[dev]"

# Install Jupyter kernel
echo -e "\n${GREEN}Setting up Jupyter kernel...${NC}"
python -m ipykernel install --user --name yemen-market --display-name "Yemen Market Integration"

# Verify installation
echo -e "\n${GREEN}Verifying installation...${NC}"
python -c "
import pandas as pd
import numpy as np
import geopandas as gpd
import pymc as pm
import yemen_market
print('✓ Core packages imported successfully')
print(f'✓ yemen_market version: {yemen_market.__version__ if hasattr(yemen_market, \"__version__\") else \"development\"}')
print(f'✓ pandas version: {pd.__version__}')
print(f'✓ geopandas version: {gpd.__version__}')
print(f'✓ pymc version: {pm.__version__}')
"

echo -e "\n${GREEN}Setup complete!${NC}"
echo -e "${YELLOW}To activate the environment in the future, run:${NC}"
echo -e "  source venv/bin/activate"
echo -e "\n${YELLOW}To use in Jupyter, select kernel:${NC}"
echo -e "  'Yemen Market Integration'"