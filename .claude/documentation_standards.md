# Documentation Standards and Anti-Repetition Rules

## 🚨 CRITICAL: Documentation Hierarchy

This file defines the MANDATORY documentation structure to prevent repetition and maintain clarity.

## File Purposes and Boundaries

### 1. Progress Tracking Files

#### `reports/progress/README.md` (SINGLE SOURCE OF TRUTH)
**CONTAINS:**
- Overall progress percentage
- Milestone tracking table
- Sprint details and goals
- Completed task lists
- Upcoming task lists
- Key metrics (LOC, coverage, etc.)
- Session notes and findings
- Technical decisions log
- Data source status

**NEVER PUT HERE:**
- Development instructions
- Code examples
- Implementation details

#### `.claude/ACTIVE_CONTEXT.md` (CURRENT FOCUS ONLY)
**CONTAINS:**
- Current sprint name and phase
- Currently working on (max 5 items)
- Recent accomplishments (max 3 items)
- Immediate next actions (max 5 items)
- "To Resume" instructions
- One-line link to progress dashboard

**NEVER PUT HERE:**
- Historical progress
- Completed task lists
- Detailed timelines
- Technical specifications

### 2. Development Documentation

#### `CLAUDE.md` (DEVELOPMENT RULES ONLY)
**CONTAINS:**
- Critical development rules
- Enhanced logging requirements
- Code style guidelines
- MCP server configuration
- Common commands
- Documentation hierarchy rules

**NEVER PUT HERE:**
- Project status
- Progress tracking
- Timeline information
- Current tasks

#### `README.md` (PUBLIC FACING ONLY)
**CONTAINS:**
- Project overview
- Installation instructions
- Quick start guide
- Usage examples
- Feature descriptions
- Requirements
- License and citation

**NEVER PUT HERE:**
- Progress percentages
- Recent updates
- Development timeline
- Current status
- Sprint information

### 3. Technical Documentation

#### `METHODOLOGY.md`
**CONTAINS:**
- Econometric methodology
- Model specifications
- Technical implementation details
- Academic references

**NEVER PUT HERE:**
- Progress tracking
- Current development status

#### `.claude/project_memory.md`
**CONTAINS:**
- Living document of project evolution
- Key decisions and rationale
- Historical context
- Lessons learned

**NEVER PUT HERE:**
- Current progress percentages
- Active task lists

## Anti-Repetition Checklist

Before updating ANY documentation:

1. **Is this progress information?**
   → Goes in `reports/progress/README.md` ONLY

2. **Is this about current development?**
   → Goes in `.claude/ACTIVE_CONTEXT.md` ONLY

3. **Is this a development rule/pattern?**
   → Goes in `CLAUDE.md` ONLY

4. **Is this for public users?**
   → Goes in `README.md` ONLY

5. **Is this technical methodology?**
   → Goes in `METHODOLOGY.md` ONLY

## Red Flags (NEVER DO)

🚫 **NEVER** create files like:
- PROJECT_STATUS.md
- CURRENT_WORK.md
- PROGRESS.md
- STATUS_UPDATE.md
- Or any variation thereof

🚫 **NEVER** duplicate information across files

🚫 **NEVER** put progress tracking in:
- README.md
- CLAUDE.md
- Any technical documentation

🚫 **NEVER** create "temporary" documentation files

## Update Protocol

### When Starting a Session:
1. Read `.claude/ACTIVE_CONTEXT.md` for immediate context
2. Check `reports/progress/README.md` for detailed progress
3. Review this file if unsure where to document

### When Ending a Session:
1. Update progress percentage in `reports/progress/README.md`
2. Update current focus in `.claude/ACTIVE_CONTEXT.md`
3. DO NOT create new status files

### When Adding Documentation:
1. First check if it fits in existing files
2. Use the checklist above to find the right location
3. If genuinely new category, discuss before creating file

### Using Claude Code for Updates:
1. **Use prompt templates**:
   - Full update: `.claude/prompts/update_claude_docs_prompt.md`
   - Quick update: `.claude/prompts/quick_update_template.md`
   - Copy relevant prompt into Claude Code
2. **Follow hierarchy**: project_memory > ACTIVE_CONTEXT > sprint files
3. **Add dates** to all updates
4. **Verify no duplication** after updates

## Enforcement

These rules are MANDATORY. Any violation should be immediately corrected by:
1. Moving content to the correct location
2. Deleting redundant files
3. Updating this documentation if new patterns emerge

Remember: **Less files, more clarity**