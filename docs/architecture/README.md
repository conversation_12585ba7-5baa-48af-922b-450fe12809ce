# Architecture Overview

This document describes the high-level architecture of the Yemen Market Integration project.

## System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                        Data Sources                          │
├─────────────┬─────────────┬─────────────┬──────────────────┤
│     HDX     │    ACLED    │    ACAPS    │   Yemen Pcodes   │
│   (Prices)  │  (Conflict) │  (Zones)    │    (Admin)       │
└──────┬──────┴──────┬──────┴──────┬──────┴──────┬───────────┘
       │             │              │              │
       ▼             ▼              ▼              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Data Collection Layer                     │
│  ┌─────────────┐  ┌──────────────┐  ┌──────────────────┐  │
│  │ HDXClient   │  │ ACLED API    │  │ Manual Download  │  │
│  └─────────────┘  └──────────────┘  └──────────────────┘  │
└─────────────────────────────┬───────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Data Processing Layer                      │
│  ┌──────────────┐  ┌───────────────┐  ┌────────────────┐  │
│  │WFPProcessor  │  │ACLEDProcessor │  │ACAPSProcessor  │  │
│  │(88.4% cover) │  │(Conflict)     │  │(Control zones) │  │
│  └──────────────┘  └───────────────┘  └────────────────┘  │
│           │                 │                  │            │
│           └─────────────────┴──────────────────┘            │
│                             │                               │
│                    ┌────────▼────────┐                      │
│                    │ SpatialJoiner   │                      │
│                    │ (Market→Zone)   │                      │
│                    └────────┬────────┘                      │
└─────────────────────────────┴───────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Integration Layer                         │
│                   ┌─────────────────┐                       │
│                   │  PanelBuilder   │                       │
│                   │ (44K obs panel) │                       │
│                   └────────┬────────┘                       │
└────────────────────────────┴────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 Feature Engineering Layer                    │
│                  ┌──────────────────┐                       │
│                  │ FeatureEngineer  │                       │
│                  │ (100+ features)  │                       │
│                  └────────┬─────────┘                       │
└───────────────────────────┴─────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Analysis Layer                            │
│  ┌────────────┐  ┌─────────────┐  ┌──────────────────┐    │
│  │ Notebooks  │  │   Models    │  │  Visualization   │    │
│  └────────────┘  └─────────────┘  └──────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

## Component Details

### Data Collection Layer
Responsible for retrieving raw data from external sources:
- **HDXClient**: Downloads humanitarian datasets
- **ACLED API**: Fetches conflict event data
- **Manual Downloads**: ACAPS shapefiles and other files

### Data Processing Layer
Transforms raw data into analysis-ready formats:
- **WFPProcessor**: Processes price data with 88.4% coverage
- **ACLEDProcessor**: Creates market-level conflict metrics
- **ACAPSProcessor**: Extracts temporal control zones
- **SpatialJoiner**: Maps markets to zones spatially

### Integration Layer
Combines processed data into unified datasets:
- **PanelBuilder**: Creates integrated panels for analysis
- Handles temporal alignment and missing data
- Produces multiple panel structures for different analyses

### Feature Engineering Layer
Creates derived variables for modeling:
- **FeatureEngineer**: Generates 100+ features
- Temporal, spatial, interaction, and threshold features
- Automated feature creation pipeline

### Analysis Layer
Final analysis and output generation:
- **Notebooks**: Interactive analysis and exploration
- **Models**: Econometric models (VECM, spatial, etc.)
- **Visualization**: Charts, maps, and reports

## Data Flow

1. **Raw Data** → Downloaded to `data/raw/`
2. **Processing** → Intermediate files in `data/interim/`
3. **Integration** → Final datasets in `data/processed/`
4. **Analysis** → Results in `reports/` and `notebooks/`

## Key Design Decisions

### 1. Modular Architecture
- Each processor handles one data source
- Clear separation of concerns
- Easy to extend or modify components

### 2. Pcode Standardization
- All location names mapped to Yemen pcodes
- Ensures consistent joining across datasets
- Improves data quality from 62% to 88.4%

### 3. Smart Panel Creation
- Only includes commodity-market pairs that exist
- Reduces false missing data
- More accurate for econometric analysis

### 4. Enhanced Logging
- Context-aware logging throughout
- Performance tracking with timers
- Progress bars for long operations

### 5. Flexible Configuration
- Central settings in `config/settings.py`
- Environment variables for sensitive data
- Easy to adjust parameters

## Technology Stack

- **Python 3.9+**: Core language
- **Pandas**: Data manipulation
- **GeoPandas**: Spatial operations
- **NumPy**: Numerical computing
- **Scikit-learn**: Machine learning utilities
- **Matplotlib/Seaborn**: Visualization
- **Jupyter**: Interactive analysis

## Scalability Considerations

1. **Memory Efficiency**: 
   - Parquet format for large datasets
   - Chunked processing where needed
   - Efficient data types

2. **Performance**:
   - Vectorized operations
   - Spatial indexing for geo operations
   - Caching of expensive computations

3. **Extensibility**:
   - New data sources easy to add
   - Feature engineering is modular
   - Clear interfaces between components