"""Spatial joins for mapping markets to control zones in Yemen.

This module handles spatial operations to map WFP market locations to ACAPS
control zones, accounting for temporal changes in territorial control.
"""

from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import geopandas as gpd
import numpy as np
import pandas as pd
from shapely.geometry import Point
from scipy.spatial import cKDTree

from ..config.settings import PROCESSED_DATA_DIR, INTERIM_DATA_DIR, RAW_DATA_DIR
from ..utils.logging import (
    bind, context, timer, progress, log_data_shape,
    info, warning, error, debug
)


class SpatialJoiner:
    """Map markets to control zones using spatial operations.
    
    This class handles:
    - Loading market coordinates from WFP data
    - Loading control zone boundaries from ACAPS
    - Performing spatial joins with temporal dimension
    - Handling markets near control zone boundaries
    - Creating market-zone lookup tables
    - Calculating distance-based weights
    
    Attributes:
        markets_dir: Directory containing market data
        control_dir: Directory containing control zone data
        output_dir: Directory for saving joined data
        buffer_distance_km: Distance in km for boundary buffer analysis
    """
    
    # Earth radius for distance calculations
    EARTH_RADIUS_KM = 6371.0
    
    # Default buffer distance for boundary markets (km)
    DEFAULT_BUFFER_KM = 10.0
    
    def __init__(self, 
                 markets_dir: Optional[Path] = None,
                 control_dir: Optional[Path] = None,
                 output_dir: Optional[Path] = None,
                 buffer_distance_km: float = DEFAULT_BUFFER_KM):
        """Initialize spatial joiner.
        
        Args:
            markets_dir: Directory with market data
            control_dir: Directory with control zone data
            output_dir: Output directory for joined data
            buffer_distance_km: Buffer distance for boundary analysis
        """
        self.markets_dir = markets_dir or PROCESSED_DATA_DIR / "wfp"
        self.control_dir = control_dir or PROCESSED_DATA_DIR / "control_zones"
        self.output_dir = output_dir or PROCESSED_DATA_DIR / "spatial"
        self.buffer_distance_km = buffer_distance_km
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        info("SpatialJoiner initialized", buffer_km=buffer_distance_km)
    
    def load_market_coordinates(self, wfp_data_path: Optional[Path] = None) -> gpd.GeoDataFrame:
        """Load market coordinates from WFP data.
        
        Args:
            wfp_data_path: Path to WFP processed data
            
        Returns:
            GeoDataFrame with market points
        """
        if wfp_data_path is None:
            # Try to find the most recent WFP data
            wfp_files = list(self.markets_dir.glob("*price_data*.parquet"))
            if not wfp_files:
                wfp_files = list(self.markets_dir.glob("*price_data*.csv"))
            
            if not wfp_files:
                raise FileNotFoundError(f"No WFP data found in {self.markets_dir}")
            
            wfp_data_path = sorted(wfp_files)[-1]  # Most recent
        
        info("Loading market coordinates", path=str(wfp_data_path))
        
        # Load data
        if wfp_data_path.suffix == '.parquet':
            df = pd.read_parquet(wfp_data_path)
        else:
            df = pd.read_csv(wfp_data_path)
        
        # Check which columns are available
        required_cols = []
        
        # Map governorate column
        if 'governorate' in df.columns:
            required_cols.append('governorate')
        elif 'admin1' in df.columns:
            df['governorate'] = df['admin1']
            required_cols.append('governorate')
        
        # Map market name column
        if 'market_name' in df.columns:
            required_cols.append('market_name')
        elif 'market' in df.columns:
            df['market_name'] = df['market']
            required_cols.append('market_name')
            
        coord_cols = []
        
        # Check for various coordinate column names
        if 'lat' in df.columns and 'lon' in df.columns:
            coord_cols = ['lat', 'lon']
        elif 'latitude' in df.columns and 'longitude' in df.columns:
            coord_cols = ['latitude', 'longitude']
            df['lat'] = df['latitude']
            df['lon'] = df['longitude']
        else:
            raise ValueError(f"No coordinate columns found. Available columns: {list(df.columns)}")
        
        # Get all location columns that exist
        location_cols = required_cols.copy()
        if 'district' in df.columns:
            location_cols.append('district')
        elif 'admin2' in df.columns:
            location_cols.append('admin2')
            df['district'] = df['admin2']
        
        location_cols.extend(['lat', 'lon'])
        
        # Get unique markets with coordinates
        markets = df[location_cols].drop_duplicates()
        
        # Remove markets without coordinates
        markets = markets.dropna(subset=['lat', 'lon'])
        
        # Create geometry
        geometry = [Point(lon, lat) for lon, lat in zip(markets['lon'], markets['lat'])]
        
        # Create GeoDataFrame
        markets_gdf = gpd.GeoDataFrame(markets, geometry=geometry, crs='EPSG:4326')
        
        # Add market ID
        markets_gdf['market_id'] = (markets_gdf['governorate'] + '_' + 
                                    markets_gdf['market_name']).str.replace(' ', '_')
        
        log_data_shape("markets_with_coordinates", markets_gdf)
        info("Markets loaded", 
             count=len(markets_gdf),
             governorates=markets_gdf['governorate'].nunique())
        
        return markets_gdf
    
    def load_control_zones(self, control_data_path: Optional[Path] = None) -> gpd.GeoDataFrame:
        """Load control zone boundaries from ACAPS data.
        
        Args:
            control_data_path: Path to control zone data
            
        Returns:
            GeoDataFrame with control zone polygons
        """
        if control_data_path is None:
            # Look for processed control zone data
            control_files = list(self.control_dir.glob("*control_zones_raw*.parquet"))
            if not control_files:
                control_files = list(self.control_dir.glob("*control_zones_raw*.csv"))
            
            if not control_files:
                raise FileNotFoundError(f"No control zone data found in {self.control_dir}")
            
            control_data_path = sorted(control_files)[-1]
        
        info("Loading control zones", path=str(control_data_path))
        
        # Load data
        if control_data_path.suffix == '.parquet':
            df = pd.read_parquet(control_data_path)
        else:
            df = pd.read_csv(control_data_path)
        
        # If geometry column exists, handle different formats
        if 'geometry' in df.columns:
            # Check first geometry value to determine format
            first_geom = df['geometry'].iloc[0]
            
            if isinstance(first_geom, str):
                # WKT format
                from shapely import wkt
                df['geometry'] = df['geometry'].apply(wkt.loads)
            elif isinstance(first_geom, bytes):
                # WKB format
                from shapely import wkb
                df['geometry'] = df['geometry'].apply(wkb.loads)
            # else: assume already shapely geometry objects
            
            # Create GeoDataFrame with the loaded geometries
            gdf = gpd.GeoDataFrame(df, geometry='geometry', crs='EPSG:4326')
        else:
            # If no geometry, we need to load from shapefile
            warning("No geometry found in processed data, need shapefile")
            return gpd.GeoDataFrame()
        
        # Ensure we have valid geometries
        gdf = gdf[gdf.geometry.notna()]
        
        log_data_shape("control_zones", gdf)
        info("Control zones loaded", districts=len(gdf))
        
        return gdf
    
    def perform_spatial_join(self, markets: gpd.GeoDataFrame, 
                           control_zones: gpd.GeoDataFrame,
                           date: Optional[pd.Timestamp] = None) -> gpd.GeoDataFrame:
        """Join markets to control zones for a specific date.
        
        Args:
            markets: GeoDataFrame of market points
            control_zones: GeoDataFrame of control zone polygons
            date: Date for temporal filtering
            
        Returns:
            Markets with assigned control zones
        """
        with timer("spatial_join"):
            # Filter control zones by date if provided
            if date and 'date' in control_zones.columns:
                zones = control_zones[control_zones['date'] == date].copy()
                info("Filtered control zones", count=len(zones), date=str(date))
            else:
                zones = control_zones.copy()
            
            # Ensure CRS match
            if markets.crs != zones.crs:
                zones = zones.to_crs(markets.crs)
            
            # Perform spatial join
            joined = gpd.sjoin(markets, zones, how='left', predicate='within')
            
            # Remove duplicates (keep first match for each market)
            # Sort by some criteria to ensure consistent results
            joined = joined.sort_values(['market_id', 'index_right'])
            joined = joined.drop_duplicates(subset='market_id', keep='first')
            
            # Ensure it remains a GeoDataFrame
            joined = gpd.GeoDataFrame(joined, geometry='geometry', crs=markets.crs)
        
        # Count successful joins
        n_matched = joined['control_zone'].notna().sum()
        info("Spatial join results", 
             matched=n_matched, 
             total=len(markets),
             match_rate=f"{n_matched/len(markets)*100:.1f}%")
        
        # For unmatched markets, find nearest control zone
        unmatched = joined[joined['control_zone'].isna()]
        if len(unmatched) > 0:
            info("Finding nearest zones", unmatched_count=len(unmatched))
            
            # Get centroids of control zones
            zone_centroids = zones.copy()
            zone_centroids['geometry'] = zone_centroids.centroid
            
            # Find nearest zone for each unmatched market
            for idx in unmatched.index:
                market_point = joined.loc[idx, 'geometry']
                
                # Calculate distances
                distances = zone_centroids.distance(market_point)
                nearest_idx = distances.idxmin()
                
                # Assign nearest zone info
                nearest_zone = zones.loc[nearest_idx]
                joined.loc[idx, 'control_zone'] = nearest_zone['control_zone']
                joined.loc[idx, 'governorate_right'] = nearest_zone['governorate']
                joined.loc[idx, 'district_right'] = nearest_zone['district']
                joined.loc[idx, 'distance_to_zone_km'] = (
                    self._haversine_distance(
                        market_point.y, market_point.x,
                        zone_centroids.loc[nearest_idx, 'geometry'].y,
                        zone_centroids.loc[nearest_idx, 'geometry'].x
                    )
                )
        
        # Clean up columns - check which columns are available
        base_cols = ['market_id', 'market_name', 'lat', 'lon', 'control_zone', 'geometry']
        available_cols = []
        
        for col in base_cols:
            if col in joined.columns:
                available_cols.append(col)
        
        # Add governorate/district columns if they exist
        if 'governorate_left' in joined.columns:
            available_cols.insert(1, 'governorate_left')
        elif 'governorate' in joined.columns:
            available_cols.insert(1, 'governorate')
            
        if 'district_left' in joined.columns:
            available_cols.insert(2, 'district_left')
        elif 'district' in joined.columns:
            available_cols.insert(2, 'district')
            
        if 'governorate_right' in joined.columns:
            available_cols.append('governorate_right')
        if 'district_right' in joined.columns:
            available_cols.append('district_right')
        
        result = joined[available_cols].copy()
        
        # Rename columns consistently
        rename_map = {
            'governorate_left': 'market_governorate',
            'governorate': 'market_governorate',
            'district_left': 'market_district',
            'district': 'market_district',
            'governorate_right': 'zone_governorate',
            'district_right': 'zone_district'
        }
        
        result.rename(columns=rename_map, inplace=True)
        
        # Add distance to zone if calculated
        if 'distance_to_zone_km' in joined.columns:
            result['distance_to_zone_km'] = joined['distance_to_zone_km']
        else:
            result['distance_to_zone_km'] = 0.0
        
        return result
    
    def identify_boundary_markets(self, market_zones: gpd.GeoDataFrame,
                                control_zones: gpd.GeoDataFrame) -> pd.DataFrame:
        """Identify markets near control zone boundaries.
        
        Args:
            market_zones: Markets with assigned zones
            control_zones: Control zone boundaries
            
        Returns:
            DataFrame with boundary market information
        """
        with timer("identify_boundary_markets"):
            info("Identifying boundary markets", buffer_km=self.buffer_distance_km)
        
        # Create buffer around markets
        # Convert to projected CRS for accurate distance calculations
        # First ensure market_zones has CRS set
        if market_zones.crs is None:
            market_zones = market_zones.set_crs('EPSG:4326')
        markets_proj = market_zones.to_crs('EPSG:32638')  # UTM Zone 38N for Yemen
        buffer_meters = self.buffer_distance_km * 1000
        
        # Create buffers
        markets_proj['buffer'] = markets_proj.geometry.buffer(buffer_meters)
        
        boundary_markets = []
        
        with progress("Checking market boundaries", total=len(markets_proj)) as update:
            for idx, market in markets_proj.iterrows():
                # Get the market's control zone
                market_zone = market_zones.loc[idx, 'control_zone']
                
                # Find zones that intersect with buffer
                zones_proj = control_zones.to_crs('EPSG:32638')
                intersecting = zones_proj[zones_proj.geometry.intersects(market['buffer'])]
                
                # Get unique control zones in buffer
                nearby_zones = intersecting['control_zone'].unique()
                other_zones = [z for z in nearby_zones if z != market_zone]
                
                if other_zones:
                    # This is a boundary market
                    boundary_info = {
                        'market_id': market_zones.loc[idx, 'market_id'],
                        'market_name': market_zones.loc[idx, 'market_name'],
                        'primary_zone': market_zone,
                        'nearby_zones': ','.join(other_zones),
                        'n_nearby_zones': len(other_zones),
                        'is_boundary': True
                    }
                    boundary_markets.append(boundary_info)
                update(1)
        
        boundary_df = pd.DataFrame(boundary_markets)
        
        if not boundary_df.empty:
            info("Boundary markets identified", count=len(boundary_df))
        else:
            info("No boundary markets identified")
            # Create empty DataFrame with expected columns
            boundary_df = pd.DataFrame(columns=['market_id', 'market_name', 
                                               'primary_zone', 'nearby_zones',
                                               'n_nearby_zones', 'is_boundary'])
        
        return boundary_df
    
    def calculate_zone_distances(self, markets: gpd.GeoDataFrame,
                               control_zones: gpd.GeoDataFrame) -> pd.DataFrame:
        """Calculate distances from each market to all control zone centroids.
        
        Args:
            markets: Market locations
            control_zones: Control zone boundaries
            
        Returns:
            DataFrame with market-to-zone distances
        """
        with timer("calculate_zone_distances"):
            info("Calculating market-to-zone distances")
        
        # Get unique zones with their centroids
        zones_by_control = control_zones.groupby('control_zone').first()
        zone_centroids = zones_by_control.copy()
        zone_centroids['centroid'] = zone_centroids.geometry.centroid
        
        # Calculate distance matrix
        distances = []
        
        for _, market in markets.iterrows():
            for zone_type, zone_data in zone_centroids.iterrows():
                dist_km = self._haversine_distance(
                    market.geometry.y, market.geometry.x,
                    zone_data['centroid'].y, zone_data['centroid'].x
                )
                
                distances.append({
                    'market_id': market['market_id'],
                    'control_zone': zone_type,
                    'distance_km': dist_km
                })
        
        distance_df = pd.DataFrame(distances)
        
        # Pivot to wide format
        distance_matrix = distance_df.pivot(
            index='market_id',
            columns='control_zone',
            values='distance_km'
        )
        
        info("Distance calculation complete",
             markets=len(markets),
             zones=len(zone_centroids))
        
        return distance_matrix
    
    def create_temporal_mapping(self, markets: gpd.GeoDataFrame,
                              control_zones: gpd.GeoDataFrame) -> pd.DataFrame:
        """Create market-zone mapping over time.
        
        Args:
            markets: Market locations
            control_zones: Control zones with temporal data
            
        Returns:
            Time series of market-zone assignments
        """
        with timer("create_temporal_mapping"):
            info("Creating temporal market-zone mapping")
            
            # Get unique dates from control zones
            if 'date' not in control_zones.columns:
                warning("No date column in control zones, creating single mapping")
                # Single time point
                mapping = self.perform_spatial_join(markets, control_zones)
                mapping['date'] = pd.Timestamp.now()
                return mapping
        
        dates = sorted(control_zones['date'].unique())
        info("Processing temporal data", time_periods=len(dates))
        
        # Process each date
        temporal_mappings = []
        
        with progress("Processing time periods", total=len(dates)) as update:
            for date in dates:
                # Get control zones for this date
                zones_date = control_zones[control_zones['date'] == date]
                
                # Perform spatial join
                mapping = self.perform_spatial_join(markets, zones_date, date)
                mapping['date'] = date
                
                temporal_mappings.append(mapping)
                update(1, f"Processed {date}")
        
        # Combine all mappings
        full_mapping = pd.concat(temporal_mappings, ignore_index=True)
        
        # Sort by market and date
        full_mapping = full_mapping.sort_values(['market_id', 'date'])
        
        # Calculate control changes
        full_mapping['previous_zone'] = full_mapping.groupby('market_id')['control_zone'].shift(1)
        full_mapping['zone_changed'] = (full_mapping['control_zone'] != 
                                       full_mapping['previous_zone'])
        
        log_data_shape("temporal_mapping", full_mapping)
        info("Temporal mapping complete",
             records=len(full_mapping),
             zone_changes=full_mapping['zone_changed'].sum() if 'zone_changed' in full_mapping.columns else 0)
        
        return full_mapping
    
    def _haversine_distance(self, lat1: float, lon1: float, 
                           lat2: float, lon2: float) -> float:
        """Calculate distance between two points using Haversine formula.
        
        Args:
            lat1, lon1: First point coordinates
            lat2, lon2: Second point coordinates
            
        Returns:
            Distance in kilometers
        """
        # Convert to radians
        lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = np.sin(dlat/2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2)**2
        c = 2 * np.arcsin(np.sqrt(a))
        
        return self.EARTH_RADIUS_KM * c
    
    def calculate_spatial_weights(self, distance_matrix: pd.DataFrame,
                                 cutoff_km: float = 200.0,
                                 control_penalty: float = 0.1) -> pd.DataFrame:
        """Calculate spatial weights for market connectivity.
        
        Args:
            distance_matrix: Market-to-market distances
            cutoff_km: Maximum distance for connectivity
            control_penalty: Penalty for cross-control trade
            
        Returns:
            Spatial weight matrix
        """
        info("Calculating spatial weights", cutoff_km=cutoff_km, penalty=control_penalty)
        
        # This would require market-to-market distances
        # For now, return a placeholder
        # Full implementation would calculate inverse distance weights
        
        return distance_matrix
    
    def save_mappings(self, market_zones: gpd.GeoDataFrame,
                     temporal_mapping: pd.DataFrame,
                     boundary_markets: pd.DataFrame,
                     distance_matrix: pd.DataFrame) -> Dict[str, Path]:
        """Save all spatial mapping outputs.
        
        Args:
            market_zones: Current market-zone assignments
            temporal_mapping: Time series of assignments
            boundary_markets: Boundary market information
            distance_matrix: Market-to-zone distances
            
        Returns:
            Dictionary of saved file paths
        """
        saved_files = {}
        
        # Save current mapping
        if not market_zones.empty:
            # Save as GeoJSON for GIS compatibility
            geojson_path = self.output_dir / "market_zones_current.geojson"
            market_zones.to_file(geojson_path, driver='GeoJSON')
            saved_files['current_geojson'] = geojson_path
            
            # Save as Parquet (without geometry for analysis)
            parquet_path = self.output_dir / "market_zones_current.parquet"
            market_zones.drop('geometry', axis=1).to_parquet(parquet_path)
            saved_files['current_parquet'] = parquet_path
            
            info("Saved current mapping", path=str(parquet_path))
        
        # Save temporal mapping
        if not temporal_mapping.empty:
            temporal_path = self.output_dir / "market_zones_temporal.parquet"
            # Drop geometry column if it exists
            if 'geometry' in temporal_mapping.columns:
                temporal_mapping = temporal_mapping.drop('geometry', axis=1)
            temporal_mapping.to_parquet(temporal_path)
            saved_files['temporal'] = temporal_path
            
            # Also save as CSV
            csv_path = self.output_dir / "market_zones_temporal.csv"
            temporal_mapping.to_csv(csv_path, index=False)
            saved_files['temporal_csv'] = csv_path
            
            info("Saved temporal mapping", path=str(temporal_path), records=len(temporal_mapping))
        
        # Save boundary markets
        if not boundary_markets.empty:
            boundary_path = self.output_dir / "boundary_markets.csv"
            boundary_markets.to_csv(boundary_path, index=False)
            saved_files['boundary'] = boundary_path
            info("Saved boundary markets", path=str(boundary_path), count=len(boundary_markets))
        
        # Save distance matrix
        if not distance_matrix.empty:
            distance_path = self.output_dir / "market_zone_distances.parquet"
            distance_matrix.to_parquet(distance_path)
            saved_files['distances'] = distance_path
            info("Saved distance matrix", path=str(distance_path))
        
        # Save metadata
        metadata = {
            'created_date': datetime.now().isoformat(),
            'n_markets': len(market_zones),
            'n_boundary_markets': len(boundary_markets),
            'buffer_distance_km': self.buffer_distance_km,
            'control_zones': list(market_zones['control_zone'].unique()) if not market_zones.empty else [],
            'date_range': {
                'start': str(temporal_mapping['date'].min()) if not temporal_mapping.empty and 'date' in temporal_mapping.columns else None,
                'end': str(temporal_mapping['date'].max()) if not temporal_mapping.empty and 'date' in temporal_mapping.columns else None
            }
        }
        
        import json
        metadata_path = self.output_dir / "spatial_join_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        saved_files['metadata'] = metadata_path
        
        return saved_files
    
    def generate_summary_report(self, market_zones: gpd.GeoDataFrame,
                              temporal_mapping: pd.DataFrame) -> pd.DataFrame:
        """Generate summary statistics for spatial joins.
        
        Args:
            market_zones: Current market-zone mapping
            temporal_mapping: Historical mappings
            
        Returns:
            Summary statistics DataFrame
        """
        summary_stats = []
        
        # Markets per control zone
        if not market_zones.empty:
            zone_counts = market_zones['control_zone'].value_counts()
            for zone, count in zone_counts.items():
                summary_stats.append({
                    'metric': f'markets_in_{zone}',
                    'value': count
                })
        
        # Temporal statistics
        if not temporal_mapping.empty and 'zone_changed' in temporal_mapping.columns:
            n_changes = temporal_mapping['zone_changed'].sum()
            summary_stats.append({
                'metric': 'total_zone_changes',
                'value': n_changes
            })
            
            # Markets that changed zones
            markets_changed = temporal_mapping[temporal_mapping['zone_changed']]['market_id'].nunique()
            summary_stats.append({
                'metric': 'markets_that_changed_zones',
                'value': markets_changed
            })
        
        summary_df = pd.DataFrame(summary_stats)
        info("Generated spatial join summary statistics", metrics=len(summary_stats))
        
        return summary_df