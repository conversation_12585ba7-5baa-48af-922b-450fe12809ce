"""Model diagnostic visualization tools.

This module provides visualization functions for econometric model diagnostics,
including residual plots, QQ plots, and diagnostic test summaries.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Optional, List, Tuple, Any
from pathlib import Path
import statsmodels.api as sm
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from scipy import stats

from ..utils.logging import bind, timer, info, warning
from ..models.base import BaseEconometricModel


def plot_diagnostic_suite(model: BaseEconometricModel, 
                         save_path: Optional[Path] = None) -> None:
    """Generate comprehensive diagnostic plots for a fitted model.
    
    Creates a suite of diagnostic plots including:
    - Residual time series
    - QQ plot for normality
    - ACF/PACF of residuals
    - Residuals vs fitted values
    
    Args:
        model: Fitted econometric model
        save_path: Optional path to save the figure
    """
    with timer("plot_diagnostics"):
        info(f"Creating diagnostic plots for {model.name}")
        
        # Get residuals
        try:
            residuals = model.get_residuals()
        except Exception as e:
            warning(f"Could not get residuals: {e}")
            return
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()
        
        # 1. Residual time series
        ax = axes[0]
        if isinstance(residuals.index, pd.MultiIndex):
            # For panel data, plot mean residuals over time
            if 'date' in residuals.index.names:
                mean_residuals = residuals.groupby('date').mean()
                ax.plot(mean_residuals.index, mean_residuals.values)
                ax.axhline(y=0, color='r', linestyle='--', alpha=0.5)
                ax.set_title('Mean Residuals Over Time')
            else:
                ax.plot(residuals.values)
                ax.axhline(y=0, color='r', linestyle='--', alpha=0.5)
                ax.set_title('Residuals')
        else:
            ax.plot(residuals.index, residuals.values)
            ax.axhline(y=0, color='r', linestyle='--', alpha=0.5)
            ax.set_title('Residual Time Series')
        ax.set_xlabel('Time')
        ax.set_ylabel('Residual')
        ax.grid(True, alpha=0.3)
        
        # 2. QQ plot for normality
        ax = axes[1]
        stats.probplot(residuals.values.flatten(), dist="norm", plot=ax)
        ax.set_title('Normal Q-Q Plot')
        ax.grid(True, alpha=0.3)
        
        # 3. Histogram of residuals
        ax = axes[2]
        ax.hist(residuals.values.flatten(), bins=30, density=True, alpha=0.7, edgecolor='black')
        
        # Overlay normal distribution
        mu, sigma = np.mean(residuals), np.std(residuals)
        x = np.linspace(mu - 4*sigma, mu + 4*sigma, 100)
        ax.plot(x, stats.norm.pdf(x, mu, sigma), 'r-', lw=2, label='Normal')
        ax.set_title('Residual Distribution')
        ax.set_xlabel('Residual')
        ax.set_ylabel('Density')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 4. ACF of residuals
        ax = axes[3]
        try:
            # Flatten residuals if multi-dimensional
            res_flat = residuals.values.flatten()
            # Remove NaN values
            res_flat = res_flat[~np.isnan(res_flat)]
            
            plot_acf(res_flat, lags=20, ax=ax, alpha=0.05)
            ax.set_title('Residual Autocorrelation')
            ax.set_xlabel('Lag')
            ax.set_ylabel('ACF')
            ax.grid(True, alpha=0.3)
        except Exception as e:
            ax.text(0.5, 0.5, f'ACF failed:\n{str(e)}', 
                   transform=ax.transAxes, ha='center')
            ax.set_title('Residual ACF (Failed)')
        
        # 5. PACF of residuals
        ax = axes[4]
        try:
            plot_pacf(res_flat, lags=20, ax=ax, alpha=0.05)
            ax.set_title('Residual Partial Autocorrelation')
            ax.set_xlabel('Lag')
            ax.set_ylabel('PACF')
            ax.grid(True, alpha=0.3)
        except Exception as e:
            ax.text(0.5, 0.5, f'PACF failed:\n{str(e)}', 
                   transform=ax.transAxes, ha='center')
            ax.set_title('Residual PACF (Failed)')
        
        # 6. Residuals vs fitted (or just squared residuals)
        ax = axes[5]
        squared_residuals = residuals ** 2
        if isinstance(squared_residuals.index, pd.MultiIndex) and 'date' in squared_residuals.index.names:
            mean_sq_res = squared_residuals.groupby('date').mean()
            ax.plot(mean_sq_res.index, mean_sq_res.values)
            ax.set_title('Squared Residuals Over Time')
            ax.set_xlabel('Time')
        else:
            ax.plot(squared_residuals.values)
            ax.set_title('Squared Residuals')
            ax.set_xlabel('Observation')
        ax.set_ylabel('Squared Residual')
        ax.grid(True, alpha=0.3)
        
        # Add main title
        plt.suptitle(f'Diagnostic Plots - {model.name}', fontsize=16)
        plt.tight_layout()
        
        # Save or show
        if save_path:
            save_path = Path(save_path)
            save_path.parent.mkdir(parents=True, exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            info(f"Saved diagnostic plots to {save_path}")
        else:
            plt.show()
        
        plt.close()


def plot_residual_analysis(residuals: pd.Series, 
                          model_name: str = "Model",
                          save_path: Optional[Path] = None) -> None:
    """Detailed residual analysis plots.
    
    Args:
        residuals: Model residuals
        model_name: Name of the model
        save_path: Optional save path
    """
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    axes = axes.flatten()
    
    # 1. Residuals over time with rolling statistics
    ax = axes[0]
    if isinstance(residuals.index, pd.MultiIndex):
        # Handle panel data
        if 'date' in residuals.index.names:
            mean_res = residuals.groupby('date').mean()
            std_res = residuals.groupby('date').std()
            
            ax.plot(mean_res.index, mean_res.values, label='Mean')
            ax.fill_between(mean_res.index, 
                          mean_res - 2*std_res, 
                          mean_res + 2*std_res,
                          alpha=0.2, label='±2 SD')
        else:
            ax.plot(residuals.values)
    else:
        ax.plot(residuals.index, residuals.values, alpha=0.7)
        
        # Add rolling mean and std
        if len(residuals) > 20:
            rolling_mean = residuals.rolling(window=20).mean()
            rolling_std = residuals.rolling(window=20).std()
            ax.plot(residuals.index, rolling_mean, 'r-', label='Rolling mean')
            ax.fill_between(residuals.index,
                          rolling_mean - 2*rolling_std,
                          rolling_mean + 2*rolling_std,
                          alpha=0.2, color='red', label='Rolling ±2 SD')
    
    ax.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax.set_title('Residuals with Rolling Statistics')
    ax.set_xlabel('Time/Observation')
    ax.set_ylabel('Residual')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 2. Residual density with multiple distributions
    ax = axes[1]
    res_clean = residuals.dropna().values
    
    ax.hist(res_clean, bins=30, density=True, alpha=0.6, label='Empirical')
    
    # Fit and plot normal
    mu, sigma = stats.norm.fit(res_clean)
    x = np.linspace(res_clean.min(), res_clean.max(), 100)
    ax.plot(x, stats.norm.pdf(x, mu, sigma), 'r-', label='Normal', lw=2)
    
    # Fit and plot Student's t
    df, loc, scale = stats.t.fit(res_clean)
    ax.plot(x, stats.t.pdf(x, df, loc, scale), 'g--', label=f"Student's t (df={df:.1f})", lw=2)
    
    ax.set_title('Residual Distribution')
    ax.set_xlabel('Residual')
    ax.set_ylabel('Density')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 3. Box plot by period (if time series)
    ax = axes[2]
    if isinstance(residuals.index, pd.MultiIndex) and 'date' in residuals.index.names:
        # Group by month/quarter
        res_df = residuals.reset_index()
        res_df['month'] = pd.to_datetime(res_df['date']).dt.to_period('M')
        
        # Take last 12 months
        unique_months = res_df['month'].unique()[-12:]
        recent_data = res_df[res_df['month'].isin(unique_months)]
        
        recent_data.boxplot(column=residuals.name or 0, by='month', ax=ax)
        ax.set_title('Residuals by Month (Last Year)')
        ax.set_xlabel('Month')
        ax.set_ylabel('Residual')
        plt.xticks(rotation=45)
    else:
        # Simple box plot
        ax.boxplot(res_clean)
        ax.set_title('Residual Box Plot')
        ax.set_ylabel('Residual')
    
    # 4. Ljung-Box test p-values
    ax = axes[3]
    try:
        # Calculate Ljung-Box test for different lags
        max_lag = min(40, len(res_clean) // 5)
        lags = range(1, max_lag + 1)
        p_values = []
        
        for lag in lags:
            lb_result = sm.stats.acorr_ljungbox(res_clean, lags=[lag], return_df=False)
            p_values.append(lb_result[1][0])
        
        ax.plot(lags, p_values, 'bo-')
        ax.axhline(y=0.05, color='r', linestyle='--', label='5% significance')
        ax.set_title('Ljung-Box Test P-values')
        ax.set_xlabel('Lag')
        ax.set_ylabel('P-value')
        ax.set_ylim(-0.05, 1.05)
        ax.legend()
        ax.grid(True, alpha=0.3)
    except Exception as e:
        ax.text(0.5, 0.5, f'Ljung-Box test failed:\n{str(e)}', 
               transform=ax.transAxes, ha='center')
    
    plt.suptitle(f'Residual Analysis - {model_name}', fontsize=14)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        info(f"Saved residual analysis to {save_path}")
    else:
        plt.show()
    
    plt.close()


def plot_threshold_search(threshold_values: np.ndarray,
                         ssr_values: np.ndarray,
                         optimal_threshold: float,
                         confidence_interval: Tuple[float, float],
                         save_path: Optional[Path] = None) -> None:
    """Plot threshold search results.
    
    Args:
        threshold_values: Grid of threshold values tested
        ssr_values: Sum of squared residuals for each threshold
        optimal_threshold: Estimated optimal threshold
        confidence_interval: CI for threshold
        save_path: Optional save path
    """
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # Plot SSR profile
    ax.plot(threshold_values, ssr_values, 'b-', linewidth=2, label='SSR')
    
    # Mark optimal threshold
    ax.axvline(x=optimal_threshold, color='r', linestyle='--', 
              linewidth=2, label=f'Optimal ({optimal_threshold:.1f})')
    
    # Shade confidence interval
    ax.axvspan(confidence_interval[0], confidence_interval[1], 
              alpha=0.2, color='red', label='95% CI')
    
    # Find and mark minimum
    min_idx = np.argmin(ssr_values)
    ax.plot(threshold_values[min_idx], ssr_values[min_idx], 'ro', 
           markersize=10, label='Minimum SSR')
    
    ax.set_xlabel('Threshold Value', fontsize=12)
    ax.set_ylabel('Sum of Squared Residuals', fontsize=12)
    ax.set_title('Threshold Search Profile', fontsize=14)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Add text box with statistics
    textstr = f'Optimal: {optimal_threshold:.2f}\n'
    textstr += f'95% CI: [{confidence_interval[0]:.2f}, {confidence_interval[1]:.2f}]\n'
    textstr += f'SSR reduction: {(1 - ssr_values[min_idx]/ssr_values[0])*100:.1f}%'
    
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
    ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', bbox=props)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        info(f"Saved threshold search plot to {save_path}")
    else:
        plt.show()
    
    plt.close()


def plot_parameter_evolution(param_paths: dict,
                           dates: pd.DatetimeIndex,
                           param_name: str = "Parameter",
                           save_path: Optional[Path] = None) -> None:
    """Plot evolution of time-varying parameters.
    
    Args:
        param_paths: Dictionary of parameter paths by market/variable
        dates: Time index
        param_name: Name of parameter for title
        save_path: Optional save path
    """
    n_series = len(param_paths)
    n_cols = min(3, n_series)
    n_rows = (n_series + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(5*n_cols, 4*n_rows))
    
    if n_series == 1:
        axes = [axes]
    else:
        axes = axes.flatten()
    
    for idx, (name, path) in enumerate(param_paths.items()):
        ax = axes[idx]
        
        # Plot path
        ax.plot(dates, path, 'b-', linewidth=2)
        
        # Add confidence bands if available
        if hasattr(path, 'shape') and len(path.shape) > 1:
            # Assume columns are [mean, lower, upper]
            ax.fill_between(dates, path[:, 1], path[:, 2], 
                          alpha=0.3, color='blue')
        
        # Add zero line
        ax.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        
        ax.set_title(f'{name}')
        ax.set_xlabel('Date')
        ax.set_ylabel(param_name)
        ax.grid(True, alpha=0.3)
        
        # Rotate x labels
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    # Remove empty subplots
    for idx in range(n_series, len(axes)):
        fig.delaxes(axes[idx])
    
    plt.suptitle(f'{param_name} Evolution Over Time', fontsize=14)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        info(f"Saved parameter evolution plot to {save_path}")
    else:
        plt.show()
    
    plt.close()