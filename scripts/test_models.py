#!/usr/bin/env python3
"""Test script for Week 5-6 econometric models.

This script validates the dual-track model implementations:
- Track 1: Bayesian TVP-VECM
- Track 2: Simple Threshold VECM

Run from project root:
    python scripts/test_models.py
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

import pandas as pd
import numpy as np
from yemen_market.utils.logging import info, warning, error, timer, bind
from yemen_market.models.track1_complex.tvp_vecm import BayesianTVPVECM
from yemen_market.models.track2_simple.threshold_vecm import SimpleThresholdVECM
from yemen_market.diagnostics.test_battery import DiagnosticTestBattery


def load_sample_data():
    """Load a small sample of data for testing."""
    info("Loading sample data for model testing")
    
    # Try to load actual data
    data_path = Path("data/processed/integrated_panel_monthly.parquet")
    
    if data_path.exists():
        # Load real data
        data = pd.read_parquet(data_path)
        
        # Filter to a few markets and recent period for testing
        markets_to_test = ['Sana\'a', 'Aden', 'Taiz']
        data_subset = data[
            (data['market_name'].isin(markets_to_test)) &
            (data['date'] >= '2022-01-01') &
            (data['commodity'] == 'Wheat')
        ].copy()
        
        if len(data_subset) < 100:
            warning("Insufficient real data, using synthetic data")
            return create_synthetic_data()
            
        info(f"Loaded {len(data_subset)} observations for {len(markets_to_test)} markets")
        return data_subset
    else:
        warning("No processed data found, creating synthetic data")
        return create_synthetic_data()


def create_synthetic_data():
    """Create synthetic data for testing."""
    np.random.seed(42)
    
    # Create dates
    dates = pd.date_range('2022-01-01', '2024-01-01', freq='M')
    markets = ['Market_A', 'Market_B', 'Market_C']
    
    # Create cointegrated price series
    n_periods = len(dates)
    n_markets = len(markets)
    
    # Common stochastic trend
    trend = np.cumsum(np.random.randn(n_periods) * 0.05)
    
    # Market-specific components
    data_list = []
    for i, market in enumerate(markets):
        # Price = trend + stationary component
        price = np.exp(5 + trend + np.random.randn(n_periods) * 0.1)
        
        # Conflict intensity (affects variance)
        conflict = np.maximum(0, 30 + np.random.randn(n_periods) * 20)
        
        # Add threshold effect
        high_conflict = conflict > 50
        price[high_conflict] *= (1 + np.random.rand(np.sum(high_conflict)) * 0.1)
        
        market_data = pd.DataFrame({
            'date': dates,
            'market_name': market,
            'price_usd': price,
            'conflict_intensity': conflict,
            'commodity': 'Wheat'
        })
        
        data_list.append(market_data)
    
    data = pd.concat(data_list, ignore_index=True)
    info(f"Created synthetic data: {len(data)} observations")
    
    return data


def test_threshold_vecm(data):
    """Test Track 2: Simple Threshold VECM."""
    info("\n" + "="*60)
    info("Testing Track 2: Simple Threshold VECM")
    info("="*60)
    
    with timer("threshold_vecm_test"):
        # Initialize model
        model = SimpleThresholdVECM(
            threshold=50.0,
            threshold_variable='conflict_intensity',
            n_coint=1,
            n_lags=2
        )
        
        # Fit model
        try:
            model.fit(data, estimate_threshold=True)
            info("✓ Model fitted successfully")
            
            # Check results
            results = model.vecm_results
            info(f"Threshold: {results.threshold_value:.2f}")
            info(f"Low regime: {results.n_obs_low} obs, High regime: {results.n_obs_high} obs")
            info(f"Threshold test p-value: {results.threshold_p_value:.4f}")
            
            # Generate predictions
            predictions = model.predict(steps=3)
            info(f"✓ Generated {len(predictions)} step predictions")
            
            # Analyze regime dynamics
            regime_analysis = model.analyze_regime_dynamics()
            info(f"Expected low regime duration: {regime_analysis['expected_low_duration']:.1f} months")
            
            # Run diagnostics
            battery = DiagnosticTestBattery(model)
            diagnostics = battery.run_all_tests(include_slow=False)
            info("✓ Diagnostic tests completed")
            
            return True
            
        except Exception as e:
            error(f"Threshold VECM test failed: {e}")
            return False


def test_bayesian_tvp_vecm(data):
    """Test Track 1: Bayesian TVP-VECM."""
    info("\n" + "="*60)
    info("Testing Track 1: Bayesian TVP-VECM")
    info("="*60)
    
    # Note: This is computationally intensive, so we use minimal settings
    with timer("bayesian_tvp_vecm_test"):
        # Initialize model with minimal sampling
        model = BayesianTVPVECM(
            n_coint=1,
            n_lags=2,
            n_samples=100,  # Very small for testing
            n_chains=2,
            random_seed=42
        )
        
        # Check if PyMC is available
        try:
            import pymc
            info("PyMC is available, testing Bayesian model")
        except ImportError:
            warning("PyMC not installed, skipping Bayesian model test")
            return True
        
        # Fit model
        try:
            model.fit(data, conflict_col='conflict_intensity')
            info("✓ Model fitted successfully")
            
            # Check convergence
            results = model.vecm_results
            info(f"Model converged: {results.converged}")
            
            # Analyze conflict impact
            conflict_analysis = model.analyze_conflict_impact()
            info(f"Conflict effect significant: {conflict_analysis['significant']}")
            
            # Generate predictions
            predictions = model.predict(steps=3, include_uncertainty=False)
            info(f"✓ Generated {len(predictions)} step predictions")
            
            return True
            
        except Exception as e:
            error(f"Bayesian TVP-VECM test failed: {e}")
            return False


def compare_models():
    """Compare both modeling approaches."""
    info("\n" + "="*60)
    info("Model Comparison Summary")
    info("="*60)
    
    comparison = {
        'Track 1 (Bayesian TVP-VECM)': {
            'Strengths': [
                'Time-varying parameters',
                'Uncertainty quantification',
                'Flexible conflict modeling'
            ],
            'Weaknesses': [
                'Computationally intensive',
                'Complex interpretation',
                'Convergence challenges'
            ]
        },
        'Track 2 (Simple Threshold VECM)': {
            'Strengths': [
                'Fast estimation',
                'Clear interpretation',
                'Robust results'
            ],
            'Weaknesses': [
                'Fixed threshold',
                'No parameter evolution',
                'Limited uncertainty measures'
            ]
        }
    }
    
    for track, details in comparison.items():
        info(f"\n{track}:")
        info("  Strengths:")
        for s in details['Strengths']:
            info(f"    + {s}")
        info("  Weaknesses:")
        for w in details['Weaknesses']:
            info(f"    - {w}")


def main():
    """Run all model tests."""
    bind(script="test_models")
    
    info("Starting Week 5-6 Model Testing")
    info(f"Testing dual-track econometric models\n")
    
    # Load data
    data = load_sample_data()
    
    # Test both tracks
    threshold_success = test_threshold_vecm(data)
    bayesian_success = test_bayesian_tvp_vecm(data)
    
    # Compare approaches
    compare_models()
    
    # Summary
    info("\n" + "="*60)
    info("TEST SUMMARY")
    info("="*60)
    info(f"Track 2 (Threshold VECM): {'✓ PASSED' if threshold_success else '✗ FAILED'}")
    info(f"Track 1 (Bayesian TVP-VECM): {'✓ PASSED' if bayesian_success else '✗ FAILED'}")
    
    if threshold_success and bayesian_success:
        info("\nAll tests passed! Models are ready for full-scale estimation.")
    else:
        warning("\nSome tests failed. Review errors and fix before proceeding.")


if __name__ == "__main__":
    main()