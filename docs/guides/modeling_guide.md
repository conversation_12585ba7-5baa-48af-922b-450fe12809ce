# Econometric Modeling Guide

This guide walks through the complete process of estimating and interpreting econometric models for the Yemen market integration analysis.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Data Requirements](#data-requirements)
3. [Model Selection](#model-selection)
4. [Step-by-Step Implementation](#step-by-step-implementation)
5. [Interpreting Results](#interpreting-results)
6. [Troubleshooting](#troubleshooting)
7. [Best Practices](#best-practices)

## Quick Start

```bash
# Run the full modeling pipeline
python scripts/analysis/run_week5_models.py

# Test model implementations
python scripts/test_models.py
```

## Data Requirements

### Essential Columns

Your panel data must include:
- `date`: Period identifier (monthly)
- `market_name`: Market identifier
- `price_usd`: Price in USD (or local currency)
- `conflict_intensity`: Conflict events measure

### Optional but Recommended
- `latitude`, `longitude`: For spatial analysis
- `control_zone`: For regime analysis
- `exchange_rate`: For currency analysis
- Additional commodities for robustness

### Data Quality Checks

```python
import pandas as pd
from yemen_market.utils.logging import info, warning

def check_data_quality(data):
    """Perform essential data quality checks."""
    
    # Check for required columns
    required = ['date', 'market_name', 'price_usd', 'conflict_intensity']
    missing = [col for col in required if col not in data.columns]
    if missing:
        raise ValueError(f"Missing required columns: {missing}")
    
    # Check coverage
    markets = data['market_name'].nunique()
    periods = data['date'].nunique()
    expected = markets * periods
    actual = len(data)
    coverage = actual / expected * 100
    
    info(f"Data coverage: {coverage:.1f}% ({actual}/{expected} observations)")
    if coverage < 50:
        warning("Low data coverage may affect model reliability")
    
    # Check for price outliers
    price_stats = data['price_usd'].describe()
    if price_stats['max'] > 10 * price_stats['75%']:
        warning("Potential price outliers detected")
    
    return True
```

## Model Selection

### Decision Tree

```
Start
  |
  ├─ Research Focus?
  |    |
  |    ├─ Time-varying dynamics → Track 1 (Bayesian TVP-VECM)
  |    |
  |    └─ Regime identification → Track 2 (Threshold VECM)
  |
  ├─ Data Characteristics?
  |    |
  |    ├─ Long series (>100 periods) → Either track
  |    |
  |    └─ Short series (<50 periods) → Track 2 preferred
  |
  └─ Computational Resources?
       |
       ├─ High (GPU/multicore) → Track 1 feasible
       |
       └─ Limited → Track 2 recommended
```

## Step-by-Step Implementation

### Step 1: Load and Prepare Data

```python
import pandas as pd
from yemen_market.utils.logging import info, bind

# Set logging context
bind(analysis="model_estimation")

# Load processed data
data = pd.read_parquet('data/processed/smart_panel_monthly.parquet')

# Filter to commodity of interest
wheat_data = data[data['commodity'] == 'Wheat'].copy()

# Ensure proper date sorting
wheat_data = wheat_data.sort_values(['market_name', 'date'])

info(f"Loaded {len(wheat_data)} observations for {wheat_data['market_name'].nunique()} markets")
```

### Step 2: Exploratory Analysis

```python
import matplotlib.pyplot as plt
import seaborn as sns

# Price evolution by market
fig, ax = plt.subplots(figsize=(12, 6))
for market in wheat_data['market_name'].unique()[:5]:  # First 5 markets
    market_data = wheat_data[wheat_data['market_name'] == market]
    ax.plot(market_data['date'], market_data['price_usd'], label=market)

ax.set_xlabel('Date')
ax.set_ylabel('Price (USD)')
ax.set_title('Wheat Price Evolution by Market')
ax.legend()
plt.show()

# Conflict intensity distribution
fig, ax = plt.subplots(figsize=(10, 6))
ax.hist(wheat_data['conflict_intensity'], bins=50, edgecolor='black')
ax.axvline(50, color='red', linestyle='--', label='Potential threshold')
ax.set_xlabel('Conflict Intensity')
ax.set_ylabel('Frequency')
ax.set_title('Distribution of Conflict Intensity')
ax.legend()
plt.show()
```

### Step 3: Estimate Track 2 Model (Recommended First)

```python
from yemen_market.models.track2_simple.threshold_vecm import SimpleThresholdVECM
from yemen_market.diagnostics.test_battery import DiagnosticTestBattery

# Initialize model
model = SimpleThresholdVECM(
    threshold_variable='conflict_intensity',
    n_coint=1,  # Start with 1 cointegrating relationship
    n_lags=2,   # 2 lags is often sufficient for monthly data
    trim_pct=0.15,  # Ensure 15% observations in each regime
    n_boot=1000  # Bootstrap replications
)

# Fit model
model.fit(wheat_data, estimate_threshold=True)

# View key results
results = model.vecm_results
print(f"""
Threshold VECM Results
=====================
Estimated threshold: {results.threshold_value:.2f}
95% CI: [{results.threshold_ci_lower:.2f}, {results.threshold_ci_upper:.2f}]
P-value for threshold: {results.threshold_p_value:.4f}

Regime distribution:
- Low conflict: {results.n_obs_low} obs ({results.n_obs_low/results.n_obs*100:.1f}%)
- High conflict: {results.n_obs_high} obs ({results.n_obs_high/results.n_obs*100:.1f}%)
""")

# Run diagnostics
battery = DiagnosticTestBattery(model)
diagnostics = battery.run_all_tests(skip_categories=['robustness'])
battery.generate_report('diagnostics_threshold.md')
```

### Step 4: Estimate Track 1 Model (If Needed)

```python
from yemen_market.models.track1_complex.tvp_vecm import BayesianTVPVECM

# Check if PyMC is available
try:
    import pymc
    import arviz
    
    # Initialize Bayesian model
    bayesian_model = BayesianTVPVECM(
        n_coint=1,
        n_lags=2,
        n_samples=2000,  # Increase for final analysis
        n_chains=4,
        target_accept=0.8
    )
    
    # Fit model (this will take time)
    bayesian_model.fit(wheat_data)
    
    # Check convergence
    print(f"Converged: {bayesian_model.vecm_results.converged}")
    
    # Analyze conflict impact
    conflict_impact = bayesian_model.analyze_conflict_impact()
    print(f"""
    Conflict Impact Analysis
    =======================
    Sensitivity: {conflict_impact['conflict_sensitivity']:.4f}
    95% CI: [{conflict_impact['ci_lower']:.4f}, {conflict_impact['ci_upper']:.4f}]
    Significant: {conflict_impact['significant']}
    """)
    
except ImportError:
    print("PyMC not installed. Install with: pip install pymc arviz")
```

### Step 5: Compare Models

```python
from yemen_market.models.model_comparison import ModelComparison

# Compare both models
if 'bayesian_model' in locals():
    models = {
        'Threshold_VECM': model,
        'Bayesian_TVP': bayesian_model
    }
else:
    models = {'Threshold_VECM': model}

comparison = ModelComparison(models)

# Generate comparison table
summary = comparison.create_comparison_table()
print(summary)

# Assess consistency
consistency = comparison.assess_dual_track_consistency()
print(f"\nDual-track consistency: {consistency['consistent']}")
if consistency['discrepancies']:
    print("Discrepancies found:")
    for disc in consistency['discrepancies']:
        print(f"  - {disc}")
```

### Step 6: Generate Forecasts

```python
# Generate 6-month ahead forecasts
forecast_horizon = 6

# Threshold model forecasts
threshold_forecast = model.predict(steps=forecast_horizon)

# Plot forecasts
fig, ax = plt.subplots(figsize=(12, 6))

# Historical data (last 12 months)
historical = wheat_data[wheat_data['market_name'] == wheat_data['market_name'].iloc[0]].tail(12)
ax.plot(historical['date'], historical['price_usd'], 'b-', label='Historical')

# Forecast
forecast_dates = pd.date_range(
    start=historical['date'].iloc[-1] + pd.DateOffset(months=1),
    periods=forecast_horizon,
    freq='M'
)
ax.plot(forecast_dates, threshold_forecast.iloc[:, 0], 'r--', label='Forecast')

ax.set_xlabel('Date')
ax.set_ylabel('Price (USD)')
ax.set_title('6-Month Price Forecast')
ax.legend()
plt.show()
```

## Interpreting Results

### Key Metrics to Report

1. **Cointegration Test**
   - Number of cointegrating relationships
   - Trace/eigenvalue statistics
   - Interpretation: Markets are integrated if r > 0

2. **Adjustment Speeds (α)**
   - Sign: Negative = convergence, Positive = divergence
   - Magnitude: Larger |α| = faster adjustment
   - Significance: t-statistics > 2 indicate significance

3. **Threshold Effects**
   - Threshold value and confidence interval
   - Significance test p-value
   - Regime-specific parameters

4. **Diagnostic Tests**
   - Serial correlation: Should be absent (p > 0.05)
   - Heteroskedasticity: Prefer homoskedastic (p > 0.05)
   - Normality: Less critical but preferred (p > 0.05)

### Economic Interpretation

```python
# Example interpretation helper
def interpret_results(model):
    """Generate economic interpretation of results."""
    
    if hasattr(model, 'vecm_results'):
        results = model.vecm_results
        
        # Threshold interpretation
        if hasattr(results, 'threshold_value'):
            print(f"""
Market Integration Analysis
==========================
            
1. Critical Conflict Threshold: {results.threshold_value:.1f} events/month
   - Below threshold: Markets function relatively normally
   - Above threshold: Integration weakens significantly
            
2. Regime Dynamics:
   - Low conflict periods: {results.n_obs_low} months
   - High conflict periods: {results.n_obs_high} months
   - Persistence: Markets stay in high conflict regime for ~{1/(1-results.transition_probs[1,1]):.1f} months on average
            """)
        
        # Adjustment speed interpretation
        if hasattr(results, 'alpha'):
            avg_speed = np.mean(np.abs(results.alpha))
            half_life = np.log(2) / avg_speed
            print(f"""
3. Price Adjustment:
   - Average adjustment speed: {avg_speed:.3f}
   - Half-life of shocks: {half_life:.1f} months
   - Interpretation: Price differences persist for ~{half_life:.0f} months
            """)
```

## Troubleshooting

### Common Issues and Solutions

#### No Cointegration Found
```python
# Solutions:
# 1. Check data transformation
log_prices = np.log(data['price_usd'])  # Ensure log transformation

# 2. Try different lag lengths
for lags in [1, 2, 3, 4]:
    model = SimpleThresholdVECM(n_lags=lags)
    # Test cointegration

# 3. Check for structural breaks
# May need to split sample or add break dummies
```

#### Convergence Issues (Bayesian)
```python
# Solutions:
# 1. Increase samples and chains
model = BayesianTVPVECM(n_samples=5000, n_chains=6)

# 2. Adjust target acceptance
model = BayesianTVPVECM(target_accept=0.9)

# 3. Check data scaling
data['price_scaled'] = data['price_usd'] / 100
```

#### Poor Forecast Performance
```python
# Solutions:
# 1. Add more lags
model = SimpleThresholdVECM(n_lags=4)

# 2. Include exogenous variables
model.fit(data, exog_cols=['exchange_rate', 'fuel_price'])

# 3. Use regime-specific forecasts
forecast_low = model.predict(steps=6, regime='low')
forecast_high = model.predict(steps=6, regime='high')
```

## Best Practices

### 1. Data Preparation
- Always log-transform prices
- Check for outliers and handle appropriately
- Ensure balanced panel where possible
- Document any data transformations

### 2. Model Specification
- Start simple, add complexity gradually
- Test multiple lag lengths
- Validate cointegration assumption
- Consider seasonal adjustments

### 3. Robustness Checks
```python
# Test on different commodities
commodities = ['Wheat', 'Rice', 'Sugar']
results = {}

for commodity in commodities:
    data_subset = data[data['commodity'] == commodity]
    model = SimpleThresholdVECM()
    model.fit(data_subset)
    results[commodity] = model.vecm_results.threshold_value

# Compare thresholds across commodities
print("Threshold estimates by commodity:")
for commodity, threshold in results.items():
    print(f"  {commodity}: {threshold:.1f}")
```

### 4. Documentation
- Save all model outputs
- Document parameter choices
- Create reproducible scripts
- Generate comprehensive reports

### 5. Visualization
```python
# Create publication-quality figures
import matplotlib.pyplot as plt
plt.style.use('seaborn-v0_8-darkgrid')

fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

# Plot 1: Regime evolution
regime = model.vecm_results.regime_assignment
dates = wheat_data['date'].unique()[1:]
ax1.fill_between(dates, 0, 1, where=regime==1, alpha=0.3, color='red', label='High conflict')
ax1.fill_between(dates, 0, 1, where=regime==0, alpha=0.3, color='green', label='Low conflict')
ax1.set_ylabel('Regime')
ax1.set_title('Market Integration Regimes Over Time')
ax1.legend()

# Plot 2: Price convergence by regime
# (Add your specific visualization)

plt.tight_layout()
plt.savefig('reports/figures/regime_analysis.png', dpi=300, bbox_inches='tight')
```

## Next Steps

1. **Run full commodity analysis**: Extend to all 22 commodities
2. **Spatial analysis**: Add geographic spillovers
3. **Policy simulations**: Test intervention scenarios
4. **Sensitivity analysis**: Vary key parameters
5. **Write up results**: Prepare technical report

## References

- [Models Overview](../models/README.md)
- [API Reference](../api/models/base.md)
- [Diagnostic Guide](../api/diagnostics/test_battery.md)
- [Data Pipeline Guide](data_pipeline.md)