# Diagnostic Test Battery

## Overview

The diagnostic test battery provides a comprehensive framework for validating econometric models. It organizes tests into six categories and provides automated reporting with clear interpretation guidance.

## Classes

### `DiagnosticResult`

Container for a single diagnostic test result.

```python
@dataclass
class DiagnosticResult:
    test_name: str
    category: str
    statistic: float
    p_value: Optional[float]
    critical_value: Optional[float]
    passed: bool
    interpretation: str
    details: Dict[str, Any]
```

**Attributes:**
- `test_name`: Name of the diagnostic test
- `category`: Test category (data_quality, pre_estimation, etc.)
- `statistic`: Test statistic value
- `p_value`: P-value if applicable
- `critical_value`: Critical value for comparison
- `passed`: Whether the test passed
- `interpretation`: Human-readable interpretation
- `details`: Additional test-specific information

### `DiagnosticTestBattery`

Main class for running comprehensive diagnostic tests.

#### Initialization

```python
DiagnosticTestBattery(model: BaseEconometricModel)
```

**Parameters:**
- `model`: Fitted econometric model to test

#### Methods

##### `run_all_tests(skip_categories: Optional[List[str]] = None, include_slow: bool = True) -> Dict[str, List[DiagnosticResult]]`

Run all applicable diagnostic tests.

**Parameters:**
- `skip_categories`: Categories to skip (e.g., ['robustness'] for faster testing)
- `include_slow`: Whether to include computationally intensive tests

**Returns:**
- Dictionary of results organized by category

##### `generate_report(output_path: Optional[str] = None) -> str`

Generate detailed diagnostic report in Markdown format.

**Parameters:**
- `output_path`: Optional path to save report

**Returns:**
- Report as string

## Test Categories

### 1. Data Quality Tests

Tests for data issues that could affect model validity:

- **Missing Data Patterns**: Checks for systematic missing values
- **Outlier Detection**: Identifies extreme values
- **Structural Breaks**: Tests for breaks in time series
- **Stationarity Preview**: Initial assessment of series properties

### 2. Pre-Estimation Tests

Tests that should be run before model estimation:

- **Unit Root Battery**: ADF and KPSS tests for each series
- **Cointegration Tests**: Johansen trace and eigenvalue tests
- **Lag Order Selection**: AIC, BIC, HQIC criteria
- **Deterministic Components**: Tests for trends and constants

### 3. Specification Tests

Tests for model specification issues:

- **Threshold Effects**: Tests for nonlinearity and thresholds
- **Spatial Correlation**: Tests for spatial dependence
- **Linearity Tests**: RESET and other linearity tests
- **Parameter Constancy**: Tests for time-varying parameters

### 4. Post-Estimation Tests

Tests on model residuals:

- **Serial Correlation**: Ljung-Box and Breusch-Godfrey tests
- **Heteroskedasticity**: White and Breusch-Pagan tests
- **Normality**: Jarque-Bera test
- **ARCH Effects**: Tests for conditional heteroskedasticity
- **Parameter Stability**: CUSUM and recursive residuals

### 5. Robustness Tests

Tests for model robustness:

- **Sample Sensitivity**: Stability across subsamples
- **Influential Observations**: Leverage and Cook's distance
- **Bootstrap Inference**: Parameter stability via bootstrap

### 6. Validation Tests

Out-of-sample performance tests:

- **Forecast Accuracy**: RMSE, MAE, MAPE metrics
- **Cross-Validation**: K-fold cross-validation

## Usage Examples

### Basic Usage

```python
from yemen_market.models.track2_simple.threshold_vecm import SimpleThresholdVECM
from yemen_market.diagnostics.test_battery import DiagnosticTestBattery

# Fit a model
model = SimpleThresholdVECM()
model.fit(data)

# Run all diagnostics
battery = DiagnosticTestBattery(model)
results = battery.run_all_tests()

# Generate report
report = battery.generate_report('diagnostics_report.md')
```

### Selective Testing

```python
# Skip slow tests
results = battery.run_all_tests(
    skip_categories=['robustness'],
    include_slow=False
)

# Run only specific categories
results = battery.run_all_tests(
    skip_categories=['data_quality', 'validation']
)
```

### Interpreting Results

```python
# Check overall pass rate
total_tests = sum(len(tests) for tests in results.values())
passed_tests = sum(
    sum(1 for test in tests if test.passed)
    for tests in results.values()
)
pass_rate = passed_tests / total_tests

print(f"Overall pass rate: {pass_rate:.1%}")

# Check critical failures
critical_categories = ['post_estimation', 'specification']
critical_failures = []

for category in critical_categories:
    for test in results.get(category, []):
        if not test.passed:
            critical_failures.append(f"{category}/{test.test_name}")

if critical_failures:
    print("Critical test failures:")
    for failure in critical_failures:
        print(f"  - {failure}")
```

## Test Implementation

### Pre-Estimation Tests Module

The `pre_estimation.py` module implements:

#### Unit Root Tests
```python
def test_unit_roots_battery(data: Dict[str, Any], 
                           tests: List[str] = ['adf', 'kpss']) -> DiagnosticResult
```

- Runs ADF and KPSS tests on each series
- Returns proportion of I(1) series
- Expects most series to be non-stationary for VECM

#### Cointegration Tests
```python
def test_cointegration(data: Dict[str, Any],
                      det_order: int = 0,
                      k_ar_diff: Optional[int] = None) -> DiagnosticResult
```

- Johansen trace and eigenvalue tests
- Automatic lag order selection
- Returns selected cointegration rank

## Report Format

The generated report includes:

```markdown
# Diagnostic Report for [Model Name]

Generated: [Timestamp]

## Summary
- Total tests run: X
- Tests passed: Y
- Pass rate: Z%

## Data Quality
### Missing Data Patterns
- **Status**: PASSED/FAILED
- **Statistic**: X.XXXX
- **P-value**: X.XXXX
- **Interpretation**: [Human-readable explanation]
...

## Recommendations
### Critical Issues
- **[Test Name]**: [Interpretation]
  - Consider: [Recommendation]
```

## Integration with Models

All models inheriting from `BaseEconometricModel` can use the diagnostic framework:

```python
# In your model class
def validate_model(self):
    """Run comprehensive model validation."""
    battery = DiagnosticTestBattery(self)
    results = battery.run_all_tests()
    
    # Check for critical failures
    if self._has_critical_failures(results):
        warning("Model has critical diagnostic failures")
    
    return results
```

## Custom Tests

To add custom diagnostic tests:

```python
from yemen_market.diagnostics.test_battery import DiagnosticResult

def custom_test(model) -> DiagnosticResult:
    """Custom diagnostic test."""
    # Perform test
    statistic = compute_test_statistic(model)
    p_value = compute_p_value(statistic)
    
    return DiagnosticResult(
        test_name="Custom Test",
        category="custom",
        statistic=statistic,
        p_value=p_value,
        critical_value=0.05,
        passed=p_value > 0.05,
        interpretation=f"Test {'passed' if p_value > 0.05 else 'failed'}",
        details={'additional_info': 'value'}
    )
```

## Performance Considerations

- **Parallel Testing**: Tests within categories can run in parallel
- **Caching**: Results are cached to avoid recomputation
- **Early Stopping**: Critical failures can halt further testing
- **Progress Tracking**: Uses enhanced logging for progress bars

## References

1. Greene, W. H. (2018). *Econometric Analysis* (8th ed.). Pearson.

2. Davidson, R., & MacKinnon, J. G. (2004). *Econometric Theory and Methods*. Oxford University Press.

3. Lütkepohl, H. (2005). *New Introduction to Multiple Time Series Analysis*. Springer.

## See Also

- [Base Model Classes](../models/base.md)
- [Pre-Estimation Tests](tests/pre_estimation.md)
- [Enhanced Logging](../utils/logging.md)
- [Model Comparison](../models/model_comparison.md)