#!/usr/bin/env python3
"""<PERSON>ript to process ACLED conflict data for Yemen market analysis.

This script:
1. Downloads ACLED data if not present
2. Calculates conflict intensity metrics around markets
3. Aggregates to market-month level
4. Creates lagged and moving average features
5. Saves processed data for panel integration
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.data.acled_processor import ACLEDProcessor
from yemen_market.config.settings import ANALYSIS_CONFIG
from yemen_market.utils.logging import setup_logging, info, warning, error

# Set up logging
setup_logging()


def main():
    """Execute ACLED processing workflow."""
    info("=" * 60)
    info("Yemen Market Integration - ACLED Conflict Data Processing")
    info("=" * 60)
    
    try:
        # Initialize processor
        processor = ACLEDProcessor(radius_km=50.0)
        
        # Get date range from config
        start_date = ANALYSIS_CONFIG.get('start_date', '2019-01-01')
        end_date = ANALYSIS_CONFIG.get('end_date', '2024-12-31')
        
        info(f"Processing ACLED data from {start_date} to {end_date}")
        info("Using 50km radius for spatial aggregation")
        
        # Check if ACLED data exists
        acled_files = list(processor.data_dir.glob("*.csv"))
        if not acled_files:
            warning("No ACLED data found in raw/acled/")
            info("\nTo download ACLED data:")
            info("1. Visit https://acleddata.com/data-export-tool/")
            info("2. Select country: Yemen")
            info(f"3. Select date range: {start_date} to {end_date}")
            info("4. Download and save to: data/raw/acled/")
            info("5. Re-run this script")
            return
        
        # Process data
        outputs = processor.process_conflict_data(
            start_date=start_date,
            end_date=end_date
        )
        
        # Display summary
        if 'conflict_summary' in outputs:
            summary = outputs['conflict_summary']
            info("\n" + "=" * 40)
            info("Conflict Data Summary")
            info("=" * 40)
            for _, row in summary.iterrows():
                info(f"{row['statistic']}: {row['value']}")
        
        # Report on outputs
        metrics = outputs.get('conflict_metrics')
        if metrics is not None:
            info("\n" + "=" * 40)
            info("Processing Complete")
            info("=" * 40)
            info(f"Total observations: {len(metrics)}")
            info(f"Markets covered: {metrics['market_id'].nunique()}")
            info(f"Time periods: {metrics['year_month'].nunique()}")
            
            # Check data quality
            missing_pct = metrics['conflict_intensity'].isna().mean() * 100
            info(f"Missing conflict data: {missing_pct:.1f}%")
            
            # Show distribution
            info("\nConflict intensity distribution:")
            info(f"  Min: {metrics['conflict_intensity'].min():.1f}")
            info(f"  25%: {metrics['conflict_intensity'].quantile(0.25):.1f}")
            info(f"  50%: {metrics['conflict_intensity'].quantile(0.50):.1f}")
            info(f"  75%: {metrics['conflict_intensity'].quantile(0.75):.1f}")
            info(f"  Max: {metrics['conflict_intensity'].max():.1f}")
        
        info("\nNext steps:")
        info("1. Review conflict metrics in data/processed/conflict/")
        info("2. Run panel builder to integrate with price data")
        info("3. Create spatial analysis notebook")
        info("4. Begin econometric modeling")
        
    except FileNotFoundError as e:
        error(f"File not found: {e}")
        info("\nPlease ensure WFP data has been processed first:")
        info("  python scripts/process_wfp_data.py")
    except Exception as e:
        error(f"Error processing ACLED data: {e}")
        raise


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        info("\nProcess interrupted by user")
        sys.exit(0)
    except Exception as e:
        error(f"Unexpected error: {e}")
        sys.exit(1)