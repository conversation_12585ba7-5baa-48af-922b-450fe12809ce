"""Unit tests for diagnostic test battery."""

import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from yemen_market.diagnostics.test_battery import DiagnosticTestBattery


class TestDiagnosticTestBattery:
    """Test suite for DiagnosticTestBattery class."""
    
    @pytest.fixture
    def sample_residuals(self):
        """Create sample residuals for testing."""
        np.random.seed(42)
        n = 100
        
        # Create residuals with some structure
        residuals = {
            'market_1': np.random.randn(n) * 2,
            'market_2': np.random.randn(n) * 1.5,
            'market_3': np.random.randn(n) * 2.5
        }
        
        return pd.DataFrame(residuals)
    
    @pytest.fixture
    def sample_data(self):
        """Create sample data for testing."""
        n = 100
        dates = pd.date_range(start='2020-01-01', periods=n, freq='M')
        
        # Create prices with cointegration
        np.random.seed(42)
        common_trend = np.cumsum(np.random.randn(n))
        
        data = pd.DataFrame({
            'date': dates,
            'price_1': 100 + common_trend + np.random.randn(n) * 2,
            'price_2': 98 + common_trend * 0.95 + np.random.randn(n) * 2,
            'price_3': 102 + common_trend * 1.05 + np.random.randn(n) * 2,
            'exog_1': np.random.randn(n),
            'exog_2': np.random.randn(n)
        })
        
        return data
    
    @pytest.fixture
    def diagnostic_battery(self, sample_residuals, sample_data):
        """Create DiagnosticTestBattery instance."""
        return DiagnosticTestBattery(
            residuals=sample_residuals,
            data=sample_data,
            model_type='VECM'
        )
    
    def test_initialization(self, sample_residuals, sample_data):
        """Test initialization."""
        battery = DiagnosticTestBattery(
            residuals=sample_residuals,
            data=sample_data,
            model_type='TVP-VECM'
        )
        
        assert battery.residuals.equals(sample_residuals)
        assert battery.data.equals(sample_data)
        assert battery.model_type == 'TVP-VECM'
        assert battery.test_results == {}
    
    def test_test_serial_correlation(self, diagnostic_battery):
        """Test serial correlation tests."""
        results = diagnostic_battery.test_serial_correlation(max_lag=10)
        
        assert isinstance(results, dict)
        assert 'ljung_box' in results
        assert 'breusch_godfrey' in results
        
        # Check Ljung-Box results
        lb_results = results['ljung_box']
        for market in diagnostic_battery.residuals.columns:
            assert market in lb_results
            assert 'statistic' in lb_results[market]
            assert 'p_value' in lb_results[market]
    
    def test_test_heteroskedasticity(self, diagnostic_battery):
        """Test heteroskedasticity tests."""
        results = diagnostic_battery.test_heteroskedasticity()
        
        assert isinstance(results, dict)
        assert 'white_test' in results
        assert 'arch_test' in results
        
        # Check White test results
        white_results = results['white_test']
        assert 'statistic' in white_results
        assert 'p_value' in white_results
    
    def test_test_normality(self, diagnostic_battery):
        """Test normality tests."""
        results = diagnostic_battery.test_normality()
        
        assert isinstance(results, dict)
        assert 'jarque_bera' in results
        assert 'shapiro_wilk' in results
        
        # Check Jarque-Bera results
        jb_results = results['jarque_bera']
        for market in diagnostic_battery.residuals.columns:
            assert market in jb_results
            assert 'statistic' in jb_results[market]
            assert 'p_value' in jb_results[market]
    
    def test_test_stability(self, diagnostic_battery):
        """Test stability tests."""
        results = diagnostic_battery.test_stability()
        
        assert isinstance(results, dict)
        assert 'cusum' in results
        assert 'recursive_residuals' in results
        
        # Check CUSUM results
        cusum_results = results['cusum']
        assert 'test_statistic' in cusum_results
        assert 'critical_value' in cusum_results
        assert 'stable' in cusum_results
    
    def test_test_cross_correlation(self, diagnostic_battery):
        """Test cross-correlation tests."""
        results = diagnostic_battery.test_cross_correlation()
        
        assert isinstance(results, dict)
        assert 'correlation_matrix' in results
        assert 'significant_pairs' in results
        
        # Check correlation matrix
        corr_matrix = results['correlation_matrix']
        n_markets = len(diagnostic_battery.residuals.columns)
        assert corr_matrix.shape == (n_markets, n_markets)
    
    def test_test_specification(self, diagnostic_battery):
        """Test specification tests."""
        results = diagnostic_battery.test_specification()
        
        assert isinstance(results, dict)
        assert 'ramsey_reset' in results
        assert 'link_test' in results
        
        # Check RESET test results
        reset_results = results['ramsey_reset']
        assert 'statistic' in reset_results
        assert 'p_value' in reset_results
    
    def test_run_all_tests(self, diagnostic_battery):
        """Test running all diagnostic tests."""
        results = diagnostic_battery.run_all_tests()
        
        assert isinstance(results, dict)
        assert len(results) == 6  # Six test categories
        
        expected_categories = [
            'serial_correlation',
            'heteroskedasticity',
            'normality',
            'stability',
            'cross_correlation',
            'specification'
        ]
        
        for category in expected_categories:
            assert category in results
    
    def test_run_tests_with_skip(self, diagnostic_battery):
        """Test running tests with categories skipped."""
        skip_categories = ['stability', 'specification']
        results = diagnostic_battery.run_all_tests(skip_categories=skip_categories)
        
        assert 'stability' not in results
        assert 'specification' not in results
        assert 'normality' in results
        assert len(results) == 4
    
    def test_generate_report(self, diagnostic_battery, tmp_path):
        """Test report generation."""
        # Run tests first
        diagnostic_battery.run_all_tests()
        
        # Generate report
        report_path = tmp_path / "diagnostic_report.txt"
        report = diagnostic_battery.generate_report(output_path=str(report_path))
        
        assert isinstance(report, str)
        assert "Diagnostic Test Report" in report
        assert report_path.exists()
        
        # Check report contents
        report_text = report_path.read_text()
        assert "Serial Correlation Tests" in report_text
        assert "Heteroskedasticity Tests" in report_text
    
    def test_plot_diagnostics(self, diagnostic_battery, tmp_path):
        """Test diagnostic plots."""
        # Run tests first
        diagnostic_battery.run_all_tests()
        
        # Create plots
        save_dir = tmp_path / "diagnostic_plots"
        diagnostic_battery.plot_diagnostics(save_dir=str(save_dir))
        
        assert save_dir.exists()
        
        # Check for expected plot files
        expected_plots = [
            'residual_plots.png',
            'acf_pacf.png',
            'qq_plots.png',
            'stability_plots.png'
        ]
        
        for plot_name in expected_plots:
            assert (save_dir / plot_name).exists()
    
    def test_get_summary_statistics(self, diagnostic_battery):
        """Test summary statistics generation."""
        stats = diagnostic_battery.get_summary_statistics()
        
        assert isinstance(stats, pd.DataFrame)
        assert len(stats) == len(diagnostic_battery.residuals.columns)
        
        expected_columns = ['mean', 'std', 'skewness', 'kurtosis', 'min', 'max']
        for col in expected_columns:
            assert col in stats.columns
    
    def test_empty_residuals(self):
        """Test handling of empty residuals."""
        empty_residuals = pd.DataFrame()
        empty_data = pd.DataFrame()
        
        with pytest.raises(ValueError, match="residuals"):
            DiagnosticTestBattery(
                residuals=empty_residuals,
                data=empty_data,
                model_type='VECM'
            )