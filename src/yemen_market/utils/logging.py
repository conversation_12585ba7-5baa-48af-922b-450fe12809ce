"""
Enhanced logging configuration for Yemen Market Integration Analysis.

This module provides a comprehensive logging solution optimized for econometric
analysis and data science workflows, combining the best features from modern
logging libraries with minimal setup complexity.

Features:
- Structured logging with automatic context injection
- Experiment tracking integration (MLflow compatible)
- Beautiful console output with progress tracking
- Automatic log rotation and archiving
- Performance metrics tracking
- Jupyter notebook compatibility
- Multi-handler configuration (console, file, remote)
- Error tracking with rich context
"""

import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional, Union, Callable
from functools import wraps
import time
from contextlib import contextmanager
from dataclasses import dataclass, asdict

# Core logging libraries
from loguru import logger
import structlog
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeRemainingColumn
from rich.logging import RichHandler
from rich.traceback import install as install_rich_traceback

# Optional integrations (graceful fallback if not installed)
try:
    import mlflow
    MLFLOW_AVAILABLE = True
except ImportError:
    MLFLOW_AVAILABLE = False

try:
    import neptune
    NEPTUNE_AVAILABLE = True
except ImportError:
    NEPTUNE_AVAILABLE = False


# Install rich traceback for beautiful exception formatting
install_rich_traceback(show_locals=True)

# Rich console for beautiful output
console = Console()


@dataclass
class LogConfig:
    """Configuration for the logging system."""
    
    # Paths
    log_dir: Path = Path("logs")
    experiment_dir: Path = Path("experiments")
    
    # Levels
    console_level: str = "INFO"
    file_level: str = "DEBUG"
    
    # Formatting
    use_colors: bool = True
    structured_format: bool = True
    include_context: bool = True
    
    # Rotation
    rotation: str = "100 MB"
    retention: str = "30 days"
    compression: str = "zip"
    
    # Features
    track_performance: bool = True
    track_experiments: bool = True
    enable_remote: bool = False
    
    # Remote logging (if enabled)
    remote_url: Optional[str] = None
    remote_api_key: Optional[str] = None


class EconometricLogger:
    """
    Enhanced logger for econometric analysis with integrated experiment tracking,
    performance monitoring, and beautiful output formatting.
    """
    
    def __init__(self, config: Optional[LogConfig] = None):
        """Initialize the logger with configuration."""
        self.config = config or LogConfig()
        self._setup_directories()
        self._configure_loguru()
        self._configure_structlog()
        self._setup_experiment_tracking()
        self._context = {}
        self._timers = {}
        
    def _setup_directories(self):
        """Create necessary directories."""
        self.config.log_dir.mkdir(parents=True, exist_ok=True)
        self.config.experiment_dir.mkdir(parents=True, exist_ok=True)
        
    def _configure_loguru(self):
        """Configure loguru with our handlers."""
        # Remove default handler
        logger.remove()
        
        # Console handler with rich formatting
        if self.config.use_colors:
            logger.add(
                RichHandler(console=console, rich_tracebacks=True),
                format="{message}",
                level=self.config.console_level,
                filter=self._console_filter
            )
        else:
            logger.add(
                sys.stderr,
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
                level=self.config.console_level
            )
        
        # File handler with rotation
        log_file = self.config.log_dir / "yemen_market_{time:YYYY-MM-DD}.log"
        logger.add(
            str(log_file),
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {extra} | {message}",
            level=self.config.file_level,
            rotation=self.config.rotation,
            retention=self.config.retention,
            compression=self.config.compression,
            serialize=self.config.structured_format,
            enqueue=True  # Thread-safe
        )
        
        # Experiment tracking handler
        if self.config.track_experiments:
            logger.add(
                self._experiment_sink,
                serialize=True,
                filter=lambda record: record["level"].no >= logger.level("INFO").no
            )
        
        # Performance tracking handler
        if self.config.track_performance:
            logger.add(
                str(self.config.log_dir / "performance.jsonl"),
                serialize=True,
                filter=lambda record: "performance" in record["extra"]
            )
            
    def _configure_structlog(self):
        """Configure structlog for structured logging."""
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                self._add_context_processor,
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            cache_logger_on_first_use=True,
        )
        
    def _add_context_processor(self, logger, method_name, event_dict):
        """Add custom context to all log entries."""
        event_dict.update(self._context)
        return event_dict
        
    def _console_filter(self, record):
        """Filter for console output."""
        # Skip performance logs in console
        if "performance" in record["extra"]:
            return False
        return True
        
    def _experiment_sink(self, message):
        """Sink for experiment tracking."""
        try:
            record = json.loads(message)
            
            # MLflow integration
            if MLFLOW_AVAILABLE and mlflow.active_run():
                if "metric" in record.get("extra", {}):
                    mlflow.log_metric(
                        record["extra"]["metric"]["name"],
                        record["extra"]["metric"]["value"],
                        step=record["extra"]["metric"].get("step", 0)
                    )
                elif "param" in record.get("extra", {}):
                    mlflow.log_param(
                        record["extra"]["param"]["name"],
                        record["extra"]["param"]["value"]
                    )
                        
        except Exception as e:
            logger.error(f"Failed to log to experiment tracker: {e}")
            
    def _setup_experiment_tracking(self):
        """Setup experiment tracking integrations."""
        if self.config.track_experiments and MLFLOW_AVAILABLE:
            mlflow.set_tracking_uri(str(self.config.experiment_dir / "mlruns"))
            
    # Context management
    def bind(self, **kwargs) -> "EconometricLogger":
        """Bind context variables to all future logs."""
        self._context.update(kwargs)
        return self
        
    def unbind(self, *keys) -> "EconometricLogger":
        """Remove context variables."""
        for key in keys:
            self._context.pop(key, None)
        return self
        
    @contextmanager
    def context(self, **kwargs):
        """Temporary context for logging."""
        old_context = self._context.copy()
        self._context.update(kwargs)
        try:
            yield self
        finally:
            self._context = old_context
            
    # Performance tracking
    def start_timer(self, name: str):
        """Start a performance timer."""
        self._timers[name] = time.time()
        logger.debug(f"Timer started: {name}")
        
    def stop_timer(self, name: str) -> float:
        """Stop a timer and log the duration."""
        if name not in self._timers:
            logger.warning(f"Timer not found: {name}")
            return 0.0
            
        duration = time.time() - self._timers.pop(name)
        logger.info(
            f"Timer stopped: {name}",
            performance={"timer": name, "duration": duration}
        )
        return duration
        
    @contextmanager
    def timer(self, name: str):
        """Context manager for timing operations."""
        self.start_timer(name)
        try:
            yield
        finally:
            self.stop_timer(name)
            
    # Experiment tracking
    def log_metric(self, name: str, value: float, step: Optional[int] = None):
        """Log a metric for experiment tracking."""
        logger.info(
            f"Metric: {name} = {value}",
            metric={"name": name, "value": value, "step": step}
        )
        
    def log_param(self, name: str, value: Any):
        """Log a parameter for experiment tracking."""
        logger.info(
            f"Parameter: {name} = {value}",
            param={"name": name, "value": value}
        )
        
    def log_artifact(self, path: Union[str, Path], artifact_type: str = "file"):
        """Log an artifact (file) for experiment tracking."""
        path = Path(path)
        if not path.exists():
            logger.error(f"Artifact not found: {path}")
            return
            
        logger.info(
            f"Artifact logged: {path.name}",
            artifact={"path": str(path), "type": artifact_type}
        )
        
        if MLFLOW_AVAILABLE and mlflow.active_run():
            mlflow.log_artifact(str(path))
            
    # Progress tracking
    @contextmanager
    def progress(self, description: str, total: Optional[int] = None):
        """Context manager for progress tracking."""
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeRemainingColumn(),
            console=console,
            transient=True,
        ) as progress:
            task = progress.add_task(description, total=total)
            
            def update(advance: int = 1, description: Optional[str] = None):
                progress.advance(task, advance)
                if description:
                    progress.update(task, description=description)
                    
            yield update
            
    # Decorators
    def log_execution(self, level: str = "INFO"):
        """Decorator to log function execution."""
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                func_name = func.__name__
                logger.log(level, f"Starting: {func_name}")
                
                with self.timer(func_name):
                    try:
                        result = func(*args, **kwargs)
                        logger.log(level, f"Completed: {func_name}")
                        return result
                    except Exception as e:
                        logger.exception(f"Failed: {func_name}")
                        raise
                        
            return wrapper
        return decorator
        
    def log_model_performance(self, model_name: str, metrics: Dict[str, float]):
        """Log model performance metrics."""
        logger.info(
            f"Model performance: {model_name}",
            model=model_name,
            metrics=metrics,
            performance={"type": "model", "name": model_name}
        )
        
        # Log to experiment tracker
        for metric_name, value in metrics.items():
            self.log_metric(f"{model_name}_{metric_name}", value)
            
    # Data pipeline logging
    def log_data_shape(self, name: str, data: Any):
        """Log the shape of data (for DataFrames, arrays, etc.)."""
        shape = None
        dtype = type(data).__name__
        
        if hasattr(data, 'shape'):
            shape = data.shape
        elif hasattr(data, '__len__'):
            shape = (len(data),)
            
        logger.info(
            f"Data shape - {name}: {shape} ({dtype})",
            data_info={"name": name, "shape": shape, "dtype": dtype}
        )
        
    # Convenience methods matching loguru interface
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        logger.bind(**self._context).debug(message, **kwargs)
        
    def info(self, message: str, **kwargs):
        """Log info message."""
        logger.bind(**self._context).info(message, **kwargs)
        
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        logger.bind(**self._context).warning(message, **kwargs)
        
    def error(self, message: str, **kwargs):
        """Log error message."""
        logger.bind(**self._context).error(message, **kwargs)
        
    def critical(self, message: str, **kwargs):
        """Log critical message."""
        logger.bind(**self._context).critical(message, **kwargs)
        
    def exception(self, message: str, **kwargs):
        """Log exception with traceback."""
        logger.bind(**self._context).exception(message, **kwargs)


# Create default logger instance
default_logger = EconometricLogger()

# Convenience exports
bind = default_logger.bind
unbind = default_logger.unbind
context = default_logger.context
timer = default_logger.timer
progress = default_logger.progress
log_execution = default_logger.log_execution
log_metric = default_logger.log_metric
log_param = default_logger.log_param
log_artifact = default_logger.log_artifact
log_model_performance = default_logger.log_model_performance
log_data_shape = default_logger.log_data_shape

# Standard logging methods
debug = default_logger.debug
info = default_logger.info
warning = default_logger.warning
error = default_logger.error
critical = default_logger.critical
exception = default_logger.exception


# Backward compatibility functions
def setup_logging(level: Union[str, int] = "INFO", 
                  log_file: Optional[Union[str, Path]] = None) -> None:
    """
    Setup basic logging configuration for backward compatibility.
    
    This function provides a simple interface that existing scripts expect.
    It configures the standard Python logging to work with our enhanced system.
    
    Parameters
    ----------
    level : str or int, optional
        Logging level (default: "INFO")
    log_file : str or Path, optional
        Path to log file (default: None, logs to console only)
    """
    # Configure standard Python logging to use our enhanced logger
    import logging as stdlib_logging
    
    # Remove existing handlers
    root_logger = stdlib_logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create a custom handler that forwards to our enhanced logger
    class LoguruHandler(stdlib_logging.Handler):
        def emit(self, record):
            # Convert standard logging record to loguru
            try:
                level = record.levelname
                msg = self.format(record)
                
                # Map standard logging levels to loguru
                if level == "DEBUG":
                    logger.debug(msg)
                elif level == "INFO":
                    logger.info(msg)
                elif level == "WARNING":
                    logger.warning(msg)
                elif level == "ERROR":
                    logger.error(msg)
                elif level == "CRITICAL":
                    logger.critical(msg)
                else:
                    logger.info(msg)
            except Exception:
                self.handleError(record)
    
    # Add our handler to root logger
    handler = LoguruHandler()
    handler.setFormatter(stdlib_logging.Formatter('%(message)s'))
    root_logger.addHandler(handler)
    root_logger.setLevel(level)
    
    # If log_file specified, update default logger config
    if log_file:
        config = LogConfig()
        config.log_dir = Path(log_file).parent
        global default_logger
        default_logger = EconometricLogger(config)
    
    # Log that setup is complete
    logger.info(f"Logging configured with level: {level}")


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    Get a standard Python logger that forwards to our enhanced system.
    
    This provides backward compatibility for code expecting standard loggers.
    
    Parameters
    ----------
    name : str, optional
        Logger name (usually __name__)
        
    Returns
    -------
    logging.Logger
        Standard Python logger that forwards to enhanced system
    """
    import logging as stdlib_logging
    
    # Ensure setup_logging has been called
    if not stdlib_logging.getLogger().handlers:
        setup_logging()
    
    return stdlib_logging.getLogger(name)
