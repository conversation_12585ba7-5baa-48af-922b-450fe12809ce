"""Base classes for econometric models.

This module provides abstract base classes and common functionality
for all econometric models in the Yemen market integration analysis.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple, Union
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
import json
from pathlib import Path
from enum import Enum

from ..utils.logging import bind, timer, info, warning, error, log_metric, log_data_shape


class ModelType(Enum):
    """Enumeration of model types."""
    VECM = "vecm"
    THRESHOLD_VECM = "threshold_vecm"
    TVP_VECM = "tvp_vecm"
    SPATIAL_VECM = "spatial_vecm"
    BAYESIAN_VECM = "bayesian_vecm"


@dataclass
class VECMResults:
    """Container for VECM-specific results."""
    
    # Core VECM parameters
    alpha: np.ndarray  # Adjustment speeds
    beta: np.ndarray   # Cointegrating vectors
    gamma: Optional[np.ndarray] = None  # Short-run dynamics
    
    # Threshold-specific (if applicable)
    threshold_value: Optional[float] = None
    threshold_variable: Optional[str] = None
    regime_coefficients: Optional[Dict[str, np.ndarray]] = None
    
    # Time-varying parameters (if applicable)
    time_varying_params: Optional[Dict[str, np.ndarray]] = None
    
    # Spatial components (if applicable)
    spatial_coefficients: Optional[np.ndarray] = None
    spatial_weights: Optional[np.ndarray] = None
    
    # Model fit statistics
    log_likelihood: float = 0.0
    aic: float = 0.0
    bic: float = 0.0
    hqic: float = 0.0
    
    # Sample information
    n_obs: int = 0
    n_params: int = 0
    n_coint: int = 0
    n_lags: int = 0
    
    # Convergence and diagnostics
    converged: bool = True
    iterations: int = 0
    convergence_msg: str = ""
    
    # Standard errors (if available)
    alpha_se: Optional[np.ndarray] = None
    beta_se: Optional[np.ndarray] = None
    gamma_se: Optional[np.ndarray] = None


@dataclass
class ModelResults:
    """Container for model estimation results."""
    
    coefficients: pd.Series
    standard_errors: pd.Series
    t_statistics: pd.Series
    p_values: pd.Series
    log_likelihood: float
    aic: float
    bic: float
    n_obs: int
    n_params: int
    converged: bool
    diagnostics: Dict[str, Any]
    
    def summary(self) -> str:
        """Generate summary string."""
        summary = f"""
Model Results Summary
====================
Observations: {self.n_obs}
Parameters: {self.n_params}
Log-likelihood: {self.log_likelihood:.2f}
AIC: {self.aic:.2f}
BIC: {self.bic:.2f}
Converged: {self.converged}

Coefficients:
{self.coefficients.to_string()}

Standard Errors:
{self.standard_errors.to_string()}
        """
        return summary.strip()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'coefficients': self.coefficients.to_dict(),
            'standard_errors': self.standard_errors.to_dict(),
            't_statistics': self.t_statistics.to_dict(),
            'p_values': self.p_values.to_dict(),
            'log_likelihood': self.log_likelihood,
            'aic': self.aic,
            'bic': self.bic,
            'n_obs': self.n_obs,
            'n_params': self.n_params,
            'converged': self.converged,
            'diagnostics': self.diagnostics
        }
    
    def save(self, filepath: Path) -> None:
        """Save results to JSON file."""
        with open(filepath, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)


class BaseEconometricModel(ABC):
    """Abstract base class for all econometric models.
    
    This class defines the interface that all models must implement
    and provides common functionality for diagnostics, prediction,
    and results management.
    """
    
    def __init__(self, name: str = None):
        """Initialize base model.
        
        Args:
            name: Optional model name for logging
        """
        self.name = name or self.__class__.__name__
        self.is_fitted = False
        self.results: Optional[ModelResults] = None
        self.data: Optional[pd.DataFrame] = None
        self.diagnostics: Dict[str, Any] = {}
        
        bind(model=self.name)
        info(f"Initialized {self.name}")
    
    @abstractmethod
    def fit(self, data: pd.DataFrame, **kwargs) -> 'BaseEconometricModel':
        """Fit the model to data.
        
        Args:
            data: Panel data for estimation
            **kwargs: Model-specific parameters
            
        Returns:
            Self for method chaining
        """
        pass
    
    @abstractmethod
    def predict(self, steps: int = 1, 
                exog: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """Generate predictions.
        
        Args:
            steps: Number of steps ahead to forecast
            exog: Optional exogenous variables for prediction
            
        Returns:
            DataFrame of predictions
        """
        pass
    
    def get_residuals(self) -> pd.Series:
        """Get model residuals.
        
        Returns:
            Series of residual values
            
        Raises:
            ValueError: If model not fitted
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        return self._calculate_residuals()
    
    @abstractmethod
    def _calculate_residuals(self) -> pd.Series:
        """Calculate residuals (model-specific implementation)."""
        pass
    
    def run_diagnostics(self, tests: Optional[List[str]] = None) -> Dict[str, Any]:
        """Run diagnostic tests on the fitted model.
        
        Args:
            tests: Optional list of specific tests to run.
                   If None, runs all applicable tests.
                   
        Returns:
            Dictionary of test results
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
            
        with timer("diagnostics"):
            info(f"Running diagnostics for {self.name}")
            
            from ..diagnostics.test_battery import run_standard_tests
            
            if tests is None:
                # Run all applicable tests
                self.diagnostics = run_standard_tests(self)
            else:
                # Run only specified tests
                self.diagnostics = run_standard_tests(self, tests=tests)
            
            # Check for critical failures
            self._check_diagnostic_failures()
            
        return self.diagnostics
    
    def _check_diagnostic_failures(self) -> None:
        """Check for critical diagnostic test failures."""
        critical_tests = {
            'serial_correlation': 0.05,
            'heteroskedasticity': 0.05,
            'normality': 0.01,  # Less critical
            'stability': 0.05
        }
        
        failures = []
        for test, threshold in critical_tests.items():
            if test in self.diagnostics:
                p_value = self.diagnostics[test].get('p_value', 1.0)
                if p_value < threshold:
                    failures.append(f"{test} (p={p_value:.3f})")
        
        if failures:
            warning(f"Diagnostic test failures: {', '.join(failures)}")
    
    def summary(self) -> None:
        """Print model summary."""
        if not self.is_fitted:
            print(f"{self.name}: Not fitted")
            return
            
        print(self.results.summary())
        
        if self.diagnostics:
            print("\nDiagnostic Tests:")
            print("-" * 40)
            for test, result in self.diagnostics.items():
                if isinstance(result, dict) and 'p_value' in result:
                    print(f"{test}: p-value = {result['p_value']:.4f}")
    
    def save_results(self, filepath: Path) -> None:
        """Save model results to file.
        
        Args:
            filepath: Path to save results
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
            
        self.results.save(filepath)
        info(f"Saved {self.name} results to {filepath}")
    
    def plot_diagnostics(self, save_path: Optional[Path] = None) -> None:
        """Generate diagnostic plots.
        
        Args:
            save_path: Optional path to save plots
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
            
        from ..visualization.model_diagnostics import plot_diagnostic_suite
        
        plot_diagnostic_suite(self, save_path=save_path)
    
    def get_information_criteria(self) -> Dict[str, float]:
        """Get information criteria for model comparison.
        
        Returns:
            Dict with AIC, BIC, HQIC values
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
            
        return {
            'AIC': self.results.aic,
            'BIC': self.results.bic,
            'HQIC': self._calculate_hqic() if hasattr(self, '_calculate_hqic') else None,
            'log_likelihood': self.results.log_likelihood
        }
    
    def forecast_evaluation(self, test_data: pd.DataFrame,
                          metrics: List[str] = ['rmse', 'mae', 'mape']) -> Dict[str, float]:
        """Evaluate forecast performance on test data.
        
        Args:
            test_data: Out-of-sample test data
            metrics: List of metrics to calculate
            
        Returns:
            Dictionary of metric values
        """
        with timer("forecast_evaluation"):
            predictions = self.predict(steps=len(test_data))
            actual = test_data[self._get_dependent_var()]
            
            from sklearn.metrics import mean_squared_error, mean_absolute_error
            
            results = {}
            if 'rmse' in metrics:
                results['rmse'] = np.sqrt(mean_squared_error(actual, predictions))
            if 'mae' in metrics:
                results['mae'] = mean_absolute_error(actual, predictions)
            if 'mape' in metrics:
                results['mape'] = np.mean(np.abs((actual - predictions) / actual)) * 100
                
            return results
    
    @abstractmethod
    def _get_dependent_var(self) -> str:
        """Get name of dependent variable (model-specific)."""
        pass


class ThresholdModelMixin:
    """Mixin for models with threshold effects."""
    
    def estimate_threshold(self, threshold_var: str,
                          trim: float = 0.15,
                          n_boot: int = 1000) -> Dict[str, Any]:
        """Estimate threshold value and test significance.
        
        Args:
            threshold_var: Variable to use for threshold
            trim: Trimming percentage for threshold search
            n_boot: Number of bootstrap replications
            
        Returns:
            Dict with threshold value, test statistic, p-value
        """
        from ..diagnostics.tests.specification import hansen_threshold_test
        
        return hansen_threshold_test(
            self.data,
            threshold_var=threshold_var,
            trim=trim,
            n_boot=n_boot
        )
    
    def plot_threshold_search(self, save_path: Optional[Path] = None) -> None:
        """Plot likelihood ratio statistics for threshold search.
        
        Args:
            save_path: Optional path to save plot
        """
        from ..visualization.model_diagnostics import plot_threshold_search
        
        plot_threshold_search(self, save_path=save_path)


class SpatialModelMixin:
    """Mixin for models with spatial components."""
    
    def calculate_spatial_weights(self, weight_type: str = 'distance',
                                 k_neighbors: int = 5) -> np.ndarray:
        """Calculate spatial weight matrix.
        
        Args:
            weight_type: Type of weights ('distance', 'contiguity', 'network')
            k_neighbors: Number of neighbors for KNN weights
            
        Returns:
            Spatial weight matrix
        """
        from ..models.track1_complex.spatial_network import create_weight_matrix
        
        return create_weight_matrix(
            self.data,
            weight_type=weight_type,
            k_neighbors=k_neighbors
        )
    
    def test_spatial_autocorrelation(self) -> Dict[str, Any]:
        """Test for spatial autocorrelation in residuals.
        
        Returns:
            Dict with Moran's I and related statistics
        """
        from ..diagnostics.tests.specification import spatial_autocorrelation_tests
        
        residuals = self.get_residuals()
        W = self.calculate_spatial_weights()
        
        return spatial_autocorrelation_tests(residuals, W)


class PanelModelMixin:
    """Mixin for panel data models."""
    
    def test_poolability(self) -> Dict[str, Any]:
        """Test whether panel can be pooled.
        
        Returns:
            Dict with test statistics and p-values
        """
        from ..diagnostics.tests.specification import poolability_test
        
        return poolability_test(self.data, self.results)
    
    def hausman_test(self, alternative_model: 'BaseEconometricModel') -> Dict[str, Any]:
        """Hausman test for model specification.
        
        Args:
            alternative_model: Alternative model for comparison
            
        Returns:
            Dict with test statistic and p-value
        """
        from ..diagnostics.tests.specification import hausman_test
        
        return hausman_test(self, alternative_model)


class VECMBase(BaseEconometricModel):
    """Base class for Vector Error Correction Models.
    
    This class provides common functionality for all VECM variants
    including standard VECM, threshold VECM, and time-varying parameter VECM.
    """
    
    def __init__(self, n_coint: int = 1, n_lags: int = 2, 
                 deterministic: str = 'ci', name: str = None):
        """Initialize VECM base model.
        
        Args:
            n_coint: Number of cointegrating relationships
            n_lags: Number of lags in the model
            deterministic: Deterministic trend specification
                'n': no deterministic terms
                'c': constant term only
                'ct': constant and trend
                'ci': constant in cointegrating relation (default)
                'cit': constant and trend in cointegrating relation
            name: Optional model name
        """
        super().__init__(name=name or "VECM")
        self.n_coint = n_coint
        self.n_lags = n_lags
        self.deterministic = deterministic
        self.vecm_results: Optional[VECMResults] = None
        
    def prepare_data(self, data: pd.DataFrame, 
                    price_cols: List[str],
                    exog_cols: Optional[List[str]] = None) -> Dict[str, Any]:
        """Prepare data for VECM estimation.
        
        Args:
            data: Panel data with date, market, and price columns
            price_cols: List of price column names to include
            exog_cols: Optional exogenous variable columns
            
        Returns:
            Dictionary with prepared matrices and metadata
        """
        with timer("data_preparation"):
            info("Preparing data for VECM estimation")
            
            # Pivot to wide format
            price_data = data.pivot_table(
                index='date',
                columns='market_name',
                values=price_cols,
                aggfunc='mean'
            )
            
            # Log transform prices
            log_prices = np.log(price_data)
            
            # Handle missing values
            log_prices = log_prices.ffill().bfill()
            
            # Create lagged differences
            dlogs = log_prices.diff().dropna()
            
            # Extract dimensions
            n_obs, n_vars = dlogs.shape
            
            log_data_shape("prepared_data", dlogs)
            
            prepared = {
                'log_prices': log_prices,
                'dlogs': dlogs,
                'n_obs': n_obs,
                'n_vars': n_vars,
                'markets': list(price_data.columns),
                'dates': price_data.index
            }
            
            # Add exogenous variables if provided
            if exog_cols:
                exog_data = data.pivot_table(
                    index='date',
                    columns='market_name',
                    values=exog_cols,
                    aggfunc='mean'
                )
                prepared['exog'] = exog_data
                
            return prepared
    
    def test_cointegration(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Test for cointegration using Johansen test.
        
        Args:
            data: Prepared data dictionary from prepare_data
            
        Returns:
            Dictionary with test results and selected rank
        """
        from statsmodels.tsa.vector_ar.vecm import coint_johansen
        
        with timer("cointegration_test"):
            info("Testing for cointegration relationships")
            
            log_prices = data['log_prices'].values
            
            # Run Johansen test
            joh_results = coint_johansen(log_prices, det_order=0, k_ar_diff=self.n_lags-1)
            
            # Extract results
            trace_stats = joh_results.lr1
            trace_crits = joh_results.cvt
            eigen_stats = joh_results.lr2
            eigen_crits = joh_results.cvm
            
            # Determine cointegration rank
            trace_rank = np.sum(trace_stats > trace_crits[:, 1])  # 5% level
            eigen_rank = np.sum(eigen_stats > eigen_crits[:, 1])
            
            selected_rank = min(trace_rank, eigen_rank, self.n_coint)
            
            results = {
                'trace_stats': trace_stats,
                'trace_crits': trace_crits,
                'eigen_stats': eigen_stats,
                'eigen_crits': eigen_crits,
                'trace_rank': trace_rank,
                'eigen_rank': eigen_rank,
                'selected_rank': selected_rank,
                'eigenvalues': joh_results.eig,
                'eigenvectors': joh_results.evec
            }
            
            log_metric("cointegration_rank", selected_rank)
            info(f"Selected cointegration rank: {selected_rank}")
            
            return results
    
    def _get_dependent_var(self) -> str:
        """Get name of dependent variable for VECM models."""
        return "price_changes"
    
    def calculate_error_correction_terms(self, log_prices: np.ndarray,
                                       beta: np.ndarray) -> np.ndarray:
        """Calculate error correction terms.
        
        Args:
            log_prices: Log price matrix (T x N)
            beta: Cointegrating vectors (N x r)
            
        Returns:
            Error correction terms (T x r)
        """
        return log_prices @ beta
    
    def estimate_adjustment_speeds(self, dlogs: np.ndarray,
                                  ect: np.ndarray,
                                  lags: int) -> Dict[str, np.ndarray]:
        """Estimate adjustment speed parameters.
        
        Args:
            dlogs: First differences of log prices
            ect: Error correction terms
            lags: Number of lags
            
        Returns:
            Dictionary with alpha coefficients and statistics
        """
        from sklearn.linear_model import LinearRegression
        
        n_obs, n_vars = dlogs.shape
        n_coint = ect.shape[1]
        
        # Lag ECT by one period
        ect_lag = ect[:-1]
        dlogs_trim = dlogs[1:]
        
        # Create lagged differences
        dlogs_lags = []
        for lag in range(1, lags):
            if lag < n_obs - 1:
                dlogs_lags.append(dlogs[lag:-1 if n_obs-lag-1 > 0 else n_obs])
        
        # Stack regressors
        if dlogs_lags:
            X = np.hstack([ect_lag[lags-1:]] + 
                         [dl[:len(ect_lag)-lags+1] for dl in dlogs_lags])
        else:
            X = ect_lag
            
        y = dlogs_trim[lags-1:] if lags > 1 else dlogs_trim
        
        # Estimate equation by equation
        alpha = np.zeros((n_vars, n_coint))
        alpha_se = np.zeros((n_vars, n_coint))
        
        for i in range(n_vars):
            reg = LinearRegression(fit_intercept=True)
            reg.fit(X, y[:, i])
            alpha[i, :] = reg.coef_[:n_coint]
            
            # Calculate standard errors
            residuals = y[:, i] - reg.predict(X)
            sigma2 = np.sum(residuals**2) / (len(residuals) - X.shape[1])
            var_beta = sigma2 * np.linalg.inv(X.T @ X)
            alpha_se[i, :] = np.sqrt(np.diag(var_beta)[:n_coint])
        
        return {
            'alpha': alpha,
            'alpha_se': alpha_se,
            'gamma': reg.coef_[n_coint:] if X.shape[1] > n_coint else None
        }
    
    def impulse_response(self, periods: int = 10,
                        shock_size: float = 1.0,
                        shock_var: int = 0) -> pd.DataFrame:
        """Calculate impulse response functions.
        
        Args:
            periods: Number of periods for impulse response
            shock_size: Size of the shock (in standard deviations)
            shock_var: Index of variable to shock
            
        Returns:
            DataFrame with impulse responses
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
            
        # Implementation depends on specific VECM variant
        raise NotImplementedError("Subclasses must implement impulse_response")
    
    def forecast_error_variance_decomposition(self, periods: int = 10) -> pd.DataFrame:
        """Calculate forecast error variance decomposition.
        
        Args:
            periods: Number of periods
            
        Returns:
            DataFrame with variance decomposition
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
            
        # Implementation depends on specific VECM variant
        raise NotImplementedError("Subclasses must implement FEVD")
    
    def test_weak_exogeneity(self) -> Dict[str, Any]:
        """Test weak exogeneity of variables.
        
        Returns:
            Dictionary with test results for each variable
        """
        if not self.is_fitted or self.vecm_results is None:
            raise ValueError("Model must be fitted first")
            
        alpha = self.vecm_results.alpha
        alpha_se = self.vecm_results.alpha_se
        
        if alpha_se is None:
            warning("Standard errors not available for weak exogeneity test")
            return {}
        
        # Test H0: alpha_i = 0 for each variable
        from scipy import stats
        t_stats = alpha / (alpha_se + 1e-10)
        df = self.vecm_results.n_obs - self.vecm_results.n_params
        p_values = 2 * (1 - stats.t.cdf(np.abs(t_stats), df))
        
        results = {}
        for i, market in enumerate(self.data['markets']):
            results[market] = {
                'alpha': alpha[i],
                't_stat': t_stats[i],
                'p_value': p_values[i],
                'is_weakly_exogenous': np.all(p_values[i] > 0.05)
            }
            
        return results