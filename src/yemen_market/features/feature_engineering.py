"""Feature engineering for Yemen market integration analysis.

This module provides functions and classes for creating derived features
from the integrated panel data, including temporal, spatial, interaction,
and threshold-based features.
"""

from typing import Dict, List, Optional, Tuple, Union
import pandas as pd
import numpy as np
from scipy.spatial.distance import cdist

from ..utils.logging import info, warning, debug, bind, timer, log_data_shape


class FeatureEngineer:
    """Main class for feature engineering on panel data.
    
    This class coordinates the creation of various feature types:
    - Temporal features (lags, rolling stats, differences)
    - Interaction features (price × conflict, zone × time)
    - Threshold indicators (regime switching variables)
    - Spatial features (neighbor averages, distance-based)
    - Conflict features (intensity quartiles, event types)
    """
    
    def __init__(self, 
                 temporal_lags: List[int] = [1, 2, 3],
                 rolling_windows: List[int] = [3, 6],
                 spatial_neighbors: int = 5):
        """Initialize feature engineer.
        
        Args:
            temporal_lags: List of lag periods to create
            rolling_windows: List of window sizes for rolling stats
            spatial_neighbors: Number of nearest neighbors for spatial features
        """
        bind(module=__name__)
        
        self.temporal_lags = temporal_lags
        self.rolling_windows = rolling_windows
        self.spatial_neighbors = spatial_neighbors
        
        info("FeatureEngineer initialized",
             temporal_lags=temporal_lags,
             rolling_windows=rolling_windows,
             spatial_neighbors=spatial_neighbors)
    
    def fit_transform(self, panel_df: pd.DataFrame) -> pd.DataFrame:
        """Apply all feature engineering steps to panel data.
        
        Args:
            panel_df: Input panel DataFrame
            
        Returns:
            Enhanced DataFrame with engineered features
        """
        with timer("feature_engineering"):
            info("Starting feature engineering pipeline")
            log_data_shape("input_panel", panel_df)
        
        # Create a copy to avoid modifying original
        df = panel_df.copy()
        
        # Apply feature engineering steps
        df = create_temporal_features(df, self.temporal_lags, self.rolling_windows)
        df = create_interaction_features(df)
        df = create_threshold_indicators(df)
        
        # Only create spatial features if coordinates exist
        if 'lat' in df.columns and 'lon' in df.columns:
            df = create_spatial_features(df, self.spatial_neighbors)
        else:
            warning("No coordinates found, skipping spatial features")
        
        # Create conflict features if conflict data exists
        if 'conflict_intensity' in df.columns:
            df = create_conflict_features(df)
        else:
            warning("No conflict data found, skipping conflict features")
        
        log_data_shape("output_panel", df)
        
        # Report new features created
        new_features = [col for col in df.columns if col not in panel_df.columns]
        info(f"Created {len(new_features)} new features",
             feature_types={
                 'temporal': len([f for f in new_features if any(x in f for x in ['lag', 'ma', 'std', 'diff'])]),
                 'interaction': len([f for f in new_features if '×' in f or '_x_' in f]),
                 'threshold': len([f for f in new_features if 'threshold' in f or 'regime' in f]),
                 'spatial': len([f for f in new_features if 'spatial' in f or 'neighbor' in f]),
                 'conflict': len([f for f in new_features if 'conflict' in f and f not in panel_df.columns])
             })
        
        return df


def create_temporal_features(df: pd.DataFrame, 
                           lags: List[int] = [1, 2, 3],
                           windows: List[int] = [3, 6]) -> pd.DataFrame:
    """Create temporal features including lags, differences, and rolling statistics.
    
    Args:
        df: Input DataFrame
        lags: List of lag periods
        windows: List of rolling window sizes
        
    Returns:
        DataFrame with temporal features added
    """
    with timer("temporal_features"):
        info("Creating temporal features")
    
    # Identify grouping columns
    if 'commodity' in df.columns and 'market_id' in df.columns:
        group_cols = ['commodity', 'market_id']
    elif 'market_id' in df.columns:
        group_cols = ['market_id']
    else:
        warning("No suitable grouping columns found for temporal features")
        return df
    
    # Sort by group and time
    df = df.sort_values(group_cols + ['date'])
    
    # Key variables for temporal features
    temporal_vars = ['price_usd', 'conflict_intensity', 'exchange_rate']
    temporal_vars = [v for v in temporal_vars if v in df.columns]
    
    for var in temporal_vars:
        # Create lags
        for lag in lags:
            df[f'{var}_lag{lag}'] = df.groupby(group_cols)[var].shift(lag)
        
        # First difference
        df[f'{var}_diff'] = df.groupby(group_cols)[var].diff()
        
        # Percentage change
        df[f'{var}_pct_change'] = df.groupby(group_cols)[var].pct_change()
        
        # Rolling statistics
        for window in windows:
            # Rolling mean
            df[f'{var}_ma{window}'] = df.groupby(group_cols)[var].transform(
                lambda x: x.rolling(window=window, min_periods=1).mean()
            )
            
            # Rolling standard deviation
            df[f'{var}_std{window}'] = df.groupby(group_cols)[var].transform(
                lambda x: x.rolling(window=window, min_periods=2).std()
            )
            
            # Coefficient of variation
            df[f'{var}_cv{window}'] = df[f'{var}_std{window}'] / df[f'{var}_ma{window}']
    
    # Time trend
    df['time_trend'] = df.groupby(group_cols).cumcount() + 1
    df['time_trend_squared'] = df['time_trend'] ** 2
    
    info("Temporal features created",
         variables=len(temporal_vars),
         total_features=len(lags) * len(temporal_vars) * 2 + len(windows) * len(temporal_vars) * 3)
    
    return df


def create_interaction_features(df: pd.DataFrame) -> pd.DataFrame:
    """Create interaction features between key variables.
    
    Args:
        df: Input DataFrame
        
    Returns:
        DataFrame with interaction features added
    """
    with timer("interaction_features"):
        info("Creating interaction features")
    
    interactions_created = 0
    
    # Price × Conflict interactions
    if 'price_usd' in df.columns and 'conflict_intensity' in df.columns:
        df['price_x_conflict'] = df['price_usd'] * df['conflict_intensity']
        df['price_x_high_conflict'] = df['price_usd'] * (df['conflict_intensity'] > df['conflict_intensity'].quantile(0.75))
        interactions_created += 2
    
    # Zone × Time interactions
    if 'control_zone' in df.columns and 'time_trend' in df.columns:
        # Create zone dummies
        zone_dummies = pd.get_dummies(df['control_zone'], prefix='zone')
        for col in zone_dummies.columns:
            df[f'{col}_x_trend'] = zone_dummies[col] * df['time_trend']
            interactions_created += 1
    
    # Exchange rate × Zone interactions
    if 'exchange_rate' in df.columns and 'control_zone' in df.columns:
        for zone in df['control_zone'].unique():
            if pd.notna(zone):
                df[f'exchange_rate_x_{zone}'] = df['exchange_rate'] * (df['control_zone'] == zone)
                interactions_created += 1
    
    # Conflict × Zone interactions
    if 'conflict_intensity' in df.columns and 'control_zone' in df.columns:
        for zone in df['control_zone'].unique():
            if pd.notna(zone):
                df[f'conflict_x_{zone}'] = df['conflict_intensity'] * (df['control_zone'] == zone)
                interactions_created += 1
    
    info(f"Created {interactions_created} interaction features")
    
    return df


def create_threshold_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """Create threshold indicators for regime switching models.
    
    Args:
        df: Input DataFrame
        
    Returns:
        DataFrame with threshold indicators added
    """
    with timer("threshold_indicators"):
        info("Creating threshold indicators")
    
    indicators_created = 0
    
    # Conflict intensity thresholds
    if 'conflict_intensity' in df.columns:
        # Quartile-based thresholds
        q1 = df['conflict_intensity'].quantile(0.25)
        q2 = df['conflict_intensity'].quantile(0.50)
        q3 = df['conflict_intensity'].quantile(0.75)
        
        df['conflict_regime_low'] = (df['conflict_intensity'] <= q1).astype(int)
        df['conflict_regime_medium'] = ((df['conflict_intensity'] > q1) & 
                                       (df['conflict_intensity'] <= q3)).astype(int)
        df['conflict_regime_high'] = (df['conflict_intensity'] > q3).astype(int)
        
        # Binary threshold at median
        df['high_conflict_threshold'] = (df['conflict_intensity'] > q2).astype(int)
        
        indicators_created += 4
    
    # Exchange rate differential thresholds
    if 'rate_differential' in df.columns:
        # 10% and 20% thresholds
        df['rate_diff_threshold_10'] = (df['rate_differential'].abs() > 0.10).astype(int)
        df['rate_diff_threshold_20'] = (df['rate_differential'].abs() > 0.20).astype(int)
        
        indicators_created += 2
    
    # Price volatility thresholds
    if 'price_usd_std3' in df.columns:
        volatility_median = df['price_usd_std3'].median()
        df['high_volatility'] = (df['price_usd_std3'] > volatility_median).astype(int)
        
        indicators_created += 1
    
    # Contested zone indicator
    if 'control_zone' in df.columns:
        df['is_contested'] = (df['control_zone'] == 'contested').astype(int)
        indicators_created += 1
    
    info(f"Created {indicators_created} threshold indicators")
    
    return df


def create_spatial_features(df: pd.DataFrame, n_neighbors: int = 5) -> pd.DataFrame:
    """Create spatial features based on geographic proximity.
    
    Args:
        df: Input DataFrame with lat/lon coordinates
        n_neighbors: Number of nearest neighbors to consider
        
    Returns:
        DataFrame with spatial features added
    """
    with timer("spatial_features"):
        info("Creating spatial features")
    
    # Get unique markets with coordinates
    markets = df[['market_id', 'lat', 'lon']].drop_duplicates()
    markets = markets.dropna(subset=['lat', 'lon'])
    
    if len(markets) < 2:
        warning("Not enough markets with coordinates for spatial features")
        return df
    
    # Calculate distance matrix
    coords = markets[['lat', 'lon']].values
    dist_matrix = cdist(coords, coords, metric='euclidean')
    
    # Convert to DataFrame for easier handling
    dist_df = pd.DataFrame(
        dist_matrix,
        index=markets['market_id'],
        columns=markets['market_id']
    )
    
    # For each market, find k nearest neighbors
    neighbor_dict = {}
    for market in markets['market_id'].values:
        if market in dist_df.columns:
            # Get distances for this market
            distances = dist_df.loc[:, market]
            if isinstance(distances, pd.DataFrame):
                # Handle case where column selection returns DataFrame
                distances = distances.iloc[:, 0]
            distances = distances.sort_values()
            # Exclude self (distance = 0) and get n_neighbors
            neighbors = distances.iloc[1:n_neighbors+1].index.tolist()
            neighbor_dict[market] = neighbors
        else:
            warning(f"Market {market} not found in distance matrix")
    
    # Create spatial features
    spatial_vars = ['price_usd', 'conflict_intensity']
    spatial_vars = [v for v in spatial_vars if v in df.columns]
    
    for var in spatial_vars:
        # Spatial lag (average of neighbors)
        df[f'{var}_spatial_lag'] = df.apply(
            lambda row: df[(df['market_id'].isin(neighbor_dict.get(row['market_id'], []))) & 
                          (df['date'] == row['date'])][var].mean()
            if row['market_id'] in neighbor_dict else np.nan,
            axis=1
        )
        
        # Spatial difference (value - neighbor average)
        df[f'{var}_spatial_diff'] = df[var] - df[f'{var}_spatial_lag']
    
    # Distance to nearest market with different control zone
    if 'control_zone' in df.columns:
        distance_to_other = []
        for idx, row in df.iterrows():
            if row['market_id'] not in dist_df.index:
                distance_to_other.append(np.nan)
                continue
            
            # Get markets with different control zone
            other_zone_markets = df[df['control_zone'] != row['control_zone']]['market_id'].unique()
            other_zone_markets = [m for m in other_zone_markets if m in markets['market_id'].values and m in dist_df.columns]
            
            if not other_zone_markets:
                distance_to_other.append(np.nan)
                continue
                
            # Get distances to these markets
            try:
                if len(other_zone_markets) == 1:
                    # Single market - get scalar value
                    distance_to_other.append(float(dist_df.loc[row['market_id'], other_zone_markets[0]]))
                else:
                    # Multiple markets - get minimum distance
                    distances = dist_df.loc[row['market_id'], other_zone_markets]
                    distance_to_other.append(float(distances.min()))
            except (KeyError, TypeError):
                distance_to_other.append(np.nan)
        
        df['distance_to_other_zone'] = distance_to_other
    
    info(f"Created spatial features for {len(spatial_vars)} variables")
    
    return df


def create_conflict_features(df: pd.DataFrame) -> pd.DataFrame:
    """Create enhanced conflict-based features.
    
    Args:
        df: Input DataFrame with conflict data
        
    Returns:
        DataFrame with conflict features added
    """
    with timer("conflict_features"):
        info("Creating conflict features")
    
    features_created = 0
    
    # Conflict intensity quartiles
    if 'conflict_intensity' in df.columns:
        df['conflict_quartile'] = pd.qcut(
            df['conflict_intensity'], 
            q=4, 
            labels=['Q1_Low', 'Q2_Medium', 'Q3_High', 'Q4_VeryHigh'],
            duplicates='drop'
        )
        features_created += 1
    
    # Event type ratios
    event_types = ['n_battles', 'n_explosions', 'n_violence_civilians']
    if all(col in df.columns for col in event_types):
        total_events = df[event_types].sum(axis=1)
        for event_type in event_types:
            df[f'{event_type}_ratio'] = df[event_type] / (total_events + 1)  # +1 to avoid division by zero
            features_created += 1
    
    # Conflict persistence indicator
    if 'conflict_intensity_lag1' in df.columns:
        df['conflict_persistent'] = (
            (df['conflict_intensity'] > df['conflict_intensity'].median()) & 
            (df['conflict_intensity_lag1'] > df['conflict_intensity_lag1'].median())
        ).astype(int)
        features_created += 1
    
    # Conflict shock indicator
    if 'conflict_intensity_ma3' in df.columns:
        df['conflict_shock'] = (
            df['conflict_intensity'] > 2 * df['conflict_intensity_ma3']
        ).astype(int)
        features_created += 1
    
    # Actor dominance features
    actor_cols = ['n_events_houthis', 'n_events_government', 'n_events_stc']
    if all(col in df.columns for col in actor_cols):
        df['dominant_actor'] = df[actor_cols].idxmax(axis=1).str.replace('n_events_', '')
        features_created += 1
    
    info(f"Created {features_created} conflict features")
    
    return df