"""WFP data processor for Yemen food price and exchange rate data.

This module processes World Food Programme price data, extracting commodity prices,
exchange rates, and market information for Yemen market integration analysis.
"""

from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

from ..config.settings import ANALYSIS_CONFIG, PROCESSED_DATA_DIR, INTERIM_DATA_DIR
from ..utils.logging import (
    bind, context, timer, progress, log_data_shape,
    info, warning, error, debug
)


class WFPProcessor:
    """Process WFP food price data for Yemen market analysis.
    
    This processor handles:
    - Parsing WFP CSV data with proper data types
    - Extracting and standardizing market information
    - Handling exchange rate data (official and parallel)
    - Creating derived features (exchange rate differentials)
    - Handling missing data with appropriate methods
    - Generating analysis-ready datasets
    - Pcode-based matching for better integration
    - Smart commodity filtering (only include if sufficient data)
    - Governorate name standardization
    - Better handling of structural missing data
    
    Attributes:
        commodities: List of commodities to analyze
        start_date: Start date for analysis period
        end_date: End date for analysis period
        min_market_coverage: Minimum required market coverage for commodities
    """
    
    # Governorate name mappings to standard pcode names
    GOVERNORATE_MAPPINGS = {
        "Al Dhale'e": "Ad Dale'",
        "Al Hudaydah": "Al Hodeidah", 
        "Amanat Al Asimah": "Sana'a City",
        "Hadramaut": "Hadramawt",
        "Sa'ada": "Sa'dah",
        "Taizz": "Ta'iz"
    }
    
    # WFP column mappings
    COLUMN_MAPPINGS = {
        'date': 'date',
        'admin1': 'governorate',
        'admin2': 'district', 
        'market': 'market_name',
        'latitude': 'lat',
        'longitude': 'lon',
        'category': 'commodity_category',
        'commodity': 'commodity',
        'unit': 'unit',
        'priceflag': 'price_flag',
        'pricetype': 'price_type',
        'currency': 'currency',
        'price': 'price_local',
        'usdprice': 'price_usd'
    }
    
    # Key commodities for analysis (matching Title Case after processing)
    KEY_COMMODITIES = [
        'Wheat', 'Wheat Flour', 'Rice (Imported)', 'Sugar', 'Oil (Vegetable)',
        'Beans (Kidney Red)', 'Beans (White)', 'Salt', 'Fuel (Diesel)', 'Fuel (Petrol-Gasoline)'
    ]
    
    # Market zones based on control
    HOUTHI_GOVERNORATES = [
        "Sana'a", "Sa'ada", "Hajjah", "Al Mahwit", "Dhamar",
        "Raymah", "Ibb", "Amran", "Al Hudaydah"
    ]
    
    GOVERNMENT_GOVERNORATES = [
        "Aden", "Lahj", "Abyan", "Shabwah", "Hadramaut",
        "Al Maharah", "Socotra", "Al Dhale'e", "Marib", "Al Jawf"
    ]
    
    def __init__(
        self,
        commodities: Optional[List[str]] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        min_market_coverage: float = 0.3
    ):
        """Initialize WFP processor.
        
        Args:
            commodities: List of commodities to analyze. Defaults to config.
            start_date: Start date for analysis. Defaults to config.
            end_date: End date for analysis. Defaults to config.
            min_market_coverage: Minimum fraction of markets a commodity must appear in (default 0.3)
        """
        # Standardize commodities list: use provided list, config value (Title Case), or default KEY_COMMODITIES
        if commodities is not None:
            self.commodities = [c.strip().title() for c in commodities]
        else:
            config_comms = ANALYSIS_CONFIG.get('commodities')
            if config_comms:
                self.commodities = [c.strip().title() for c in config_comms]
            else:
                self.commodities = self.KEY_COMMODITIES
        self.start_date = pd.to_datetime(start_date or ANALYSIS_CONFIG.get('start_date'))
        self.end_date = pd.to_datetime(end_date or ANALYSIS_CONFIG.get('end_date'))
        self.min_market_coverage = min_market_coverage
        
        # Load pcode mappings if available
        self._load_pcode_mappings()
        
        info(
            "WFP processor initialized",
            commodities=len(self.commodities),
            start_date=str(self.start_date),
            end_date=str(self.end_date),
            min_market_coverage=f"{min_market_coverage*100:.0f}%"
        )
    
    def _load_pcode_mappings(self):
        """Load Yemen administrative pcode mappings if available."""
        from ..config.settings import RAW_DATA_DIR
        pcode_file = RAW_DATA_DIR / "hdx/cod-ab-yem/yem_admin_pcodes-02122024.xlsx"
        
        if pcode_file.exists():
            try:
                # Load governorate pcodes
                self.gov_pcodes = pd.read_excel(pcode_file, sheet_name="Admin1")
                self.gov_to_pcode = dict(zip(
                    self.gov_pcodes['Eng_Name'], 
                    self.gov_pcodes['Gov_Pcode']
                ))
                
                # Load district pcodes
                self.dist_pcodes = pd.read_excel(pcode_file, sheet_name="Admin2")
                
                info("Loaded pcode mappings",
                     governorates=len(self.gov_pcodes),
                     districts=len(self.dist_pcodes))
            except Exception as e:
                warning(f"Could not load pcode mappings: {e}")
                self.gov_pcodes = None
                self.dist_pcodes = None
                self.gov_to_pcode = {}
        else:
            debug("Pcode file not found, proceeding without pcodes")
            self.gov_pcodes = None
            self.dist_pcodes = None
            self.gov_to_pcode = {}
    
    def process_raw_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process raw WFP data with proper types and cleaning.
        
        Args:
            df: Raw WFP DataFrame
            
        Returns:
            Processed DataFrame with standardized columns
        """
        with timer("process_raw_wfp_data"):
            log_data_shape("raw_wfp_data", df)
        
        # Create a copy to avoid modifying original
        processed = df.copy()
        
        # Rename columns to standard names
        rename_dict = {k: v for k, v in self.COLUMN_MAPPINGS.items() if k in processed.columns}
        processed = processed.rename(columns=rename_dict)
        
        # Convert date column
        processed['date'] = pd.to_datetime(processed['date'])
        
        # Clean numeric columns (handle mixed types)
        numeric_cols = ['price_local', 'price_usd', 'lat', 'lon']
        for col in numeric_cols:
            if col in processed.columns:
                processed[col] = pd.to_numeric(processed[col], errors='coerce')
        
        # Standardize text columns
        text_cols = ['governorate', 'district', 'market_name', 'commodity', 'commodity_category']
        for col in text_cols:
            if col in processed.columns:
                processed[col] = processed[col].str.strip().str.title()
        
        # Apply governorate name standardization
        if 'governorate' in processed.columns:
            processed['governorate'] = processed['governorate'].replace(self.GOVERNORATE_MAPPINGS)
            
            # Add pcode if available
            if hasattr(self, 'gov_to_pcode') and self.gov_to_pcode:
                processed['gov_pcode'] = processed['governorate'].map(self.gov_to_pcode)
        
        # Filter by date range
        processed = processed[
            (processed['date'] >= self.start_date) & 
            (processed['date'] <= self.end_date)
        ]
        
        # Add year-month column for aggregation
        processed['year_month'] = processed['date'].dt.to_period('M')
        
        log_data_shape("processed_wfp_data", processed)
        
        return processed
    
    def extract_exchange_rates(self, df: pd.DataFrame) -> pd.DataFrame:
        """Extract exchange rate data from price data.
        
        WFP data includes exchange rates as the ratio of local to USD prices.
        This method calculates implicit exchange rates from commodity prices.
        
        Args:
            df: Processed WFP DataFrame
            
        Returns:
            DataFrame with exchange rates by market and date
        """
        with timer("extract_exchange_rates"):
            info("Extracting exchange rates from price data")
        
        # Filter for valid price data
        valid_prices = df[
            (df['price_local'].notna()) & 
            (df['price_usd'].notna()) & 
            (df['price_usd'] > 0)
        ].copy()
        
        # Calculate implicit exchange rate
        valid_prices['exchange_rate'] = valid_prices['price_local'] / valid_prices['price_usd']
        
        # Remove outliers (likely data errors)
        # Yemen exchange rates typically between 200-1500 YER/USD
        valid_prices = valid_prices[
            (valid_prices['exchange_rate'] >= 100) & 
            (valid_prices['exchange_rate'] <= 2000)
        ]
        
        # Aggregate by market and month (median to handle outliers)
        exchange_rates = valid_prices.groupby(
            ['year_month', 'governorate', 'market_name']
        )['exchange_rate'].agg(['median', 'count', 'std']).reset_index()
        
        exchange_rates.columns = ['year_month', 'governorate', 'market_name', 
                                  'exchange_rate', 'n_observations', 'exchange_rate_std']
        
        # Add zone classification
        exchange_rates['control_zone'] = exchange_rates['governorate'].apply(
            lambda x: 'Houthi' if x in self.HOUTHI_GOVERNORATES 
            else 'Government' if x in self.GOVERNMENT_GOVERNORATES 
            else 'Contested'
        )
        
        log_data_shape("exchange_rates", exchange_rates)
        info(
            "Exchange rates extracted",
            market_months=len(exchange_rates),
            zones=exchange_rates['control_zone'].value_counts().to_dict()
        )
        
        return exchange_rates
    
    def extract_commodity_prices(self, df: pd.DataFrame) -> pd.DataFrame:
        """Extract raw commodity price data for panel building.
        
        Args:
            df: Processed WFP DataFrame
            
        Returns:
            DataFrame with commodity prices by market and date
        """
        with timer("extract_commodity_prices"):
            info("Extracting commodity prices", commodities=self.commodities)
        
        # Filter for key commodities (prices already cleaned in process_raw_data)
        debug(f"Looking for commodities: {self.commodities}")
        debug(f"Available commodities: {df['commodity'].unique()[:20]}")
        commodity_prices = df[df['commodity'].isin(self.commodities)].copy()
        
        # Select relevant columns
        price_cols = [
            'date', 'year_month', 'governorate', 'district', 'market_name',
            'lat', 'lon', 'commodity', 'unit', 'price_local', 'price_usd',
            'currency', 'price_type'
        ]
        
        # Keep only columns that exist
        price_cols = [col for col in price_cols if col in commodity_prices.columns]
        commodity_prices = commodity_prices[price_cols]
        
        # Add market ID
        commodity_prices['market_id'] = (
            commodity_prices['governorate'] + '_' + 
            commodity_prices['market_name']
        ).str.replace(' ', '_')
        
        # Sort by market, commodity, and date
        commodity_prices = commodity_prices.sort_values(
            ['market_id', 'commodity', 'date']
        )
        
        log_data_shape("commodity_prices", commodity_prices)
        info(
            "Commodity prices extracted",
            records=len(commodity_prices),
            markets=commodity_prices['market_id'].nunique(),
            commodities=commodity_prices['commodity'].nunique()
        )
        
        return commodity_prices
    
    def calculate_price_indices(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate commodity price indices by market.
        
        Args:
            df: Processed WFP DataFrame
            
        Returns:
            DataFrame with price indices
        """
        with timer("calculate_price_indices"):
            info("Calculating price indices", commodities=self.commodities)
        
        # Filter for key commodities
        commodity_prices = df[df['commodity'].isin(self.commodities)].copy()
        
        # Get monthly median prices by market and commodity
        monthly_prices = commodity_prices.groupby(
            ['year_month', 'governorate', 'market_name', 'commodity']
        )['price_usd'].median().reset_index()
        
        # Calculate base period (first 3 months) prices
        base_period = monthly_prices['year_month'].min()
        base_prices = monthly_prices[
            monthly_prices['year_month'] <= base_period + 2
        ].groupby(['governorate', 'market_name', 'commodity'])['price_usd'].mean()
        
        # Merge base prices
        monthly_prices = monthly_prices.merge(
            base_prices.reset_index().rename(columns={'price_usd': 'base_price'}),
            on=['governorate', 'market_name', 'commodity'],
            how='left'
        )
        
        # Calculate price index (base = 100)
        monthly_prices['price_index'] = (
            monthly_prices['price_usd'] / monthly_prices['base_price'] * 100
        )
        
        # Create composite index (simple average)
        composite_index = monthly_prices.groupby(
            ['year_month', 'governorate', 'market_name']
        )['price_index'].mean().reset_index()
        
        composite_index.rename(columns={'price_index': 'composite_index'}, inplace=True)
        
        info(
            "Price indices calculated",
            n_markets=composite_index['market_name'].nunique(),
            n_observations=len(composite_index)
        )
        
        return monthly_prices, composite_index
    
    def create_market_panel(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create balanced panel dataset for market analysis.
        
        Args:
            df: Processed WFP DataFrame
            
        Returns:
            Balanced panel DataFrame
        """
        with timer("create_market_panel"):
            info("Creating balanced panel dataset")
        
        # Get exchange rates
        exchange_rates = self.extract_exchange_rates(df)
        
        # Get price indices
        price_data, composite_index = self.calculate_price_indices(df)
        
        # Get market coordinates from original data
        market_coords = df[['governorate', 'market_name', 'lat', 'lon']].drop_duplicates()
        market_coords = market_coords[market_coords['lat'].notna() & market_coords['lon'].notna()]
        
        # Start with exchange rate data as base
        panel = exchange_rates.copy()
        
        # Merge market coordinates
        panel = panel.merge(
            market_coords,
            on=['governorate', 'market_name'],
            how='left'
        )
        
        # Merge composite price index
        panel = panel.merge(
            composite_index,
            on=['year_month', 'governorate', 'market_name'],
            how='left'
        )
        
        # Calculate zone-level aggregates
        zone_exchange = exchange_rates.groupby(
            ['year_month', 'control_zone']
        )['exchange_rate'].median().reset_index()
        
        zone_exchange = zone_exchange.pivot(
            index='year_month',
            columns='control_zone',
            values='exchange_rate'
        ).reset_index()
        
        # Calculate exchange rate differential
        if 'Houthi' in zone_exchange.columns and 'Government' in zone_exchange.columns:
            zone_exchange['exchange_diff'] = (
                zone_exchange['Houthi'] - zone_exchange['Government']
            )
            zone_exchange['exchange_diff_pct'] = (
                zone_exchange['exchange_diff'] / zone_exchange['Government'] * 100
            )
        
        # Merge zone-level data if exchange differential exists
        if 'exchange_diff' in zone_exchange.columns:
            panel = panel.merge(
                zone_exchange[['year_month', 'exchange_diff', 'exchange_diff_pct']],
                on='year_month',
                how='left'
            )
        
        # Add time variables
        panel['year'] = panel['year_month'].dt.year
        panel['month'] = panel['year_month'].dt.month
        panel['quarter'] = panel['year_month'].dt.quarter
        
        # Sort by market and time
        panel = panel.sort_values(['market_name', 'year_month'])
        
        log_data_shape("market_panel", panel)
        info(
            "Panel created",
            observations=len(panel),
            markets=panel['market_name'].nunique(),
            time_periods=panel['year_month'].nunique(),
            markets_with_coords=panel['lat'].notna().sum() // panel['year_month'].nunique()
        )
        
        return panel
    
    def _analyze_commodity_coverage(self, df: pd.DataFrame) -> pd.Series:
        """Analyze market coverage for each commodity.
        
        Args:
            df: DataFrame with commodity prices
            
        Returns:
            Series with coverage percentage for each commodity
        """
        total_markets = df['market_id'].nunique()
        coverage = df.groupby('commodity')['market_id'].nunique() / total_markets
        return coverage.sort_values(ascending=False)
    
    def process_price_data(self, raw_data_path: Optional[str] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Process WFP price data with enhanced filtering and pcode support.
        
        This is the main entry point for enhanced processing that includes:
        - Automatic commodity selection based on coverage
        - Pcode integration
        - Smart panel creation
        
        Args:
            raw_data_path: Optional path to raw WFP CSV file
            
        Returns:
            Tuple of (commodity_prices_df, exchange_rates_df)
        """
        with timer("process_enhanced_wfp_data"):
            info("Processing WFP data with enhanced methods")
        
        # Load raw data
        from ..config.settings import RAW_DATA_DIR
        if raw_data_path is None:
            raw_data_path = RAW_DATA_DIR / "hdx/wfp-food-prices-for-yemen/wfp_food_prices_202505.csv"
        
        df = pd.read_csv(raw_data_path, dtype=str, low_memory=False)
        
        # Skip header rows
        df = df[df['market'] != '#adm2+name']
        
        # Convert numeric columns
        numeric_cols = ['price', 'usdprice', 'latitude', 'longitude']
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Parse dates
        df['date'] = pd.to_datetime(df['date'], errors='coerce')
        df = df.dropna(subset=['date'])
        
        # Filter by date
        df = df[df['date'] >= self.start_date]
        
        log_data_shape("filtered_by_date", df)
        
        # Standardize governorate names
        df['admin1'] = df['admin1'].replace(self.GOVERNORATE_MAPPINGS)
        
        # Add pcodes if available
        if hasattr(self, 'gov_pcodes') and self.gov_pcodes is not None:
            df = df.merge(
                self.gov_pcodes[['Eng_Name', 'Gov_Pcode']],
                left_on='admin1',
                right_on='Eng_Name',
                how='left'
            )
            df.rename(columns={'Gov_Pcode': 'admin1_pcode'}, inplace=True)
            df.drop('Eng_Name', axis=1, inplace=True)
            
            info("Added governorate pcodes",
                 matched=df['admin1_pcode'].notna().sum(),
                 total=len(df),
                 match_rate=f"{df['admin1_pcode'].notna().sum()/len(df)*100:.1f}%")
        
        # Create standardized market ID using underscores
        df['market_id'] = (df['admin1'] + '_' + df['market']).str.replace(' ', '_')
        
        # Separate exchange rates and commodity prices
        exchange_mask = df['commodity'] == 'Exchange rate (unofficial)'
        exchange_rates_df = df[exchange_mask].copy()
        commodity_prices_df = df[~exchange_mask].copy()
        
        # Smart commodity selection based on market coverage
        if self.commodities is None:
            commodity_coverage = self._analyze_commodity_coverage(commodity_prices_df)
            self.commodities = commodity_coverage[
                commodity_coverage >= self.min_market_coverage
            ].index.tolist()
            
            info("Auto-selected commodities based on coverage",
                 selected=len(self.commodities),
                 total=commodity_coverage.shape[0],
                 threshold=f"{self.min_market_coverage*100:.0f}%")
            
            # Log selected commodities
            for comm in sorted(self.commodities):
                cov = commodity_coverage[comm]
                info(f"  {comm}: {cov*100:.1f}% market coverage")
        
        # Filter to selected commodities
        commodity_prices_df = commodity_prices_df[
            commodity_prices_df['commodity'].isin(self.commodities)
        ]
        
        # Process exchange rates
        exchange_rates_df = self._process_exchange_rates(exchange_rates_df)
        
        # Process commodity prices
        commodity_prices_df = self._process_commodity_prices(commodity_prices_df)
        
        return commodity_prices_df, exchange_rates_df
    
    def _process_exchange_rates(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process exchange rate data with enhanced features.
        
        Args:
            df: Raw exchange rate data
            
        Returns:
            Processed exchange rate DataFrame
        """
        if df.empty:
            warning("No exchange rate data found")
            return df
        
        # Rename columns for clarity
        df = df.rename(columns={
            'price': 'official_rate',
            'usdprice': 'parallel_rate'
        })
        
        # Add exchange rate features
        df['exchange_rate'] = df['parallel_rate']  # Primary rate for analysis
        df['rate_differential'] = df['parallel_rate'] - df['official_rate']
        df['rate_premium'] = (df['rate_differential'] / df['official_rate'] * 100)
        
        # Keep relevant columns
        keep_cols = [
            'date', 'market_id', 'admin1', 'admin2', 'market',
            'latitude', 'longitude',
            'official_rate', 'parallel_rate', 'exchange_rate',
            'rate_differential', 'rate_premium'
        ]
        
        if 'admin1_pcode' in df.columns:
            keep_cols.append('admin1_pcode')
        
        df = df[keep_cols].copy()
        
        # Add time dimensions
        df['year_month'] = df['date'].dt.to_period('M')
        
        log_data_shape("processed_exchange_rates", df)
        
        return df
    
    def _process_commodity_prices(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process commodity price data with enhanced standardization.
        
        Args:
            df: Raw commodity price data
            
        Returns:
            Processed commodity price DataFrame
        """
        # Standardize commodity names  
        df['commodity'] = df['commodity'].str.strip().str.title()
        
        # Apply special case corrections
        commodity_corrections = {
            'Beans (Kidney Red)': 'Beans (Kidney Red)',
            'Rice (Imported)': 'Rice (Imported)',
            'Oil (Vegetable)': 'Oil (Vegetable)',
            'Fuel (Petrol-Gasoline)': 'Fuel (Petrol-Gasoline)',
            'Peas (Yellow, Split)': 'Peas (Yellow, Split)'
        }
        
        for old, new in commodity_corrections.items():
            df.loc[df['commodity'] == old.title(), 'commodity'] = new
        
        # Keep relevant columns
        keep_cols = [
            'date', 'market_id', 'admin1', 'admin2', 'market',
            'latitude', 'longitude', 'commodity', 'unit',
            'price', 'usdprice', 'currency', 'pricetype'
        ]
        
        if 'admin1_pcode' in df.columns:
            keep_cols.append('admin1_pcode')
        
        df = df[keep_cols].copy()
        
        # Rename for consistency
        df = df.rename(columns={
            'admin1': 'governorate',
            'admin2': 'district', 
            'market': 'market_name',
            'latitude': 'lat',
            'longitude': 'lon',
            'price': 'price_local',
            'usdprice': 'price_usd',
            'pricetype': 'price_type'
        })
        
        # Add time dimensions
        df['year_month'] = df['date'].dt.to_period('M')
        
        log_data_shape("processed_commodity_prices", df)
        
        return df
    
    def create_smart_panels(self, commodity_df: pd.DataFrame) -> pd.DataFrame:
        """Create smart panel data that respects commodity availability.
        
        Instead of forcing all commodities into all markets, this creates
        panels based on actual data availability.
        
        Args:
            commodity_df: DataFrame with commodity prices
            
        Returns:
            Smart panel DataFrame with better coverage
        """
        with timer("create_smart_panels"):
            info("Creating smart panel structure")
        
        panels = []
        
        # Process each commodity separately
        with progress("Processing commodities", total=len(self.commodities)) as update:
            for commodity in self.commodities:
                comm_data = commodity_df[commodity_df['commodity'] == commodity].copy()
                
                if comm_data.empty:
                    update(1)
                    continue
                
                # Get markets that actually sell this commodity
                comm_markets = comm_data['market_id'].unique()
                
                # Create balanced panel for this commodity and its markets
                date_min = comm_data['date'].min()
                date_max = comm_data['date'].max()
                
                # Monthly data on 15th
                all_dates = pd.date_range(
                    date_min.replace(day=15),
                    date_max,
                    freq='MS'
                ) + pd.Timedelta(days=14)
                
                # Create index for this commodity
                index = pd.MultiIndex.from_product(
                    [[commodity], comm_markets, all_dates],
                    names=['commodity', 'market_id', 'date']
                )
                
                # Create panel
                panel = pd.DataFrame(index=index).reset_index()
                
                # Merge with actual data
                panel = panel.merge(
                    comm_data,
                    on=['commodity', 'market_id', 'date'],
                    how='left'
                )
                
                # Fill time-invariant columns
                for col in ['governorate', 'district', 'market_name', 'lat', 'lon', 'gov_pcode']:
                    if col in panel.columns:
                        panel[col] = panel.groupby('market_id')[col].transform(
                            lambda x: x.fillna(method='ffill').fillna(method='bfill')
                        )
                
                panels.append(panel)
                update(1)
        
        # Combine all panels
        smart_panel = pd.concat(panels, ignore_index=True)
        
        # Add year_month for consistency
        smart_panel['year_month'] = smart_panel['date'].dt.to_period('M')
        
        log_data_shape("smart_panel", smart_panel)
        
        # Report coverage
        coverage = smart_panel.groupby('commodity')['price_usd'].apply(
            lambda x: x.notna().sum() / len(x) * 100
        ).sort_values(ascending=False)
        
        info("Price coverage by commodity:")
        for comm, cov in coverage.items():
            info(f"  {comm}: {cov:.1f}%")
        
        overall_coverage = smart_panel['price_usd'].notna().sum() / len(smart_panel) * 100
        info(f"Overall price coverage: {overall_coverage:.1f}%")
        
        return smart_panel
    
    def handle_missing_data(
        self, 
        df: pd.DataFrame, 
        method: str = 'interpolate'
    ) -> pd.DataFrame:
        """Handle missing data in panel dataset.
        
        Args:
            df: Panel DataFrame
            method: Method for handling missing data ('interpolate', 'forward_fill', 'drop')
            
        Returns:
            DataFrame with missing data handled
        """
        with timer(f"handle_missing_data_{method}"):
            info("Handling missing data", method=method)
            
            # Report missing data
            missing_before = df.isnull().sum()
            missing_cols = missing_before[missing_before > 0]
            if not missing_cols.empty:
                info("Missing values before handling", missing=missing_cols.to_dict())
        
        if method == 'interpolate':
            # Interpolate within each market
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            df[numeric_cols] = df.groupby('market_name')[numeric_cols].transform(
                lambda x: x.interpolate(method='linear', limit=3)
            )
        
        elif method == 'forward_fill':
            # Forward fill within each market
            df = df.groupby('market_name').fillna(method='ffill', limit=2)
        
        elif method == 'drop':
            # Drop markets with too many missing values
            market_missing = df.groupby('market_name')['exchange_rate'].apply(
                lambda x: x.isnull().sum() / len(x)
            )
            valid_markets = market_missing[market_missing < 0.3].index
            df = df[df['market_name'].isin(valid_markets)]
        
        # Report missing data after
        missing_after = df.isnull().sum()
        missing_cols_after = missing_after[missing_after > 0]
        if not missing_cols_after.empty:
            info("Missing values after handling", missing=missing_cols_after.to_dict())
        
        return df
    
    def save_processed_data(
        self, 
        panel_df: pd.DataFrame,
        exchange_df: pd.DataFrame,
        price_df: pd.DataFrame,
        raw_prices_df: Optional[pd.DataFrame] = None,
        smart_panel_df: Optional[pd.DataFrame] = None
    ):
        """Save processed datasets to disk.
        
        Args:
            panel_df: Main panel dataset
            exchange_df: Exchange rate dataset
            price_df: Price index dataset
            raw_prices_df: Raw commodity price data
            smart_panel_df: Smart panel data (optional)
        """
        # Create output directories
        PROCESSED_DATA_DIR.mkdir(parents=True, exist_ok=True)
        INTERIM_DATA_DIR.mkdir(parents=True, exist_ok=True)
        
        # Save main panel
        panel_path = PROCESSED_DATA_DIR / 'wfp_market_panel.parquet'
        panel_df.to_parquet(panel_path, index=False)
        info("Saved panel data", path=str(panel_path), size=len(panel_df))
        
        # Save exchange rates
        exchange_path = INTERIM_DATA_DIR / 'exchange_rates.parquet'
        exchange_df.to_parquet(exchange_path, index=False)
        info("Saved exchange rates", path=str(exchange_path), size=len(exchange_df))
        
        # Save price indices
        price_path = INTERIM_DATA_DIR / 'price_indices.parquet'
        price_df.to_parquet(price_path, index=False)
        info("Saved price indices", path=str(price_path), size=len(price_df))
        
        # Save raw commodity prices if provided
        if raw_prices_df is not None and not raw_prices_df.empty:
            raw_price_path = PROCESSED_DATA_DIR / 'wfp_commodity_prices.parquet'
            raw_prices_df.to_parquet(raw_price_path, index=False)
            info("Saved raw commodity prices", path=str(raw_price_path), size=len(raw_prices_df))
        
        # Save smart panel if provided
        if smart_panel_df is not None and not smart_panel_df.empty:
            smart_panel_path = PROCESSED_DATA_DIR / 'wfp_smart_panel.parquet'
            smart_panel_df.to_parquet(smart_panel_path, index=False)
            info("Saved smart panel data", path=str(smart_panel_path), size=len(smart_panel_df))
        
        # Save summary statistics
        summary_path = PROCESSED_DATA_DIR / 'wfp_summary_stats.csv'
        summary = self.generate_summary_statistics(panel_df)
        summary.to_csv(summary_path)
        info("Saved summary statistics", path=str(summary_path))
        
        # Save enhanced summary JSON
        self._save_summary_stats(raw_prices_df, exchange_df, smart_panel_df or panel_df)
    
    def _save_summary_stats(self, commodity_df: Optional[pd.DataFrame], exchange_df: pd.DataFrame, panel_df: pd.DataFrame):
        """Generate and save enhanced summary statistics in JSON format.
        
        Args:
            commodity_df: Commodity price data
            exchange_df: Exchange rate data  
            panel_df: Panel data (smart or regular)
        """
        summary = {
            'processing_date': datetime.now().isoformat(),
            'date_range': {
                'start': str(self.start_date),
                'end': str(self.end_date)
            },
            'observations': {
                'exchange_rates': int(len(exchange_df)) if not exchange_df.empty else 0,
                'panel_total': int(len(panel_df)),
            }
        }
        
        # Add commodity info if available
        if commodity_df is not None and not commodity_df.empty:
            summary['markets'] = {
                'total': int(commodity_df['market_id'].nunique()),
                'by_governorate': {k: int(v) for k, v in commodity_df.groupby('governorate')['market_id'].nunique().to_dict().items()}
            }
            summary['commodities'] = {
                'selected': len(self.commodities),
                'list': self.commodities
            }
            summary['observations']['commodity_prices'] = int(len(commodity_df))
        
        # Add panel coverage info
        if 'price_usd' in panel_df.columns:
            summary['observations']['panel_with_prices'] = int(panel_df['price_usd'].notna().sum())
            summary['coverage'] = {
                'overall': f"{panel_df['price_usd'].notna().sum() / len(panel_df) * 100:.1f}%"
            }
            
            # By commodity coverage if available
            if 'commodity' in panel_df.columns:
                summary['coverage']['by_commodity'] = {
                    comm: f"{(panel_df[panel_df['commodity'] == comm]['price_usd'].notna().sum() / len(panel_df[panel_df['commodity'] == comm]) * 100):.1f}%"
                    for comm in panel_df['commodity'].unique() if comm in self.commodities
                }
        
        import json
        summary_path = PROCESSED_DATA_DIR / "wfp_processing_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        info(f"Saved processing summary: {summary_path}")
    
    def generate_summary_statistics(self, df: pd.DataFrame) -> pd.DataFrame:
        """Generate summary statistics for the processed data.
        
        Args:
            df: Processed panel DataFrame
            
        Returns:
            DataFrame with summary statistics
        """
        summary_stats = []
        
        # Overall statistics
        summary_stats.append({
            'Category': 'Overall',
            'Metric': 'Total observations',
            'Value': len(df)
        })
        
        summary_stats.append({
            'Category': 'Overall',
            'Metric': 'Number of markets',
            'Value': df['market_name'].nunique()
        })
        
        summary_stats.append({
            'Category': 'Overall',
            'Metric': 'Time period',
            'Value': f"{df['year_month'].min()} to {df['year_month'].max()}"
        })
        
        # By control zone
        for zone in df['control_zone'].unique():
            zone_data = df[df['control_zone'] == zone]
            
            summary_stats.append({
                'Category': f'{zone} zone',
                'Metric': 'Number of markets',
                'Value': zone_data['market_name'].nunique()
            })
            
            summary_stats.append({
                'Category': f'{zone} zone',
                'Metric': 'Avg exchange rate',
                'Value': f"{zone_data['exchange_rate'].mean():.1f}"
            })
        
        # Exchange rate differential
        if 'exchange_diff_pct' in df.columns:
            summary_stats.append({
                'Category': 'Exchange rate',
                'Metric': 'Avg differential (%)',
                'Value': f"{df['exchange_diff_pct'].mean():.1f}"
            })
            
            summary_stats.append({
                'Category': 'Exchange rate',
                'Metric': 'Max differential (%)',
                'Value': f"{df['exchange_diff_pct'].max():.1f}"
            })
        
        return pd.DataFrame(summary_stats)
    
    def process_wfp_data(self, raw_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Main processing pipeline for WFP data.
        
        Args:
            raw_df: Raw WFP DataFrame from HDXClient
            
        Returns:
            Tuple of (panel_data, exchange_rates, price_indices)
        """
        with timer("complete_wfp_pipeline"):
            info("Starting WFP data processing pipeline")
        
        # Process raw data
        processed = self.process_raw_data(raw_df)
        
        # Extract raw commodity prices
        commodity_prices = self.extract_commodity_prices(processed)
        
        # Extract exchange rates
        exchange_rates = self.extract_exchange_rates(processed)
        
        # Calculate price indices
        price_indices, composite_index = self.calculate_price_indices(processed)
        
        # Create panel dataset
        panel = self.create_market_panel(processed)
        
        # Handle missing data
        panel = self.handle_missing_data(panel)
        
        # Save all datasets including raw prices
        self.save_processed_data(panel, exchange_rates, price_indices, commodity_prices)
        
        info("WFP data processing complete")
        
        return panel, exchange_rates, price_indices