# Yemen Market Integration - EDA Phase Implementation

## Project Context
I'm working on the Yemen Market Integration econometric analysis project. I've completed the data pipeline (Week 1-2) with 100% implementation and ~90% test coverage. Now I'm starting Week 3-4: Exploratory Data Analysis & Feature Engineering.

**Project Location**: `/Users/<USER>/Documents/GitHub/yemen-market-integration/`

## Current Status
- ✅ Data Pipeline Complete: HDXClient, WFPProcessor, ACAPSProcessor, SpatialJoiner, PanelBuilder
- ✅ All unit tests passing with high coverage
- 🎯 Starting EDA phase to analyze patterns before modeling

## Immediate Tasks

### 1. First, help me run the data pipeline to download and process real data:
```bash
# I need to execute these scripts in order:
python scripts/download_data.py
python scripts/process_wfp_data.py
python scripts/process_acaps_data.py
python scripts/run_spatial_joins.py
python scripts/build_panel_datasets.py
```

### 2. Create the visualization module at `src/yemen_market/visualization/price_dynamics.py`:
```python
"""
Implement a PriceDynamicsVisualizer class with methods for:
- plot_price_series(): Time series of commodity prices across markets
- plot_exchange_differential(): Exchange rate differentials between control zones
- plot_spatial_price_map(): Geographic heatmap of prices
- plot_price_convergence(): Analyze price convergence patterns
- plot_structural_breaks(): Identify regime changes

Use matplotlib, seaborn, and geopandas. Follow the coding style in our existing modules.
"""
```

### 3. Create notebook `notebooks/01-data-validation.ipynb` that:
- Loads the integrated panel from `data/processed/panels/integrated_panel.parquet`
- Checks data quality, coverage, and missing patterns
- Validates exchange rate differentials between Houthi and Government zones
- Creates summary statistics tables
- Identifies any data anomalies

### 4. Create notebook `notebooks/02-price-patterns.ipynb` that:
- Analyzes price transmission between markets
- Calculates price correlations within/across control zones
- Identifies potential threshold effects visually
- Maps exchange rate impact on commodity prices
- Detects structural breaks (especially around 2017 Central Bank move)

## Key Analysis Questions
1. **Exchange Rate Differentials**: What's the maximum spread and when do rates converge/diverge?
2. **Price Leadership**: Which markets lead price changes?
3. **Spatial Clustering**: Do prices cluster by control zones?
4. **Threshold Effects**: At what price differential does arbitrage occur?

## Technical Requirements
- Use our existing project structure and imports
- Follow NumPy-style docstrings
- Add type hints to all functions
- Create reusable visualization functions
- Handle missing data appropriately
- Use the logging configuration from `src/yemen_market/utils/logging.py`

## Data Paths
- Integrated panel: `data/processed/panels/integrated_panel.parquet`
- Exchange rates: `data/processed/wfp/exchange_rates.parquet`
- Market zones: `data/processed/spatial/market_zones_temporal.parquet`
- Price indices: `data/processed/panels/price_indices.parquet`

## Expected Outputs
1. Working visualization module with comprehensive plotting functions
2. Two analysis notebooks with findings
3. Identified patterns ready for econometric modeling
4. Visual evidence of threshold effects and structural breaks

Please start by checking if the data pipeline scripts exist and are ready to run, then help me execute them to get real data before starting the analysis.
