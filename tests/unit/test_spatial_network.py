"""Unit tests for spatial network components."""

import pytest
import numpy as np
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point
from unittest.mock import Mock, patch, MagicMock

from yemen_market.models.track1_complex.spatial_network import SpatialWeightMatrix


class TestSpatialWeightMatrix:
    """Test suite for SpatialWeightMatrix class."""
    
    @pytest.fixture
    def sample_locations(self):
        """Create sample market locations."""
        # Create a simple grid of markets
        locations = {
            'market_1': (44.0, 13.0),  # Aden
            'market_2': (44.2, 13.8),  # Taiz
            'market_3': (43.9, 15.3),  # Sanaa
            'market_4': (45.5, 14.5),  # Marib
            'market_5': (49.1, 14.0)   # Hadramaut
        }
        
        # Create GeoDataFrame
        geometry = [Point(lon, lat) for lon, lat in locations.values()]
        gdf = gpd.GeoDataFrame(
            {'market': list(locations.keys())},
            geometry=geometry,
            crs='EPSG:4326'
        )
        
        return gdf
    
    @pytest.fixture
    def road_network(self):
        """Create sample road network data."""
        # Simplified connectivity matrix (1 = connected, 0 = not connected)
        markets = ['market_1', 'market_2', 'market_3', 'market_4', 'market_5']
        n = len(markets)
        
        # Create adjacency matrix
        adjacency = np.zeros((n, n))
        # Connect adjacent markets
        connections = [(0, 1), (1, 2), (2, 3), (3, 4), (0, 4)]  # Circular connections
        for i, j in connections:
            adjacency[i, j] = 1
            adjacency[j, i] = 1
        
        return pd.DataFrame(adjacency, index=markets, columns=markets)
    
    @pytest.fixture
    def spatial_weight(self, sample_locations):
        """Create SpatialWeightMatrix instance."""
        return SpatialWeightMatrix(sample_locations)
    
    def test_initialization(self, spatial_weight, sample_locations):
        """Test initialization."""
        assert spatial_weight.locations.equals(sample_locations)
        assert spatial_weight.n_locations == len(sample_locations)
        assert spatial_weight.weight_matrix is None
    
    def test_distance_weight_matrix(self, spatial_weight):
        """Test distance-based weight matrix creation."""
        W = spatial_weight.create_weight_matrix(
            weight_type='distance',
            threshold=200  # km
        )
        
        assert isinstance(W, np.ndarray)
        assert W.shape == (5, 5)
        assert np.allclose(W.diagonal(), 0)  # No self-connections
        assert np.allclose(W, W.T)  # Symmetric
        assert np.all(W >= 0)  # Non-negative weights
    
    def test_contiguity_weight_matrix(self, spatial_weight, sample_locations):
        """Test contiguity-based weight matrix."""
        # Mock contiguity detection
        with patch.object(spatial_weight, '_detect_contiguity') as mock_detect:
            mock_detect.return_value = np.array([
                [0, 1, 0, 0, 0],
                [1, 0, 1, 0, 0],
                [0, 1, 0, 1, 0],
                [0, 0, 1, 0, 1],
                [0, 0, 0, 1, 0]
            ])
            
            W = spatial_weight.create_weight_matrix(weight_type='contiguity')
            
            assert W.shape == (5, 5)
            assert np.array_equal(W, mock_detect.return_value)
    
    def test_network_weight_matrix(self, spatial_weight, road_network):
        """Test network-based weight matrix."""
        W = spatial_weight.create_weight_matrix(
            weight_type='network',
            network_data=road_network
        )
        
        assert W.shape == (5, 5)
        assert np.array_equal(W, road_network.values)
    
    def test_hybrid_weight_matrix(self, spatial_weight, road_network):
        """Test hybrid weight matrix."""
        W = spatial_weight.create_weight_matrix(
            weight_type='hybrid',
            distance_weight=0.5,
            network_weight=0.5,
            network_data=road_network,
            threshold=500
        )
        
        assert W.shape == (5, 5)
        assert np.allclose(W.diagonal(), 0)
        assert np.allclose(W, W.T)
    
    def test_row_standardization(self, spatial_weight):
        """Test row standardization."""
        W = spatial_weight.create_weight_matrix(
            weight_type='distance',
            threshold=200,
            row_standardize=True
        )
        
        # Check row sums equal 1 (except isolated nodes)
        row_sums = W.sum(axis=1)
        non_isolated = row_sums > 0
        assert np.allclose(row_sums[non_isolated], 1)
    
    def test_calculate_spatial_lag(self, spatial_weight):
        """Test spatial lag calculation."""
        # Create weight matrix
        W = spatial_weight.create_weight_matrix(
            weight_type='distance',
            threshold=200
        )
        
        # Create sample values
        values = pd.Series(
            [100, 110, 105, 120, 115],
            index=['market_1', 'market_2', 'market_3', 'market_4', 'market_5']
        )
        
        # Calculate spatial lag
        lag = spatial_weight.calculate_spatial_lag(values)
        
        assert isinstance(lag, pd.Series)
        assert len(lag) == len(values)
        assert lag.index.equals(values.index)
    
    def test_morans_i(self, spatial_weight):
        """Test Moran's I calculation."""
        # Create weight matrix
        W = spatial_weight.create_weight_matrix(
            weight_type='distance',
            threshold=300
        )
        
        # Create sample values with spatial autocorrelation
        values = pd.Series(
            [100, 102, 98, 105, 103],
            index=['market_1', 'market_2', 'market_3', 'market_4', 'market_5']
        )
        
        # Calculate Moran's I
        moran_result = spatial_weight.calculate_morans_i(values)
        
        assert isinstance(moran_result, dict)
        assert 'I' in moran_result
        assert 'p_value' in moran_result
        assert -1 <= moran_result['I'] <= 1
        assert 0 <= moran_result['p_value'] <= 1
    
    def test_plot_network(self, spatial_weight, tmp_path):
        """Test network visualization."""
        # Create weight matrix
        W = spatial_weight.create_weight_matrix(
            weight_type='distance',
            threshold=200
        )
        
        # Create plot
        save_path = tmp_path / "network.png"
        spatial_weight.plot_network(save_path=str(save_path))
        
        assert save_path.exists()
    
    def test_get_neighbors(self, spatial_weight):
        """Test neighbor identification."""
        # Create weight matrix
        W = spatial_weight.create_weight_matrix(
            weight_type='distance',
            threshold=200
        )
        
        # Get neighbors for first market
        neighbors = spatial_weight.get_neighbors('market_1', min_weight=0.1)
        
        assert isinstance(neighbors, list)
        assert 'market_1' not in neighbors  # No self-neighbors
    
    def test_invalid_weight_type(self, spatial_weight):
        """Test invalid weight type."""
        with pytest.raises(ValueError, match="weight_type"):
            spatial_weight.create_weight_matrix(weight_type='invalid')
    
    def test_missing_network_data(self, spatial_weight):
        """Test network weight without data."""
        with pytest.raises(ValueError, match="network_data"):
            spatial_weight.create_weight_matrix(weight_type='network')
    
    def test_export_weight_matrix(self, spatial_weight, tmp_path):
        """Test weight matrix export."""
        # Create weight matrix
        W = spatial_weight.create_weight_matrix(
            weight_type='distance',
            threshold=200
        )
        
        # Export to file
        export_path = tmp_path / "weights.csv"
        spatial_weight.export_weight_matrix(export_path)
        
        # Verify file exists and can be loaded
        assert export_path.exists()
        loaded = pd.read_csv(export_path, index_col=0)
        assert loaded.shape == (5, 5)