# Yemen Market Integration Analysis

[![Python 3.10+](https://img.shields.io/badge/python-3.10+-blue.svg)](https://www.python.org/downloads/)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Data Pipeline](https://img.shields.io/badge/data%20pipeline-100%25-success.svg)](docs/data_pipeline.md)

## Overview

This repository implements econometric analysis of market integration in conflict-affected Yemen, examining how dual exchange rates and territorial fragmentation affect commodity price transmission. The analysis employs threshold cointegration, spatial econometrics, and machine learning techniques.

## Quick Start

```bash
# Clone the repository
git clone https://github.com/worldbank/yemen-market-integration.git
cd yemen-market-integration

# Create conda environment
conda env create -f environment.yml
conda activate yemen-market

# Install package in development mode
pip install -e .

# Run the complete data pipeline
python scripts/data_collection/download_data.py        # Download from HDX
python scripts/data_collection/download_acled_data.py  # Download ACLED data
python scripts/data_processing/process_wfp_data.py     # Process prices (88.4% coverage)
python scripts/data_processing/process_acaps_data.py   # Process control zones
python scripts/data_processing/process_acled_data.py   # Process conflict data
python scripts/data_processing/run_spatial_joins.py    # Map markets to zones
python scripts/analysis/build_panel_datasets.py        # Create analysis panels

# Run econometric models (Week 5-6)
python scripts/analysis/run_week5_models.py            # Dual-track VECM models
python scripts/test_models.py                          # Test model implementations

# Run tests
python scripts/utilities/test_full_pipeline.py         # Full pipeline test

# Generate outputs
python scripts/utilities/export_notebooks_to_pdf.py    # Export notebooks
python scripts/utilities/create_visualization_gallery.py # Create gallery
```

## Project Structure

```
yemen-market-integration/
├── data/                   # Data directories (gitignored)
│   ├── raw/               # Downloaded from HDX
│   ├── interim/           # Intermediate processing
│   └── processed/         # Analysis-ready datasets
├── docs/                   # Documentation
│   ├── api/               # API documentation
│   ├── guides/            # User guides
│   └── technical/         # Technical documentation
├── examples/              # Example code and demos
├── notebooks/             # Jupyter notebooks for analysis
├── reports/               # Generated outputs
│   ├── figures/          # Visualizations
│   └── progress/         # Progress tracking
├── scripts/              # Organized pipeline scripts
│   ├── data_collection/  # Data download scripts
│   ├── data_processing/  # Processing scripts
│   ├── analysis/         # Analysis scripts
│   └── utilities/        # Utility scripts
├── src/yemen_market/     # Main package
│   ├── config/          # Configuration
│   ├── data/            # Data processing modules
│   ├── models/          # Econometric models
│   ├── utils/           # Utilities (enhanced logging)
│   └── visualization/   # Plotting functions
├── tests/               # Unit and integration tests
└── .claude/            # Claude Code context
```

## Key Features

### ✅ Completed (Week 3-4 Sprint: 100%)
- **Enhanced data pipeline** with 88.4% price coverage (up from 62%)
- **Pcode integration** standardizing all governorate names
- **Smart panel creation** respecting commodity-market availability
- **ACLED integration** with 60.5% conflict coverage
- **Feature engineering** module with 100+ derived features
- **Comprehensive documentation** with 100% API coverage
- **Three analysis notebooks** (validation, patterns, spatial)
- **Enhanced logging system** with timers and progress tracking
- **Organized scripts** with clear subfolder structure
- **Utility scripts** for testing, export, and visualization

### ✅ Week 5-6: Econometric Models (60% Complete)
- **Dual-track modeling approach** for robustness and clarity
- **Track 1: Bayesian TVP-VECM** with conflict-linked parameter evolution
- **Track 2: Simple Threshold VECM** with Hansen (1999) estimation
- **Spatial weight matrices** for network effects and spillovers
- **Comprehensive diagnostic framework** with 6 test categories
- **Model comparison tools** for assessing consistency

### 🚧 Next Steps
- **Policy simulation framework** for exchange rate unification scenarios
- **Full commodity analysis** beyond wheat
- **Machine learning integration** for pattern detection and forecasting

## Data Pipeline Components

| Component | Purpose | Key Features | Status |
|-----------|---------|--------------|--------|
| **HDXClient** | Fetch humanitarian data | Authentication, caching, rate limiting | ✅ Complete |
| **WFPProcessor** | Process price data | Exchange rate extraction, panel creation | ✅ Complete |
| **ACAPSProcessor** | Process control zones | Temporal tracking, standardization | ✅ Complete |
| **SpatialJoiner** | Map markets to zones | Boundary detection, distance calculation | ✅ Complete |
| **PanelBuilder** | Create analysis datasets | Integration, balancing, model-specific outputs | ✅ Complete |
| **ACLEDProcessor** | Process conflict data | Market-level metrics, actor analysis | ✅ Complete |
| **FeatureEngineer** | Create derived features | Temporal, spatial, interaction, threshold | ✅ Complete |

## Econometric Models (Week 5-6)

| Model Track | Type | Purpose | Key Features | Status |
|-------------|------|---------|--------------|--------|
| **Track 1** | Bayesian TVP-VECM | Capture time-varying dynamics | PyMC implementation, conflict sensitivity, stochastic volatility | ✅ Complete |
| **Track 2** | Simple Threshold VECM | Clear identification | Hansen (1999) estimation, regime transitions, bootstrap testing | ✅ Complete |
| **Spatial** | Network Components | Geographic spillovers | Distance/contiguity weights, visualization, Moran's I tests | ✅ Complete |
| **Diagnostics** | Test Battery | Model validation | 6 categories, automated reporting, critical failure detection | ✅ Complete |

## Methodology

The enhanced methodology is documented in [METHODOLOGY.md](METHODOLOGY.md), covering:
- Time-varying parameter VECM (Bayesian)
- Multiple threshold estimation (Hansen-Seo)
- Momentum TAR for asymmetric adjustment
- Network-augmented spatial analysis
- Machine learning integration

## Data Sources

- **World Food Programme (WFP)** price data via HDX (includes exchange rates)
- **ACAPS** Yemen areas of control (bi-weekly territorial boundaries)
- **ACLED** conflict events (aggregated)
- **HDX** administrative boundaries

### Key Datasets Created
- `wfp_price_data.parquet` - Standardized commodity prices
- `exchange_rates.parquet` - Official and parallel rates by market
- `control_zones_monthly.parquet` - Territorial control time series
- `market_zones_temporal.parquet` - Market-zone mapping over time
- `integrated_panel.parquet` - Analysis-ready panel dataset

## Using the Processed Data

```python
import pandas as pd
from pathlib import Path

# Load integrated panel
panel = pd.read_parquet('data/processed/panels/integrated_panel.parquet')

# Key variables available:
# - price_usd: Commodity prices in USD
# - parallel_rate: Parallel market exchange rate
# - official_rate: Official exchange rate
# - control_zone: Territorial control (houthi/government/contested)
# - rate_differential: Exchange rate gap between zones
# - price_usd_lag1/2/3: Lagged prices for time series
# - is_contested: Binary indicator for contested areas

# Initialize visualization
from yemen_market.visualization.price_dynamics import PriceDynamicsVisualizer
viz = PriceDynamicsVisualizer()

# Create price evolution plot
fig = viz.plot_price_evolution(panel, commodity='wheat_flour', save_path='reports/figures/')
```

## Development with Claude Code

This repository is optimized for development with Claude Code. Key resources:
- `.claude/project_memory.md` - Current context and status
- `.claude/tasks/current_sprint.md` - Active development tasks
- `CLAUDE.md` - Comprehensive development guide
- `reports/progress/README.md` - Progress tracking dashboard

## Requirements

- Python 3.10+
- Key packages: pandas, numpy, geopandas, statsmodels, hdx-python-api
- See `environment.yml` for complete dependencies

## Testing

```bash
# Run all tests
pytest tests/

# Run with coverage
pytest tests/ --cov=yemen_market --cov-report=html

# Run specific test modules
pytest tests/unit/test_hdx_client.py
pytest tests/unit/test_wfp_processor.py
pytest tests/unit/test_acaps_processor.py
pytest tests/unit/test_spatial_joins.py
pytest tests/unit/test_panel_builder.py
pytest tests/unit/test_logging.py

# Test logging functionality
python scripts/logging_integration_demo.py
```

## Contributing

1. Follow the coding standards in `.claude/coding_standards.md`
2. Use conventional commits (feat:, fix:, docs:, test:)
3. Ensure >90% test coverage
4. Update progress tracking in `.claude/tasks/`

## Citation

If you use this code or methodology, please cite:

```bibtex
@techreport{yemen_market_2025,
  title={Market Unity Amidst Conflict: Econometric Analysis of Yemen's Market Integration},
  author={Mohammad al Akkaoui},
  institution={World Bank},
  year={2025}
}
```

## License

This project is licensed under the MIT License - see [LICENSE](LICENSE) file.

## Contact

For questions about this analysis, contact: <EMAIL>

---

**Note**: This is an active research project. The data pipeline is complete and ready for econometric analysis.