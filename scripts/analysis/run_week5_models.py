#!/usr/bin/env python3
"""Run Week 5-6 econometric models on Yemen market data.

This script:
1. Loads the processed panel data
2. Runs both Track 1 (Bayesian) and Track 2 (Threshold) models
3. Compares results and generates visualizations
4. Saves model outputs for further analysis

Run from project root:
    python scripts/analysis/run_week5_models.py
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, Tuple

from yemen_market.utils.logging import (
    info, warning, error, timer, bind, progress,
    log_metric, log_data_shape, setup_logging
)
from yemen_market.models.track1_complex.tvp_vecm import BayesianTVPVECM
from yemen_market.models.track2_simple.threshold_vecm import SimpleThresholdVECM
from yemen_market.models.track1_complex.spatial_network import SpatialWeightMatrix
from yemen_market.models.model_comparison import ModelComparisonFramework
from yemen_market.diagnostics.test_battery import DiagnosticTestBattery
from yemen_market.diagnostics.tests.pre_estimation import (
    test_unit_roots_battery,
    test_cointegration,
    test_gregory_hansen_cointegration
)


def load_analysis_data() -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Load processed panel data for analysis.
    
    Returns:
        Tuple of (full_panel, smart_panel)
    """
    info("Loading processed panel data")
    
    # Load the integrated panel which has all necessary data including conflict
    panel_path = Path("data/processed/panels/integrated_panel.parquet")
    
    if not panel_path.exists():
        error(f"Panel data not found at {panel_path}")
        return None, None
    
    smart_panel = pd.read_parquet(panel_path)
    info(f"Loaded integrated panel: {len(smart_panel)} observations")
    log_data_shape("smart_panel", smart_panel)
    
    # Verify we have the necessary columns
    required_cols = ['commodity', 'market_id', 'date', 'price_usd', 'conflict_intensity', 
                     'control_zone', 'lat', 'lon']
    missing_cols = [col for col in required_cols if col not in smart_panel.columns]
    if missing_cols:
        warning(f"Missing columns in panel: {missing_cols}")
    else:
        info("All required columns present including conflict data")
        
    # Log conflict statistics
    info(f"Conflict intensity range: {smart_panel['conflict_intensity'].min():.0f} - {smart_panel['conflict_intensity'].max():.0f}")
    info(f"Mean conflict intensity: {smart_panel['conflict_intensity'].mean():.1f}")
    
    # Also load full panel for comparison
    full_panel_path = Path("data/processed/integrated_panel_monthly.parquet")
    if full_panel_path.exists():
        full_panel = pd.read_parquet(full_panel_path)
        info(f"Loaded full panel: {len(full_panel)} observations")
    else:
        full_panel = None
    
    return full_panel, smart_panel


def prepare_wheat_data(panel: pd.DataFrame) -> pd.DataFrame:
    """Extract and prepare wheat price data for VECM analysis.
    
    Args:
        panel: Full panel dataframe
        
    Returns:
        Wheat-specific panel with required columns
    """
    info("Preparing wheat data for analysis")
    
    # Filter to wheat only
    wheat_data = panel[panel['commodity'] == 'Wheat'].copy()
    
    if len(wheat_data) == 0:
        warning("No wheat data found, trying 'Wheat - Retail'")
        wheat_data = panel[panel['commodity'] == 'Wheat - Retail'].copy()
    
    info(f"Wheat observations: {len(wheat_data)}")
    
    # Ensure we have key columns
    required_cols = ['date', 'market_name', 'price_usd', 'conflict_intensity']
    missing_cols = [col for col in required_cols if col not in wheat_data.columns]
    
    if missing_cols:
        error(f"Missing required columns: {missing_cols}")
        return None
    
    # Sort by date and market
    wheat_data = wheat_data.sort_values(['market_name', 'date'])
    
    # Check market coverage
    markets = wheat_data['market_name'].unique()
    date_range = wheat_data['date'].min(), wheat_data['date'].max()
    
    info(f"Markets: {len(markets)}")
    info(f"Date range: {date_range[0]} to {date_range[1]}")
    
    # Calculate coverage
    expected_obs = len(markets) * wheat_data['date'].nunique()
    actual_obs = len(wheat_data)
    coverage = actual_obs / expected_obs * 100
    
    info(f"Data coverage: {coverage:.1f}% ({actual_obs}/{expected_obs} observations)")
    
    return wheat_data


def run_pre_estimation_tests(wheat_data: pd.DataFrame) -> None:
    """Run pre-estimation diagnostic tests.
    
    Args:
        wheat_data: Prepared wheat panel data
    """
    info("\n" + "="*60)
    info("PRE-ESTIMATION DIAGNOSTIC TESTS")
    info("="*60)
    
    # Prepare data for testing
    top_markets = wheat_data.groupby('market_name').size().nlargest(5).index.tolist()
    price_matrix = wheat_data[wheat_data['market_name'].isin(top_markets)].pivot_table(
        index='date',
        columns='market_name',
        values='price_usd',
        aggfunc='mean'
    )
    log_prices = np.log(price_matrix).dropna()
    
    test_data = {
        'log_prices': log_prices,
        'markets': top_markets,
        'dlogs': log_prices.diff().dropna()
    }
    
    # 1. Unit root tests
    unit_root_results = test_unit_roots_battery(test_data)
    info(f"\nUnit Root Tests: {unit_root_results.interpretation}")
    info(f"  Passed: {'✅' if unit_root_results.passed else '❌'}")
    
    # 2. Johansen cointegration test
    johansen_result = test_cointegration(test_data)
    info(f"\nJohansen Cointegration: {johansen_result.interpretation}")
    info(f"  Selected rank: {johansen_result.details.get('selected_rank', 0)}")
    info(f"  Passed: {'✅' if johansen_result.passed else '❌'}")
    
    # 3. Gregory-Hansen test with structural break
    gh_result = test_gregory_hansen_cointegration(test_data, test_type='c')
    info(f"\nGregory-Hansen Test: {gh_result.interpretation}")
    if 'break_date' in gh_result.details:
        info(f"  Break date: {gh_result.details['break_date']}")
        info(f"  Break magnitude: {gh_result.details.get('break_magnitude', 'N/A')}")
    info(f"  Passed: {'✅' if gh_result.passed else '❌'}")
    
    # Decision based on tests
    if johansen_result.details.get('selected_rank', 0) == 0 and gh_result.passed:
        warning("\n⚠️ Structural break may be masking cointegration!")
        info("Consider separate analysis for pre/post break periods")


def create_spatial_weights(wheat_data: pd.DataFrame) -> np.ndarray:
    """Create spatial weight matrix for the markets.
    
    Args:
        wheat_data: Panel data with market coordinates
        
    Returns:
        Spatial weight matrix
    """
    info("Creating spatial weight matrix")
    
    # Get unique markets with coordinates
    markets_df = wheat_data[['market_name', 'latitude', 'longitude']].drop_duplicates()
    markets_df = markets_df.dropna(subset=['latitude', 'longitude'])
    
    if len(markets_df) == 0:
        warning("No market coordinates available, skipping spatial weights")
        return None
    
    info(f"Creating weights for {len(markets_df)} markets with coordinates")
    
    # Create weight matrix
    sw = SpatialWeightMatrix(markets_df, weight_type='distance', normalize=True)
    W = sw.create_weight_matrix(cutoff_km=200, decay='exponential', alpha=1.0)
    
    # Save network plot
    output_dir = Path("reports/figures")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    sw.plot_network(save_path=output_dir / "spatial_network.png", edge_threshold=0.1)
    
    return W, sw


def run_threshold_model(wheat_data: pd.DataFrame) -> Dict[str, Any]:
    """Run Track 2: Simple Threshold VECM.
    
    Args:
        wheat_data: Prepared wheat panel data
        
    Returns:
        Dictionary with model results
    """
    info("\n" + "="*60)
    info("TRACK 2: Simple Threshold VECM")
    info("="*60)
    
    with timer("threshold_model"):
        # Initialize model
        model = SimpleThresholdVECM(
            threshold=50.0,  # Initial guess based on EDA
            threshold_variable='conflict_intensity',
            n_coint=1,
            n_lags=2,
            trim_pct=0.15,
            n_boot=1000  # Full bootstrap for production
        )
        
        # Fit model
        try:
            model.fit(wheat_data, estimate_threshold=True, price_col='price_usd')
            
            # Extract results
            results = model.vecm_results
            
            info("\n--- Threshold VECM Results ---")
            info(f"Estimated threshold: {results.threshold_value:.2f}")
            info(f"Threshold CI: [{results.threshold_ci_lower:.2f}, {results.threshold_ci_upper:.2f}]")
            info(f"Threshold test p-value: {results.threshold_p_value:.4f}")
            info(f"Low regime: {results.n_obs_low} obs ({results.n_obs_low/results.n_obs*100:.1f}%)")
            info(f"High regime: {results.n_obs_high} obs ({results.n_obs_high/results.n_obs*100:.1f}%)")
            
            # Regime dynamics
            regime_info = model.analyze_regime_dynamics()
            info(f"\nRegime persistence:")
            info(f"  Low regime duration: {regime_info['expected_low_duration']:.1f} months")
            info(f"  High regime duration: {regime_info['expected_high_duration']:.1f} months")
            
            # Generate predictions
            predictions = model.predict(steps=6)
            info(f"\nGenerated {len(predictions)}-month ahead forecasts")
            
            # Run diagnostics
            battery = DiagnosticTestBattery(model)
            diagnostics = battery.run_all_tests(
                skip_categories=['robustness'],  # Skip slow tests
                include_slow=False
            )
            
            # Save diagnostic report
            report_path = Path("reports/diagnostics_threshold_vecm.md")
            battery.generate_report(report_path)
            
            # Plot results
            fig_path = Path("reports/figures/threshold_dynamics.png")
            model.plot_threshold_dynamics(save_path=fig_path)
            
            return {
                'model': model,
                'results': results,
                'diagnostics': diagnostics,
                'regime_info': regime_info,
                'predictions': predictions
            }
            
        except Exception as e:
            error(f"Threshold model failed: {e}")
            import traceback
            traceback.print_exc()
            return None


def run_bayesian_model(wheat_data: pd.DataFrame) -> Dict[str, Any]:
    """Run Track 1: Bayesian TVP-VECM.
    
    Args:
        wheat_data: Prepared wheat panel data
        
    Returns:
        Dictionary with model results
    """
    info("\n" + "="*60)
    info("TRACK 1: Bayesian TVP-VECM")
    info("="*60)
    
    # Check if PyMC is available
    try:
        import pymc
        import arviz
        info("PyMC and ArviZ available for Bayesian modeling")
    except ImportError:
        warning("PyMC not installed, skipping Bayesian model")
        warning("Install with: pip install pymc arviz")
        return None
    
    with timer("bayesian_model"):
        # Initialize model with reasonable settings
        model = BayesianTVPVECM(
            n_coint=1,
            n_lags=2,
            n_samples=2000,   # Production sampling
            n_chains=4,      # Production chains
            target_accept=0.8,
            random_seed=42
        )
        
        # Fit model
        try:
            model.fit(wheat_data, conflict_col='conflict_intensity', price_col='price_usd')
            
            # Extract results
            results = model.vecm_results
            
            info("\n--- Bayesian TVP-VECM Results ---")
            info(f"Model converged: {results.converged}")
            if hasattr(results, 'rhat'):
                max_rhat = max(results.rhat.values()) if results.rhat else np.nan
                info(f"Max R-hat: {max_rhat:.3f}")
            
            # Analyze conflict impact
            conflict_impact = model.analyze_conflict_impact()
            info(f"\nAverage |α_diff|: {conflict_impact.get('avg_alpha_diff', 0):.4f}")
            info(f"  Significant markets: {conflict_impact.get('n_significant_differences', 0)}")
            info(f"  Conflict impact significant: {conflict_impact.get('significant', False)}")
            
            # Generate predictions
            predictions, (lower, upper) = model.predict(steps=6, include_uncertainty=True)
            info(f"\nGenerated {len(predictions)}-month ahead forecasts with uncertainty")
            
            # Plot time-varying parameters
            fig_path = Path("reports/figures/tvp_evolution.png")
            model.plot_time_varying_parameters(market_idx=0, save_path=fig_path)
            
            # Run diagnostics
            battery = DiagnosticTestBattery(model)
            diagnostics = battery.run_all_tests(
                skip_categories=['robustness', 'validation'],
                include_slow=False
            )
            
            return {
                'model': model,
                'results': results,
                'diagnostics': diagnostics,
                'conflict_impact': conflict_impact,
                'predictions': predictions,
                'prediction_bounds': (lower, upper)
            }
            
        except Exception as e:
            error(f"Bayesian model failed: {e}")
            import traceback
            traceback.print_exc()
            return None


def compare_models(threshold_results: Dict[str, Any],
                  bayesian_results: Dict[str, Any]) -> None:
    """Compare results from both modeling approaches.
    
    Args:
        threshold_results: Results from threshold VECM
        bayesian_results: Results from Bayesian TVP-VECM
    """
    info("\n" + "="*60)
    info("MODEL COMPARISON")
    info("="*60)
    
    # Create comparison plots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. Forecast comparison
    ax = axes[0, 0]
    
    if threshold_results and 'predictions' in threshold_results:
        thresh_pred = threshold_results['predictions']
        ax.plot(thresh_pred.index, thresh_pred.iloc[:, 0], 
                label='Threshold VECM', color='blue', linewidth=2)
    
    if bayesian_results and 'predictions' in bayesian_results:
        bayes_pred = bayesian_results['predictions']
        lower, upper = bayesian_results['prediction_bounds']
        
        ax.plot(bayes_pred.index, bayes_pred.iloc[:, 0], 
                label='Bayesian TVP-VECM', color='red', linewidth=2)
        ax.fill_between(bayes_pred.index, 
                       lower.iloc[:, 0], upper.iloc[:, 0],
                       alpha=0.3, color='red', label='95% CI')
    
    ax.set_title('6-Month Ahead Forecasts (First Market)')
    ax.set_xlabel('Date')
    ax.set_ylabel('Price (USD)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 2. Model fit comparison (placeholder)
    ax = axes[0, 1]
    
    metrics = []
    labels = []
    
    if threshold_results:
        t_res = threshold_results['results']
        metrics.append([t_res.aic, t_res.bic])
        labels.append('Threshold\nVECM')
    
    if bayesian_results:
        b_res = bayesian_results['results']
        # Use WAIC/LOO for Bayesian models
        metrics.append([
            b_res.waic if hasattr(b_res, 'waic') else np.nan,
            b_res.loo if hasattr(b_res, 'loo') else np.nan
        ])
        labels.append('Bayesian\nTVP-VECM')
    
    if metrics:
        x = np.arange(len(labels))
        width = 0.35
        
        if not all(np.isnan(m[0]) for m in metrics):
            ax.bar(x - width/2, [m[0] for m in metrics], width, label='AIC/WAIC')
        if not all(np.isnan(m[1]) for m in metrics):
            ax.bar(x + width/2, [m[1] for m in metrics], width, label='BIC/LOO')
        
        ax.set_ylabel('Information Criterion')
        ax.set_title('Model Comparison')
        ax.set_xticks(x)
        ax.set_xticklabels(labels)
        ax.legend()
    
    # 3. Diagnostic test summary
    ax = axes[1, 0]
    
    test_results = []
    
    if threshold_results and 'diagnostics' in threshold_results:
        passed = sum(
            sum(1 for r in results if r.passed)
            for results in threshold_results['diagnostics'].values()
        )
        total = sum(len(results) for results in threshold_results['diagnostics'].values())
        test_results.append(('Threshold VECM', passed, total))
    
    if bayesian_results and 'diagnostics' in bayesian_results:
        passed = sum(
            sum(1 for r in results if r.passed)
            for results in bayesian_results['diagnostics'].values()
        )
        total = sum(len(results) for results in bayesian_results['diagnostics'].values())
        test_results.append(('Bayesian TVP-VECM', passed, total))
    
    if test_results:
        models = [r[0] for r in test_results]
        pass_rates = [r[1]/r[2]*100 for r in test_results]
        
        bars = ax.bar(models, pass_rates)
        ax.set_ylim(0, 100)
        ax.set_ylabel('Pass Rate (%)')
        ax.set_title('Diagnostic Test Performance')
        
        # Add value labels
        for bar, (model, passed, total) in zip(bars, test_results):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{passed}/{total}',
                   ha='center', va='bottom')
    
    # 4. Key insights
    ax = axes[1, 1]
    ax.axis('off')
    
    insights = ["KEY INSIGHTS\n"]
    
    if threshold_results:
        t_res = threshold_results['results']
        insights.append(f"• Conflict threshold: {t_res.threshold_value:.1f} events/month")
        insights.append(f"• Threshold effect is {'significant' if t_res.threshold_p_value < 0.05 else 'not significant'}")
        
        regime_info = threshold_results.get('regime_info', {})
        if regime_info:
            insights.append(f"• High conflict regime is {'less' if regime_info.get('mean_alpha_diff', 0) < 0 else 'more'} integrated")
    
    if bayesian_results:
        conflict = bayesian_results.get('conflict_impact', {})
        if conflict.get('significant'):
            insights.append(f"• Conflict significantly affects price dynamics")
            insights.append(f"• Time-varying integration detected")
    
    ax.text(0.1, 0.9, '\n'.join(insights), 
            transform=ax.transAxes, fontsize=11,
            verticalalignment='top')
    
    plt.suptitle('Dual-Track Model Comparison', fontsize=16)
    plt.tight_layout()
    
    # Save comparison plot
    output_path = Path("reports/figures/model_comparison.png")
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    info(f"Saved model comparison to {output_path}")
    plt.close()


def save_results(threshold_results: Dict[str, Any],
                bayesian_results: Dict[str, Any]) -> None:
    """Save model results for future use.
    
    Args:
        threshold_results: Results from threshold model
        bayesian_results: Results from Bayesian model
    """
    output_dir = Path("data/model_outputs")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save predictions
    if threshold_results and 'predictions' in threshold_results:
        threshold_results['predictions'].to_parquet(
            output_dir / "threshold_vecm_predictions.parquet"
        )
    
    if bayesian_results and 'predictions' in bayesian_results:
        bayesian_results['predictions'].to_parquet(
            output_dir / "bayesian_tvp_predictions.parquet"
        )
        
        # Save prediction bounds
        lower, upper = bayesian_results['prediction_bounds']
        lower.to_parquet(output_dir / "bayesian_tvp_lower_bound.parquet")
        upper.to_parquet(output_dir / "bayesian_tvp_upper_bound.parquet")
    
    info(f"Model outputs saved to {output_dir}")


def main():
    """Main execution function."""
    # Setup logging
    setup_logging("INFO")
    bind(script="run_week5_models")
    
    # Enable hardware acceleration for M3 Pro
    try:
        from enable_acceleration import setup_apple_silicon_acceleration
        hw_info = setup_apple_silicon_acceleration()
        info("Hardware acceleration enabled for M3 Pro")
    except ImportError:
        info("Running without explicit hardware acceleration")
        info("Run 'python scripts/analysis/enable_acceleration.py' for optimization")
    
    info("Starting Week 5-6 Model Estimation")
    info("=" * 60)
    
    # Load data
    full_panel, smart_panel = load_analysis_data()
    
    if smart_panel is None:
        error("Failed to load data")
        return
    
    # Prepare wheat data
    wheat_data = prepare_wheat_data(smart_panel)
    
    if wheat_data is None or len(wheat_data) == 0:
        error("No wheat data available for analysis")
        return
    
    # Run pre-estimation tests
    run_pre_estimation_tests(wheat_data)
    
    # Create spatial weights
    spatial_weights, sw = create_spatial_weights(wheat_data)
    
    # Run models
    threshold_results = run_threshold_model(wheat_data)
    bayesian_results = run_bayesian_model(wheat_data)
    
    # Compare results
    if threshold_results or bayesian_results:
        compare_models(threshold_results, bayesian_results)
        save_results(threshold_results, bayesian_results)
    
    # Summary
    info("\n" + "="*60)
    info("ANALYSIS COMPLETE")
    info("="*60)
    
    if threshold_results:
        info("✓ Threshold VECM estimated successfully")
    else:
        warning("✗ Threshold VECM failed")
    
    if bayesian_results:
        info("✓ Bayesian TVP-VECM estimated successfully")
    else:
        warning("✗ Bayesian TVP-VECM failed or skipped")
    
    info("\nOutputs saved to:")
    info("  - reports/figures/")
    info("  - reports/diagnostics_*.md")
    info("  - data/model_outputs/")


if __name__ == "__main__":
    main()