"""ACLED conflict data processor for Yemen market integration analysis.

This module processes Armed Conflict Location & Event Data (ACLED) to extract
conflict intensity metrics at the market-month level for integration with
price transmission models.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import geopandas as gpd
from shapely.geometry import Point
from datetime import datetime

from yemen_market.config.settings import RAW_DATA_DIR, PROCESSED_DATA_DIR
from yemen_market.utils.logging import (
    get_logger, info, warning, error, 
    timer, progress, log_data_shape, bind
)

# Bind module context
bind(module=__name__)
logger = get_logger(__name__)


class ACLEDProcessor:
    """Process ACLED conflict data for Yemen market analysis."""
    
    # Conflict event types to track
    EVENT_TYPES = [
        'Battles',
        'Explosions/Remote violence', 
        'Violence against civilians',
        'Protests',
        'Riots',
        'Strategic developments'
    ]
    
    # Actors of interest
    KEY_ACTORS = [
        'Houthis',
        'Government',
        'AQAP',
        'ISIS',
        'STC',
        'Coalition'
    ]
    
    def __init__(self, 
                 data_dir: Optional[Path] = None,
                 output_dir: Optional[Path] = None,
                 radius_km: float = 50.0):
        """Initialize ACLED processor.
        
        Args:
            data_dir: Directory containing raw ACLED data
            output_dir: Directory for processed output
            radius_km: Radius for spatial aggregation around markets
        """
        self.data_dir = data_dir or RAW_DATA_DIR / "acled"
        self.output_dir = output_dir or PROCESSED_DATA_DIR / "conflict"
        self.radius_km = radius_km
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        info(f"ACLEDProcessor initialized", 
             radius_km=radius_km,
             data_dir=str(self.data_dir))
    
    def load_acled_data(self, 
                       start_date: Optional[str] = None,
                       end_date: Optional[str] = None) -> pd.DataFrame:
        """Load raw ACLED data for Yemen.
        
        Args:
            start_date: Start date filter (YYYY-MM-DD)
            end_date: End date filter (YYYY-MM-DD)
            
        Returns:
            DataFrame with ACLED events
        """
        with timer("load_acled_data"):
            # Find ACLED files
            acled_files = list(self.data_dir.glob("*yemen*.csv")) + \
                         list(self.data_dir.glob("*Yemen*.csv")) + \
                         list(self.data_dir.glob("*YEM*.csv")) + \
                         list(self.data_dir.glob("acled_*.csv"))
            
            if not acled_files:
                raise FileNotFoundError(f"No ACLED files found in {self.data_dir}")
            
            # Use most recent file
            acled_file = sorted(acled_files)[-1]
            info(f"Loading ACLED data from {acled_file.name}")
            
            # Load data
            df = pd.read_csv(acled_file, parse_dates=['event_date'])
            log_data_shape("raw_acled", df)
            
            # Filter by date if specified
            if start_date:
                df = df[df['event_date'] >= pd.to_datetime(start_date)]
            if end_date:
                df = df[df['event_date'] <= pd.to_datetime(end_date)]
            
            # Ensure required columns exist
            required_cols = ['event_date', 'latitude', 'longitude', 
                           'event_type', 'fatalities', 'actor1', 'actor2']
            
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                error(f"Missing required columns: {missing_cols}")
                raise ValueError(f"ACLED data missing columns: {missing_cols}")
            
            info(f"Loaded {len(df)} events", 
                 date_range=f"{df['event_date'].min()} to {df['event_date'].max()}")
            
            return df
    
    def load_market_locations(self) -> gpd.GeoDataFrame:
        """Load market locations for spatial aggregation.
        
        Returns:
            GeoDataFrame with market points
        """
        # Try to load from WFP panel data
        panel_path = PROCESSED_DATA_DIR / "wfp_market_panel.parquet"
        
        if panel_path.exists():
            df = pd.read_parquet(panel_path)
            
            # Get unique markets with coordinates
            markets = df[['governorate', 'market_name', 'lat', 'lon']].drop_duplicates()
            markets = markets.dropna(subset=['lat', 'lon'])
            
            # Create market IDs
            markets['market_id'] = (markets['governorate'] + '_' + 
                                   markets['market_name']).str.replace(' ', '_')
            
            # Convert to GeoDataFrame
            geometry = [Point(lon, lat) for lon, lat in 
                       zip(markets['lon'], markets['lat'])]
            
            gdf = gpd.GeoDataFrame(markets, geometry=geometry, crs='EPSG:4326')
            
            info(f"Loaded {len(gdf)} market locations")
            return gdf
        else:
            raise FileNotFoundError("Market locations not found. Run WFP processor first.")
    
    def calculate_conflict_metrics(self, 
                                 events: pd.DataFrame,
                                 markets: gpd.GeoDataFrame) -> pd.DataFrame:
        """Calculate conflict intensity metrics for each market-month.
        
        Args:
            events: ACLED event data
            markets: Market locations
            
        Returns:
            DataFrame with conflict metrics by market-month
        """
        with timer("calculate_conflict_metrics"):
            info("Calculating conflict metrics", 
                 n_events=len(events),
                 n_markets=len(markets))
            
            # Convert events to GeoDataFrame
            event_geometry = [Point(lon, lat) for lon, lat in 
                            zip(events['longitude'], events['latitude'])]
            
            events_gdf = gpd.GeoDataFrame(
                events, 
                geometry=event_geometry, 
                crs='EPSG:4326'
            )
            
            # Project to Yemen TM for distance calculations
            events_proj = events_gdf.to_crs('EPSG:2090')
            markets_proj = markets.to_crs('EPSG:2090')
            
            # Buffer markets by radius
            market_buffers = markets_proj.copy()
            market_buffers['geometry'] = market_buffers.geometry.buffer(
                self.radius_km * 1000  # Convert km to meters
            )
            
            # Initialize results
            results = []
            
            # Add year-month to events
            events_proj['year_month'] = events_proj['event_date'].dt.to_period('M')
            
            # Get unique year-months
            periods = sorted(events_proj['year_month'].unique())
            
            with progress("Processing market-months", total=len(markets) * len(periods)) as update:
                for _, market in markets_proj.iterrows():
                    market_buffer = market.geometry.buffer(self.radius_km * 1000)
                    
                    # Find events within buffer
                    nearby_mask = events_proj.geometry.within(market_buffer)
                    nearby_events = events_proj[nearby_mask].copy()
                    
                    # Calculate distances
                    if len(nearby_events) > 0:
                        nearby_events['distance_km'] = (
                            nearby_events.geometry.distance(market.geometry) / 1000
                        )
                    
                    for period in periods:
                        # Filter to this month
                        month_events = nearby_events[
                            nearby_events['year_month'] == period
                        ]
                        
                        # Calculate metrics
                        metrics = {
                            'market_id': market['market_id'],
                            'year_month': period,
                            'n_events': len(month_events),
                            'n_battles': len(month_events[
                                month_events['event_type'] == 'Battles'
                            ]),
                            'n_explosions': len(month_events[
                                month_events['event_type'] == 'Explosions/Remote violence'
                            ]),
                            'n_violence_civilians': len(month_events[
                                month_events['event_type'] == 'Violence against civilians'
                            ]),
                            'total_fatalities': month_events['fatalities'].sum(),
                            'avg_distance_km': month_events['distance_km'].mean() if len(month_events) > 0 else np.nan
                        }
                        
                        # Actor involvement
                        for actor in self.KEY_ACTORS:
                            actor_events = month_events[
                                (month_events['actor1'].str.contains(actor, case=False, na=False)) |
                                (month_events['actor2'].str.contains(actor, case=False, na=False))
                            ]
                            metrics[f'n_events_{actor.lower()}'] = len(actor_events)
                        
                        results.append(metrics)
                        update(1)
            
            # Convert to DataFrame
            metrics_df = pd.DataFrame(results)
            
            # Handle empty results case
            if metrics_df.empty:
                info("No conflict events found in the specified radius")
                # Create empty dataframe with expected columns
                metrics_df = pd.DataFrame(columns=[
                    'market_id', 'year_month', 'n_events', 'n_battles',
                    'n_explosions', 'n_violence_civilians', 'total_fatalities',
                    'avg_distance_km', 'conflict_intensity'
                ])
                return metrics_df
            
            # Convert year_month Period to string for parquet compatibility
            metrics_df['year_month'] = metrics_df['year_month'].astype(str)
            
            # Add derived metrics
            metrics_df['conflict_intensity'] = (
                metrics_df['n_events'] + 
                metrics_df['n_battles'] * 2 + 
                metrics_df['n_explosions'] * 2 +
                metrics_df['total_fatalities'] * 0.1
            )
            
            # Add lagged variables
            metrics_df = metrics_df.sort_values(['market_id', 'year_month'])
            
            for lag in [1, 2, 3]:
                metrics_df[f'conflict_intensity_lag{lag}'] = (
                    metrics_df.groupby('market_id')['conflict_intensity']
                    .shift(lag)
                )
            
            # Calculate moving averages
            metrics_df['conflict_ma3'] = (
                metrics_df.groupby('market_id')['conflict_intensity']
                .transform(lambda x: x.rolling(3, min_periods=1).mean())
            )
            
            log_data_shape("conflict_metrics", metrics_df)
            
            return metrics_df
    
    def process_conflict_data(self, 
                            start_date: Optional[str] = None,
                            end_date: Optional[str] = None) -> Dict[str, pd.DataFrame]:
        """Main processing pipeline for ACLED data.
        
        Args:
            start_date: Start date for analysis
            end_date: End date for analysis
            
        Returns:
            Dictionary with processed datasets
        """
        info("=" * 60)
        info("Processing ACLED Conflict Data")
        info("=" * 60)
        
        try:
            # Load data
            events = self.load_acled_data(start_date, end_date)
            markets = self.load_market_locations()
            
            # Calculate metrics
            metrics = self.calculate_conflict_metrics(events, markets)
            
            # Create summary statistics
            summary = self.create_summary_stats(metrics)
            
            # Save outputs
            outputs = {
                'conflict_metrics': metrics,
                'conflict_summary': summary
            }
            
            self.save_outputs(outputs)
            
            return outputs
            
        except Exception as e:
            error(f"Error processing ACLED data: {e}")
            raise
    
    def create_summary_stats(self, metrics: pd.DataFrame) -> pd.DataFrame:
        """Create summary statistics for conflict data.
        
        Args:
            metrics: Conflict metrics by market-month
            
        Returns:
            Summary statistics DataFrame
        """
        summary_data = []
        
        # Overall statistics
        summary_data.append({
            'statistic': 'Total observations',
            'value': len(metrics)
        })
        
        summary_data.append({
            'statistic': 'Markets covered',
            'value': metrics['market_id'].nunique()
        })
        
        # Convert Period to string for summary
        if 'year_month' in metrics.columns and len(metrics) > 0:
            period_start = str(metrics['year_month'].min())
            period_end = str(metrics['year_month'].max())
            summary_data.append({
                'statistic': 'Time period',
                'value': f"{period_start} to {period_end}"
            })
        else:
            summary_data.append({
                'statistic': 'Time period',
                'value': 'No data'
            })
        
        # Conflict statistics
        summary_data.append({
            'statistic': 'Average events per market-month',
            'value': f"{metrics['n_events'].mean():.1f}"
        })
        
        summary_data.append({
            'statistic': 'Average fatalities per market-month',
            'value': f"{metrics['total_fatalities'].mean():.1f}"
        })
        
        summary_data.append({
            'statistic': 'Markets with zero events (%)',
            'value': f"{(metrics['n_events'] == 0).mean() * 100:.1f}%"
        })
        
        # High conflict markets
        high_conflict = metrics.groupby('market_id')['conflict_intensity'].mean()
        top_markets = high_conflict.nlargest(5)
        
        for i, (market, intensity) in enumerate(top_markets.items(), 1):
            summary_data.append({
                'statistic': f'High conflict market {i}',
                'value': f"{market} ({intensity:.1f})"
            })
        
        # Ensure all values are strings for consistency
        for item in summary_data:
            item['value'] = str(item['value'])
            
        return pd.DataFrame(summary_data)
    
    def save_outputs(self, outputs: Dict[str, pd.DataFrame]) -> None:
        """Save processed outputs.
        
        Args:
            outputs: Dictionary of DataFrames to save
        """
        info("Saving conflict data outputs")
        
        # Save each output
        for name, df in outputs.items():
            if df is not None and not df.empty:
                # Parquet format
                parquet_path = self.output_dir / f"{name}.parquet"
                df.to_parquet(parquet_path, index=False)
                info(f"Saved {name} to {parquet_path}")
                
                # CSV sample for inspection
                if len(df) > 100:
                    sample = df.sample(min(100, len(df)), random_state=42)
                else:
                    sample = df
                
                csv_path = self.output_dir / f"{name}_sample.csv"
                sample.to_csv(csv_path, index=False)
        
        # Save metadata
        # Build metadata
        if 'conflict_metrics' in outputs and not outputs['conflict_metrics'].empty:
            n_markets = outputs['conflict_metrics']['market_id'].nunique()
            start_period = outputs['conflict_metrics']['year_month'].min()
            end_period = outputs['conflict_metrics']['year_month'].max()
        else:
            n_markets = 0
            start_period = 'N/A'
            end_period = 'N/A'
            
        metadata = {
            'processed_date': datetime.now().isoformat(),
            'radius_km': self.radius_km,
            'n_markets': n_markets,
            'time_range': {
                'start': str(start_period),
                'end': str(end_period)
            }
        }
        
        import json
        metadata_path = self.output_dir / "processing_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        info("All outputs saved successfully")