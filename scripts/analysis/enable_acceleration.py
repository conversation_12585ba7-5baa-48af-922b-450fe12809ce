#!/usr/bin/env python
"""Enable hardware acceleration for M3 Pro MacBook Pro.

This script configures optimal settings for Apple Silicon acceleration
in the Yemen market integration models.
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from yemen_market.utils.logging import setup_logging, info, warning


def setup_apple_silicon_acceleration():
    """Configure environment for Apple Silicon acceleration."""
    
    info("Configuring Apple Silicon acceleration for M3 Pro")
    
    # 1. Enable JAX Metal backend for PyMC (if available)
    try:
        # Set JAX to use Metal
        os.environ['JAX_PLATFORMS'] = 'cpu'  # Force CPU only (Metal disabled)
        os.environ['JAX_ENABLE_X64'] = 'True'  # Use 64-bit precision
        
        # Try to import and configure JAX
        import jax
        info(f"JAX version: {jax.__version__}")
        info(f"JAX devices: {jax.devices()}")
        
        # Configure PyMC to use JAX
        import pymc as pm
        info(f"PyMC version: {pm.__version__}")
        
    except ImportError:
        warning("JAX not installed. PyMC will use default backend.")
        info("Install with: pip install jax-metal")
    
    # 2. Configure NumPy/SciPy for Apple Accelerate framework
    try:
        import numpy as np
        import scipy
        
        # Check if using Accelerate
        info(f"NumPy version: {np.__version__}")
        info(f"NumPy config: {np.show_config()}")
        
        # Set threading for optimal M3 Pro performance (10 CPU cores)
        os.environ['OMP_NUM_THREADS'] = '10'
        os.environ['MKL_NUM_THREADS'] = '10'
        os.environ['OPENBLAS_NUM_THREADS'] = '10'
        os.environ['VECLIB_MAXIMUM_THREADS'] = '10'
        
        info("Configured threading for 10 CPU cores")
        
    except Exception as e:
        warning(f"Could not configure NumPy acceleration: {e}")
    
    # 3. Configure scikit-learn for parallel processing
    os.environ['SKLEARN_NUM_THREADS'] = '10'
    
    # 4. PyMC-specific optimizations
    os.environ['PYMC_CHAINS'] = '4'  # Parallel chains
    os.environ['PYMC_CORES'] = '4'   # Use 4 cores for chains
    
    info("Hardware acceleration setup complete")
    
    return {
        'cpu_cores': 10,
        'gpu_cores': 18,  # M3 Pro GPU cores
        'memory_gb': 36,
        'recommendations': [
            'Use n_jobs=-1 in scikit-learn models',
            'Set chains=4 and cores=4 in PyMC',
            'Use joblib.parallel_backend("threading") for shared memory',
            'Consider batch processing for bootstrap',
            'Monitor memory usage with 36GB available'
        ]
    }


def optimize_pymc_settings():
    """Return optimized PyMC settings for M3 Pro."""
    return {
        'chains': 4,           # One chain per performance core group
        'cores': 4,            # Parallel chain execution
        'target_accept': 0.9,  # Higher for better sampling
        'max_treedepth': 12,   # Deeper trees with more memory
        'init': 'adapt_diag',  # Better initialization
        'tune': 1000,          # Standard tuning
        'draws': 2000,         # Production draws
    }


def optimize_bootstrap_settings():
    """Return optimized bootstrap settings for parallel execution."""
    return {
        'n_jobs': 10,          # Use all performance cores
        'backend': 'threading', # Shared memory backend
        'batch_size': 100,     # Process in batches
        'verbose': 10          # Progress every 10 batches
    }


def create_accelerated_config():
    """Create configuration file for accelerated execution."""
    
    config = {
        'hardware': {
            'platform': 'apple_silicon_m3_pro',
            'cpu_cores': 10,
            'gpu_cores': 18,
            'memory_gb': 36
        },
        'pymc': optimize_pymc_settings(),
        'bootstrap': optimize_bootstrap_settings(),
        'numpy': {
            'threads': 10,
            'block_size': 512  # Optimal for M3 cache
        },
        'data': {
            'chunk_size': 5000,  # Process data in chunks
            'cache_enabled': True,
            'compression': 'snappy'  # Fast compression
        }
    }
    
    # Save configuration
    import json
    config_path = Path('config/acceleration.json')
    config_path.parent.mkdir(exist_ok=True)
    
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    info(f"Saved acceleration config to {config_path}")
    
    return config


if __name__ == "__main__":
    setup_logging("INFO")
    
    # Setup acceleration
    hw_info = setup_apple_silicon_acceleration()
    
    # Create config
    config = create_accelerated_config()
    
    # Display recommendations
    info("\n" + "="*60)
    info("ACCELERATION RECOMMENDATIONS")
    info("="*60)
    
    for rec in hw_info['recommendations']:
        info(f"• {rec}")
    
    info("\nExample usage in models:")
    info("```python")
    info("# For PyMC")
    info("model = BayesianTVPVECM(")
    info("    n_samples=2000,")
    info("    n_chains=4,")
    info("    cores=4  # Add this")
    info(")")
    info("")
    info("# For bootstrap")
    info("from joblib import Parallel, delayed")
    info("results = Parallel(n_jobs=10, backend='threading')(")
    info("    delayed(bootstrap_iteration)(i) for i in range(1000)")
    info(")")
    info("```")