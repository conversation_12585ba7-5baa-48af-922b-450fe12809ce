#!/usr/bin/env python3
"""Check that all required packages are properly installed for Week 5 models."""

import sys
import importlib
from pathlib import Path

def check_import(module_name: str, package_name: str = None) -> bool:
    """Check if a module can be imported."""
    if package_name is None:
        package_name = module_name
    
    try:
        importlib.import_module(module_name)
        print(f"✅ {package_name} is installed")
        return True
    except ImportError as e:
        print(f"❌ {package_name} is NOT installed: {e}")
        return False

def main():
    print("Checking Week 5 Sprint Dependencies")
    print("=" * 50)
    
    # Essential packages
    essential = [
        ('pandas', None),
        ('numpy', None),
        ('matplotlib', None),
        ('seaborn', None),
        ('statsmodels', None),
        ('sklearn', 'scikit-learn'),
        ('scipy', None),
        ('arch', None),  # For unit root tests
    ]
    
    # Optional but recommended
    optional = [
        ('pymc', 'PyMC'),  # For Bayesian models
        ('arviz', None),   # For MCMC diagnostics
        ('linearmodels', None),  # For panel models
        ('geopandas', None),  # For spatial analysis
    ]
    
    print("\nEssential packages:")
    essential_ok = all(check_import(mod, pkg) for mod, pkg in essential)
    
    print("\nOptional packages (for advanced features):")
    optional_ok = all(check_import(mod, pkg) for mod, pkg in optional)
    
    # Check project modules
    print("\nProject modules:")
    sys.path.insert(0, str(Path(__file__).parent.parent))
    
    project_modules = [
        'src.yemen_market.utils.logging',
        'src.yemen_market.models.track2_simple.threshold_vecm',
        'src.yemen_market.models.track1_complex.tvp_vecm',
        'src.yemen_market.diagnostics.test_battery',
        'src.yemen_market.models.model_comparison',
    ]
    
    project_ok = all(check_import(mod) for mod in project_modules)
    
    # Summary
    print("\n" + "=" * 50)
    if essential_ok and project_ok:
        print("✅ All essential dependencies are installed!")
        print("✅ Project modules are accessible!")
        
        if not optional_ok:
            print("\n⚠️  Some optional packages are missing.")
            print("   Track 1 (Bayesian) models may not work without PyMC.")
            print("   Install with: pip install pymc arviz")
        else:
            print("✅ All optional packages are installed!")
            
        print("\n🚀 You're ready to run Week 5 models!")
        print("   Run: make week5-models")
        return 0
    else:
        print("❌ Some essential dependencies are missing!")
        print("\nTo fix:")
        print("1. Activate environment: conda activate yemen-market")
        print("2. Install package: pip install -e .")
        print("3. Install missing: pip install [package-name]")
        return 1

if __name__ == "__main__":
    sys.exit(main())
