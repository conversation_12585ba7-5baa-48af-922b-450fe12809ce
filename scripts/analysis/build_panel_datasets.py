#!/usr/bin/env python3
"""Script to build integrated panel datasets for Yemen market analysis.

This script:
1. Loads all processed data components
2. Integrates prices, exchange rates, and control zones
3. Creates balanced panel structures
4. Adds temporal features for time series analysis
5. Generates model-specific datasets
"""

import logging
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.data.panel_builder import PanelBuilder
from yemen_market.config.settings import ANALYSIS_CONFIG
from yemen_market.utils.logging import setup_logging, info, warning, error

# Set up logging
setup_logging()


def build_panels():
    """Execute panel building workflow."""
    info("=" * 60)
    info("Building Integrated Panel Datasets")
    info("=" * 60)
    
    # Initialize panel builder - use all commodities from config
    commodities = None  # This will make it use ANALYSIS_CONFIG commodities
    
    builder = PanelBuilder(
        commodities=commodities,
        frequency='M'  # Monthly frequency
    )
    
    if commodities is None:
        info(f"Building panels for all commodities from ANALYSIS_CONFIG")
    else:
        info(f"Building panels for {len(commodities)} commodities")
    
    try:
        # Step 1: Load all component data
        info("\nStep 1: Loading component datasets")
        data = builder.load_component_data()
        
        info("Loaded datasets:")
        for name, df in data.items():
            if not df.empty:
                info(f"  {name}: {len(df)} records")
        
        # Step 2: Create price panel
        info("\nStep 2: Creating commodity price panel")
        price_panel = builder.create_price_panel(data)
        
        if not price_panel.empty:
            info(f"Price panel: {len(price_panel)} observations")
            info(f"Markets: {price_panel['market_id'].nunique()}")
            info(f"Date range: {price_panel['date'].min()} to {price_panel['date'].max()}")
            
            # Show commodity distribution
            commodity_counts = price_panel['commodity'].value_counts()
            info("\nCommodity distribution:")
            for commodity, count in commodity_counts.items():
                info(f"  {commodity}: {count} observations")
        
        # Step 3: Create exchange rate panel
        info("\nStep 3: Creating exchange rate panel")
        exchange_panel = builder.create_exchange_rate_panel(data)
        
        if not exchange_panel.empty:
            info(f"Exchange panel: {len(exchange_panel)} observations")
            
            # Show exchange rate differentials
            if 'rate_differential_pct' in exchange_panel.columns:
                avg_diff = exchange_panel.groupby('year_month')['rate_differential_pct'].mean()
                info("\nAverage exchange rate differential (%):")
                for month, diff in avg_diff.tail(6).items():
                    info(f"  {month}: {diff:.1f}%")
        
        # Step 4: Add temporal features
        info("\nStep 4: Adding temporal features")
        price_panel = builder.add_temporal_features(price_panel)
        
        temporal_cols = [c for c in price_panel.columns if any(
            x in c for x in ['lag', 'diff', 'ma', 'std', 'trend']
        )]
        info(f"Added {len(temporal_cols)} temporal features")
        
        # Step 5: Create balanced panel
        info("\nStep 5: Creating balanced panel structure")
        balanced_panel = builder.create_balanced_panel(price_panel, min_obs_per_entity=12)
        
        info(f"Balanced panel: {len(balanced_panel)} observations")
        info(f"Entities retained: {balanced_panel['market_id'].nunique()} markets")
        
        # Check balance
        obs_per_entity = balanced_panel.groupby(['commodity', 'market_id']).size()
        info(f"Observations per entity: min={obs_per_entity.min()}, "
                   f"max={obs_per_entity.max()}, mean={obs_per_entity.mean():.1f}")
        
        # Step 6: Handle missing data
        info("\nStep 6: Handling missing data")
        
        # Report missing before
        missing_before = balanced_panel.isnull().sum()
        cols_with_missing = missing_before[missing_before > 0]
        if not cols_with_missing.empty:
            info("Missing data before handling:")
            for col, n_missing in cols_with_missing.items():
                pct = n_missing / len(balanced_panel) * 100
                info(f"  {col}: {n_missing} ({pct:.1f}%)")
        
        clean_panel = builder.handle_missing_data(balanced_panel)
        
        # Report missing after
        missing_after = clean_panel.isnull().sum()
        cols_still_missing = missing_after[missing_after > 0]
        if not cols_still_missing.empty:
            info("\nRemaining missing data:")
            for col, n_missing in cols_still_missing.items():
                pct = n_missing / len(clean_panel) * 100
                info(f"  {col}: {n_missing} ({pct:.1f}%)")
        
        # Step 7: Create model-specific panels
        info("\nStep 7: Creating model-specific datasets")
        model_panels = builder.create_model_specific_panels(clean_panel)
        
        # Add main integrated panel
        all_panels = {'integrated': clean_panel}
        all_panels.update(model_panels)
        
        info("Created panels:")
        for panel_name, panel_df in all_panels.items():
            if not panel_df.empty:
                info(f"  {panel_name}: {len(panel_df)} observations, "
                           f"{len(panel_df.columns)} variables")
        
        # Step 8: Save all panels
        info("\nStep 8: Saving panel datasets")
        saved_files = builder.save_panels(all_panels)
        
        info("Saved files:")
        for file_type, path in saved_files.items():
            info(f"  {file_type}: {path.name}")
        
        # Step 9: Generate summary report
        info("\nStep 9: Generating summary statistics")
        summary = builder.generate_panel_summary(all_panels)
        
        if not summary.empty:
            info("\nPanel summary:")
            info(summary.to_string(index=False))
        
        # Additional analysis insights
        if 'integrated' in all_panels:
            integrated = all_panels['integrated']
            
            # Control zone distribution
            if 'control_zone' in integrated.columns:
                zone_dist = integrated.groupby('control_zone')['market_id'].nunique()
                info("\n Markets by control zone:")
                for zone, n_markets in zone_dist.items():
                    info(f"  {zone}: {n_markets} markets")
            
            # Price variation
            if 'price_usd' in integrated.columns:
                price_stats = integrated.groupby('commodity')['price_usd'].agg(['mean', 'std'])
                info("\nPrice statistics by commodity (USD):")
                for commodity, stats in price_stats.iterrows():
                    info(f"  {commodity}: mean=${stats['mean']:.2f}, "
                               f"std=${stats['std']:.2f}")
        
        info("\n" + "=" * 60)
        info("Panel building completed successfully!")
        info("=" * 60)
        
    except Exception as e:
        error(f"Error in panel building: {e}")
        sys.exit(1)


def check_prerequisites():
    """Check if required processed data exists."""
    info("Checking prerequisites...")
    
    from yemen_market.config.settings import PROCESSED_DATA_DIR
    
    required_files = [
        PROCESSED_DATA_DIR / "wfp_market_panel.parquet",
        PROCESSED_DATA_DIR / "spatial" / "market_zones_temporal.parquet"
    ]
    
    missing = []
    for file in required_files:
        if not file.exists():
            missing.append(file)
    
    if missing:
        error("Missing required files:")
        for file in missing:
            error(f"  {file}")
        return False
    
    info("All prerequisites satisfied")
    return True


def main():
    """Main execution function."""
    info("Yemen Market Integration - Panel Dataset Builder")
    
    # Check prerequisites
    if not check_prerequisites():
        error("\nPlease run the following scripts first:")
        error("1. scripts/process_wfp_data.py")
        error("2. scripts/process_acaps_data.py")
        error("3. scripts/run_spatial_joins.py")
        sys.exit(1)
    
    # Build panels
    build_panels()
    
    info("\nNext steps:")
    info("1. Use integrated panel for exploratory data analysis")
    info("2. Use model-specific panels for econometric estimation")
    info("3. Check panel balance and missing data patterns")
    info("4. Consider additional feature engineering if needed")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        info("\nProcess interrupted by user")
        sys.exit(0)
    except Exception as e:
        error(f"Unexpected error: {e}")
        sys.exit(1)