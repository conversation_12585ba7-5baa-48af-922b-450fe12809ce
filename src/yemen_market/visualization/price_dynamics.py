"""Price dynamics visualization module for Yemen market integration analysis.

This module provides comprehensive visualization tools for analyzing commodity prices,
exchange rates, and market integration patterns in conflict-affected Yemen.
"""

from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
from matplotlib.figure import Figure
from matplotlib.axes import Axes
import geopandas as gpd
from matplotlib.colors import ListedColormap

from ..config.settings import FIGURES_DIR, PROCESSED_DATA_DIR
from ..utils.logging import (
    bind, timer, progress, log_data_shape,
    info, warning, error, debug
)


class PriceDynamicsVisualizer:
    """Visualize price dynamics and market integration patterns.
    
    This class provides methods for creating publication-quality visualizations
    of commodity prices, exchange rates, and spatial patterns in Yemen's markets.
    
    Attributes:
        style: Matplotlib style to use for plots
        figsize: Default figure size
        dpi: Resolution for saved figures
        color_palette: Color scheme for visualizations
    """
    
    def __init__(self, style: str = 'seaborn-v0_8-whitegrid', 
                 figsize: Tuple[int, int] = (12, 8),
                 dpi: int = 300):
        """Initialize the visualizer with style settings.
        
        Parameters
        ----------
        style : str
            Matplotlib style to use
        figsize : tuple
            Default figure size (width, height)
        dpi : int
            Resolution for saved figures
        """
        self.style = style
        self.figsize = figsize
        self.dpi = dpi
        
        # Set up style
        plt.style.use(self.style)
        
        # Define color palettes
        self.control_zone_colors = {
            'Houthi': '#E74C3C',      # Red
            'Government': '#3498DB',   # Blue
            'Contested': '#F39C12',    # Orange
            'STC': '#9B59B6',         # Purple
            'Other': '#95A5A6'        # Gray
        }
        
        self.commodity_colors = {
            'Wheat': '#8B4513',       # Brown
            'Rice': '#FFD700',        # Gold
            'Sugar': '#FF69B4',       # Pink
            'Oil': '#006400',         # Dark Green
            'Fuel': '#000080'         # Navy
        }
        
        # Create output directory
        FIGURES_DIR.mkdir(parents=True, exist_ok=True)
        
        bind(component="PriceDynamicsVisualizer")
        info("PriceDynamicsVisualizer initialized")
    
    def plot_price_series(self, 
                         data: pd.DataFrame,
                         commodity: str,
                         markets: Optional[List[str]] = None,
                         start_date: Optional[str] = None,
                         end_date: Optional[str] = None,
                         show_events: bool = True) -> Figure:
        """Plot time series of commodity prices across markets.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data with columns: date, market_name, commodity, price
        commodity : str
            Commodity to plot
        markets : list, optional
            List of markets to include (default: top 5 by volume)
        start_date : str, optional
            Start date for plot (YYYY-MM-DD)
        end_date : str, optional
            End date for plot (YYYY-MM-DD)
        show_events : bool
            Whether to show major events on timeline
            
        Returns
        -------
        Figure
            Matplotlib figure object
        """
        with timer(f"plot_price_series_{commodity}"):
            # Filter data
            mask = data['commodity'] == commodity
            if start_date:
                mask &= data['date'] >= pd.to_datetime(start_date)
            if end_date:
                mask &= data['date'] <= pd.to_datetime(end_date)
            
            plot_data = data[mask].copy()
            
            # Select markets
            if markets is None:
                # Get top 5 markets by data availability
                market_counts = plot_data.groupby('market_name').size()
                markets = market_counts.nlargest(5).index.tolist()
            
            plot_data = plot_data[plot_data['market_name'].isin(markets)]
            
            log_data_shape("plot_data", plot_data)
            
            # Create figure
            fig, ax = plt.subplots(figsize=self.figsize)
            
            # Plot each market
            for market in markets:
                market_data = plot_data[plot_data['market_name'] == market]
                ax.plot(market_data['date'], market_data['price'], 
                       label=market, linewidth=2, alpha=0.8)
            
            # Add events if requested
            if show_events:
                self._add_event_markers(ax, plot_data['date'].min(), plot_data['date'].max())
            
            # Formatting
            ax.set_xlabel('Date', fontsize=12)
            ax.set_ylabel(f'{commodity} Price (YER)', fontsize=12)
            ax.set_title(f'{commodity} Prices Across Key Markets', fontsize=14, fontweight='bold')
            ax.legend(loc='best', frameon=True, fancybox=True, shadow=True)
            ax.grid(True, alpha=0.3)
            
            # Format x-axis
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            plt.xticks(rotation=45)
            
            plt.tight_layout()
            return fig
    
    def plot_exchange_differential(self,
                                 data: pd.DataFrame,
                                 show_ma: bool = True,
                                 ma_window: int = 30) -> Figure:
        """Plot exchange rate differentials between control zones.
        
        Parameters
        ----------
        data : pd.DataFrame
            Data with columns: date, control_zone, exchange_rate
        show_ma : bool
            Whether to show moving average
        ma_window : int
            Window for moving average
            
        Returns
        -------
        Figure
            Matplotlib figure object
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(self.figsize[0], self.figsize[1]*1.2),
                                       sharex=True, height_ratios=[2, 1])
        
        # Plot exchange rates by zone
        for zone in ['Houthi', 'Government']:
            zone_data = data[data['control_zone'] == zone].groupby('date')['exchange_rate'].mean()
            
            ax1.plot(zone_data.index, zone_data.values,
                    label=f'{zone} Zone', 
                    color=self.control_zone_colors[zone],
                    linewidth=2, alpha=0.8)
            
            if show_ma and len(zone_data) > ma_window:
                ma = zone_data.rolling(window=ma_window, center=True).mean()
                ax1.plot(ma.index, ma.values,
                        color=self.control_zone_colors[zone],
                        linestyle='--', alpha=0.5)
        
        # Calculate and plot differential
        houthi_rates = data[data['control_zone'] == 'Houthi'].groupby('date')['exchange_rate'].mean()
        gov_rates = data[data['control_zone'] == 'Government'].groupby('date')['exchange_rate'].mean()
        
        # Align the series
        aligned = pd.DataFrame({'houthi': houthi_rates, 'government': gov_rates}).dropna()
        differential = ((aligned['government'] - aligned['houthi']) / aligned['houthi']) * 100
        
        ax2.plot(differential.index, differential.values,
                color='#2C3E50', linewidth=2)
        ax2.fill_between(differential.index, 0, differential.values,
                        alpha=0.3, color='#2C3E50')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # Formatting
        ax1.set_ylabel('Exchange Rate (YER/USD)', fontsize=12)
        ax1.set_title('Exchange Rates by Control Zone', fontsize=14, fontweight='bold')
        ax1.legend(loc='best', frameon=True, fancybox=True, shadow=True)
        ax1.grid(True, alpha=0.3)
        
        ax2.set_xlabel('Date', fontsize=12)
        ax2.set_ylabel('Differential (%)', fontsize=12)
        ax2.set_title('Exchange Rate Differential (Government vs Houthi)', fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        # Format x-axis
        ax2.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        return fig
    
    def plot_spatial_price_map(self,
                             data: pd.DataFrame,
                             boundaries: gpd.GeoDataFrame,
                             commodity: str,
                             date: str,
                             show_markets: bool = True) -> Figure:
        """Create spatial heatmap of commodity prices.
        
        Parameters
        ----------
        data : pd.DataFrame
            Price data with market locations
        boundaries : gpd.GeoDataFrame
            Administrative boundaries
        commodity : str
            Commodity to map
        date : str
            Date to map (YYYY-MM)
        show_markets : bool
            Whether to show market locations
            
        Returns
        -------
        Figure
            Matplotlib figure object
        """
        # Filter data for specific commodity and date
        month_data = data[(data['commodity'] == commodity) & 
                         (data['year_month'] == date)].copy()
        
        if month_data.empty:
            warning(f"No data for {commodity} in {date}")
            return None
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # Plot boundaries
        boundaries.plot(ax=ax, color='white', edgecolor='black', linewidth=0.5)
        
        # Create price heatmap by averaging prices within districts
        if 'district_pcode' in month_data.columns:
            district_prices = month_data.groupby('district_pcode')['price'].mean()
            
            # Merge with boundaries
            boundaries_with_prices = boundaries.merge(
                district_prices.to_frame(), 
                left_on='pcode', 
                right_index=True,
                how='left'
            )
            
            # Plot choropleth
            boundaries_with_prices.plot(
                column='price',
                ax=ax,
                legend=True,
                cmap='YlOrRd',
                missing_kwds={'color': 'lightgray'},
                legend_kwds={'label': f'{commodity} Price (YER)', 'orientation': 'horizontal'}
            )
        
        # Plot market locations if requested
        if show_markets and 'longitude' in month_data.columns:
            ax.scatter(month_data['longitude'], month_data['latitude'],
                      c='blue', s=50, alpha=0.6, edgecolors='black',
                      label='Markets')
            ax.legend()
        
        # Add title and labels
        ax.set_title(f'{commodity} Prices - {date}', fontsize=16, fontweight='bold')
        ax.set_xlabel('Longitude', fontsize=12)
        ax.set_ylabel('Latitude', fontsize=12)
        
        # Remove axis for cleaner look
        ax.set_axis_off()
        
        plt.tight_layout()
        return fig
    
    def plot_price_convergence(self,
                             data: pd.DataFrame,
                             commodity: str,
                             reference_market: str,
                             comparison_markets: List[str]) -> Figure:
        """Analyze price convergence patterns between markets.
        
        Parameters
        ----------
        data : pd.DataFrame
            Price data
        commodity : str
            Commodity to analyze
        reference_market : str
            Reference market for comparison
        comparison_markets : list
            Markets to compare against reference
            
        Returns
        -------
        Figure
            Matplotlib figure object
        """
        # Filter for commodity
        comm_data = data[data['commodity'] == commodity].copy()
        
        # Get reference prices
        ref_prices = comm_data[comm_data['market_name'] == reference_market].set_index('date')['price']
        
        # Create figure
        fig, axes = plt.subplots(len(comparison_markets), 1, 
                                figsize=(self.figsize[0], 4*len(comparison_markets)),
                                sharex=True)
        
        if len(comparison_markets) == 1:
            axes = [axes]
        
        for idx, market in enumerate(comparison_markets):
            ax = axes[idx]
            
            # Get comparison market prices
            comp_prices = comm_data[comm_data['market_name'] == market].set_index('date')['price']
            
            # Calculate price ratio
            ratio = comp_prices / ref_prices
            ratio = ratio.dropna()
            
            # Plot ratio
            ax.plot(ratio.index, ratio.values, linewidth=2, alpha=0.8)
            ax.axhline(y=1, color='red', linestyle='--', alpha=0.5)
            
            # Add moving average
            if len(ratio) > 30:
                ma = ratio.rolling(window=30, center=True).mean()
                ax.plot(ma.index, ma.values, color='black', linestyle='-', alpha=0.5)
            
            # Formatting
            ax.set_ylabel('Price Ratio', fontsize=10)
            ax.set_title(f'{market} / {reference_market}', fontsize=12)
            ax.grid(True, alpha=0.3)
            ax.set_ylim(0.5, 2.0)
        
        # Overall formatting
        axes[-1].set_xlabel('Date', fontsize=12)
        axes[0].suptitle(f'{commodity} Price Convergence Analysis', 
                        fontsize=14, fontweight='bold', y=1.02)
        
        # Format x-axis
        axes[-1].xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        axes[-1].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        return fig
    
    def plot_structural_breaks(self,
                             data: pd.DataFrame,
                             series_name: str = 'exchange_rate',
                             breakpoints: Optional[List[str]] = None) -> Figure:
        """Identify and visualize structural breaks in time series.
        
        Parameters
        ----------
        data : pd.DataFrame
            Time series data
        series_name : str
            Column name to analyze
        breakpoints : list, optional
            Known breakpoint dates
            
        Returns
        -------
        Figure
            Matplotlib figure object
        """
        # Aggregate series
        series = data.groupby('date')[series_name].mean()
        
        # Create figure
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.figsize,
                                       height_ratios=[2, 1], sharex=True)
        
        # Plot series
        ax1.plot(series.index, series.values, linewidth=2, alpha=0.8, label='Original')
        
        # Add breakpoints if provided
        if breakpoints:
            for bp in breakpoints:
                bp_date = pd.to_datetime(bp)
                if bp_date in series.index:
                    ax1.axvline(x=bp_date, color='red', linestyle='--', alpha=0.7)
                    ax1.text(bp_date, ax1.get_ylim()[1]*0.95, bp, 
                           rotation=90, verticalalignment='top')
        
        # Calculate and plot rolling statistics
        window = 60  # 2 months
        rolling_mean = series.rolling(window=window, center=True).mean()
        rolling_std = series.rolling(window=window, center=True).std()
        
        ax1.plot(rolling_mean.index, rolling_mean.values, 
                color='red', linewidth=2, label=f'{window}-day MA')
        ax1.fill_between(rolling_std.index,
                        rolling_mean - 2*rolling_std,
                        rolling_mean + 2*rolling_std,
                        alpha=0.2, color='gray')
        
        # Plot volatility
        ax2.plot(rolling_std.index, rolling_std.values, 
                color='green', linewidth=2)
        ax2.fill_between(rolling_std.index, 0, rolling_std.values,
                        alpha=0.3, color='green')
        
        # Formatting
        ax1.set_ylabel(series_name.replace('_', ' ').title(), fontsize=12)
        ax1.set_title('Structural Break Analysis', fontsize=14, fontweight='bold')
        ax1.legend(loc='best')
        ax1.grid(True, alpha=0.3)
        
        ax2.set_xlabel('Date', fontsize=12)
        ax2.set_ylabel('Volatility', fontsize=12)
        ax2.set_title('Rolling Standard Deviation', fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        # Format x-axis
        ax2.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        return fig
    
    def _add_event_markers(self, ax: Axes, start_date: datetime, end_date: datetime):
        """Add vertical lines for major events.
        
        Parameters
        ----------
        ax : Axes
            Matplotlib axes object
        start_date : datetime
            Start of plot range
        end_date : datetime
            End of plot range
        """
        events = {
            '2014-09-21': 'Houthis take Sana\'a',
            '2015-03-26': 'Saudi intervention begins',
            '2016-09-18': 'Central Bank moved to Aden',
            '2017-11-04': 'Saudi blockade begins',
            '2018-06-13': 'Hodeidah offensive',
            '2020-01-18': 'Riyadh Agreement',
            '2022-04-02': 'UN-mediated truce',
            '2023-12-26': 'Red Sea attacks begin'
        }
        
        for date_str, event in events.items():
            event_date = pd.to_datetime(date_str)
            if start_date <= event_date <= end_date:
                ax.axvline(x=event_date, color='gray', linestyle=':', alpha=0.5)
                ax.text(event_date, ax.get_ylim()[1], event,
                       rotation=90, verticalalignment='bottom',
                       fontsize=9, alpha=0.7)
    
    def save_figure(self, fig: Figure, filename: str, 
                   formats: List[str] = ['png', 'pdf']):
        """Save figure in multiple formats.
        
        Parameters
        ----------
        fig : Figure
            Matplotlib figure object
        filename : str
            Base filename (without extension)
        formats : list
            List of formats to save
        """
        for fmt in formats:
            filepath = FIGURES_DIR / f"{filename}.{fmt}"
            fig.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
            info(f"Saved figure to {filepath}")