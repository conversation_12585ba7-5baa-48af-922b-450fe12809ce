# ACLEDProcessor API Reference

The `ACLEDProcessor` class processes Armed Conflict Location & Event Data (ACLED) for Yemen, creating market-level conflict metrics.

## Class: ACLEDProcessor

```python
from yemen_market.data import ACLEDProcessor

processor = ACLEDProcessor(
    start_date=None,
    end_date=None,
    buffer_km=50
)
```

### Parameters

- **start_date** (str, optional): Start date for filtering. Defaults to config value.
- **end_date** (str, optional): End date for filtering. Defaults to config value.
- **buffer_km** (float): Buffer radius in kilometers for spatial matching (default: 50).

### Methods

#### load_acled_data()
Load and preprocess ACLED event data.

```python
events_gdf = processor.load_acled_data(file_path=None)
```

**Parameters:**
- **file_path** (str, optional): Path to ACLED CSV file

**Returns:**
- **events_gdf** (GeoDataFrame): Preprocessed ACLED events with geometries

#### calculate_conflict_metrics()
Calculate comprehensive conflict metrics at market-month level.

```python
metrics = processor.calculate_conflict_metrics(
    events_gdf,
    markets_gdf,
    spatial_method='buffer'
)
```

**Parameters:**
- **events_gdf** (GeoDataFrame): ACLED events
- **markets_gdf** (GeoDataFrame): Market locations
- **spatial_method** (str): Method for spatial matching ('buffer' or 'nearest')

**Returns:**
- **metrics** (DataFrame): Conflict metrics by market and month

#### process_acled_data()
Main processing pipeline for ACLED data.

```python
conflict_metrics = processor.process_acled_data(
    wfp_markets_path=None,
    save_output=True
)
```

**Parameters:**
- **wfp_markets_path** (str, optional): Path to WFP markets data
- **save_output** (bool): Save processed data to disk

**Returns:**
- **conflict_metrics** (DataFrame): Complete conflict metrics dataset

### Conflict Metrics

The processor calculates the following metrics:

```python
# Event counts
- conflict_events      # Total events
- conflict_fatalities  # Total fatalities
- n_battles           # Battle events
- n_explosions        # Explosion/remote violence
- n_protests          # Protests
- n_riots             # Riots
- n_strategic         # Strategic developments
- n_violence_civilians # Violence against civilians

# Actor-specific counts
- n_events_houthis    # Houthi-involved events
- n_events_government # Government-involved events
- n_events_stc        # STC-involved events
- n_events_aqap       # AQAP-involved events
- n_events_isis       # ISIS-involved events
- n_events_other      # Other actors

# Derived metrics
- conflict_intensity  # Log(events + 1)
- fatality_rate      # Fatalities per event
```

### Event Type Classification

```python
EVENT_TYPE_MAPPING = {
    'Battles': 'battles',
    'Explosions/Remote violence': 'explosions',
    'Protests': 'protests',
    'Riots': 'riots',
    'Strategic developments': 'strategic',
    'Violence against civilians': 'violence_civilians'
}
```

### Example Usage

```python
from yemen_market.data import ACLEDProcessor

# Initialize processor
processor = ACLEDProcessor(
    start_date='2019-01-01',
    buffer_km=50  # 50km buffer around markets
)

# Process ACLED data
conflict_metrics = processor.process_acled_data()

# Check results
print(f"Processed {len(conflict_metrics)} market-month observations")
print(f"Conflict coverage: {conflict_metrics['conflict_events'].notna().mean():.1%}")

# Get high-conflict markets
high_conflict = conflict_metrics[
    conflict_metrics['conflict_intensity'] > conflict_metrics['conflict_intensity'].quantile(0.9)
]
print(f"High conflict observations: {len(high_conflict)}")
```

### Spatial Matching

The processor uses two methods for matching events to markets:

1. **Buffer Method** (default):
   - Creates circular buffers around markets
   - Counts events within buffer radius
   - More accurate but computationally intensive

2. **Nearest Method**:
   - Assigns events to nearest market
   - Filters by maximum distance
   - Faster but less precise

### Data Quality

- Validates event coordinates
- Handles missing actor information
- Filters by date range
- Removes duplicate events
- Logs processing statistics