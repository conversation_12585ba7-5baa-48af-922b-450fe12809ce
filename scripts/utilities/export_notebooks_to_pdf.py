#!/usr/bin/env python3
"""Export Jupyter notebooks to PDF format.

This script converts all analysis notebooks to PDF for easy sharing and archival.
Requires: jupyter, nbconvert, and pandoc/latex for PDF generation.
"""

import sys
import subprocess
from pathlib import Path
import json

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.utils.logging import info, warning, error, bind, timer

# Set up logging
bind(module=__name__)


def check_dependencies():
    """Check if required dependencies are installed."""
    info("Checking dependencies...")
    
    dependencies = {
        'jupyter': 'pip install jupyter',
        'nbconvert': 'pip install nbconvert',
        'pandoc': 'brew install pandoc (macOS) or apt-get install pandoc (Linux)'
    }
    
    has_all = True
    
    # Check Python packages
    for package in ['jupyter', 'nbconvert']:
        try:
            __import__(package)
            info(f"✅ {package} is installed")
        except ImportError:
            error(f"❌ {package} is not installed. Run: {dependencies[package]}")
            has_all = False
    
    # Check pandoc
    try:
        result = subprocess.run(['pandoc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            info("✅ pandoc is installed")
        else:
            raise FileNotFoundError
    except FileNotFoundError:
        error(f"❌ pandoc is not installed. {dependencies['pandoc']}")
        has_all = False
    
    return has_all


def export_notebook_to_pdf(notebook_path: Path, output_dir: Path):
    """Export a single notebook to PDF.
    
    Args:
        notebook_path: Path to the notebook
        output_dir: Directory to save PDF
    
    Returns:
        True if successful, False otherwise
    """
    output_path = output_dir / f"{notebook_path.stem}.pdf"
    
    info(f"Converting {notebook_path.name} to PDF...")
    
    try:
        # First, try direct PDF export
        cmd = [
            'jupyter', 'nbconvert',
            '--to', 'pdf',
            '--output', str(output_path),
            '--output-dir', str(output_dir),
            str(notebook_path)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            info(f"✅ Created: {output_path.name}")
            return True
        else:
            # If direct PDF fails, try HTML then PDF
            warning("Direct PDF conversion failed, trying HTML route...")
            
            # Convert to HTML first
            html_path = output_dir / f"{notebook_path.stem}.html"
            cmd_html = [
                'jupyter', 'nbconvert',
                '--to', 'html',
                '--output', str(html_path),
                str(notebook_path)
            ]
            
            result_html = subprocess.run(cmd_html, capture_output=True, text=True)
            
            if result_html.returncode == 0:
                info(f"✅ Created HTML: {html_path.name}")
                warning("Note: Install latex for direct PDF conversion: 'brew install basictex' (macOS)")
                return True
            else:
                error(f"Failed to convert {notebook_path.name}")
                error(f"Error: {result.stderr}")
                return False
                
    except Exception as e:
        error(f"Error converting {notebook_path.name}: {e}")
        return False


def create_notebook_index(notebooks: list, output_dir: Path):
    """Create an index file listing all notebooks and their purposes."""
    index_path = output_dir / "notebook_index.md"
    
    content = ["# Yemen Market Integration - Notebook Index\n\n"]
    content.append("This directory contains PDF exports of all analysis notebooks.\n\n")
    content.append("## Notebooks\n\n")
    
    notebook_descriptions = {
        "01-data-validation": "Data quality checks, missing data analysis, and validation statistics",
        "02-price-patterns": "Price dynamics, threshold detection, structural breaks, and correlations",
        "03-spatial-analysis": "Spatial patterns, Moran's I statistics, LISA clustering, and boundary effects"
    }
    
    for nb_path in sorted(notebooks):
        nb_name = nb_path.stem
        desc = notebook_descriptions.get(nb_name, "Analysis notebook")
        pdf_name = f"{nb_name}.pdf"
        
        content.append(f"### {nb_name}\n")
        content.append(f"- **Description**: {desc}\n")
        content.append(f"- **PDF**: [{pdf_name}](./{pdf_name})\n")
        content.append(f"- **Source**: [notebooks/{nb_path.name}](../notebooks/{nb_path.name})\n\n")
    
    with open(index_path, 'w') as f:
        f.write(''.join(content))
    
    info(f"✅ Created notebook index: {index_path.name}")


def main():
    """Main function to export all notebooks."""
    info("=" * 60)
    info("Exporting Notebooks to PDF")
    info("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        error("\nPlease install missing dependencies and try again.")
        return 1
    
    # Find all notebooks
    notebook_dir = project_root / "notebooks"
    notebooks = list(notebook_dir.glob("*.ipynb"))
    
    if not notebooks:
        error("No notebooks found in notebooks/ directory")
        return 1
    
    info(f"\nFound {len(notebooks)} notebooks to convert")
    
    # Create output directory
    output_dir = project_root / "reports" / "notebooks_pdf"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Convert each notebook
    with timer("notebook_conversion"):
        success_count = 0
        for notebook in sorted(notebooks):
            if export_notebook_to_pdf(notebook, output_dir):
                success_count += 1
    
    # Create index file
    create_notebook_index(notebooks, output_dir)
    
    # Summary
    info("\n" + "=" * 60)
    info(f"Conversion complete: {success_count}/{len(notebooks)} notebooks exported")
    info(f"Output directory: {output_dir}")
    
    if success_count < len(notebooks):
        warning("\nSome notebooks failed to convert to PDF.")
        warning("HTML versions may have been created instead.")
        warning("For full PDF support, install: brew install basictex (macOS)")
    
    return 0 if success_count > 0 else 1


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        info("\nExport interrupted by user")
        sys.exit(1)
    except Exception as e:
        error(f"Unexpected error: {e}")
        sys.exit(1)