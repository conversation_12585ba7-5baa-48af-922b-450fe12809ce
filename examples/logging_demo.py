"""
Example usage of the enhanced logging system for Yemen Market Integration Analysis.

This script demonstrates how to use the various features of our logging system
in the context of econometric analysis workflows.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import time

# Import our enhanced logger
from yemen_market.utils.logging import (
    EconometricLogger, LogConfig,
    info, debug, warning, error,
    bind, context, timer, progress,
    log_execution, log_metric, log_param,
    log_model_performance, log_data_shape
)


def example_basic_usage():
    """Basic logging examples."""
    print("\n=== Basic Logging ===")
    
    # Simple messages
    info("Starting Yemen market integration analysis")
    debug("Loading configuration files")
    warning("Exchange rate data for 2020-Q1 is incomplete")
    
    # With context
    bind(market="Sana'a", commodity="wheat")
    info("Processing price data")
    
    # Structured data
    info("Price statistics calculated", 
         stats={"mean": 45.2, "std": 3.4, "n_obs": 1200})


def example_performance_tracking():
    """Performance tracking examples."""
    print("\n=== Performance Tracking ===")
    
    # Simple timer
    with timer("data_loading"):
        time.sleep(0.5)  # Simulate data loading
        info("Loaded 50,000 price observations")
    
    # Manual timing
    from yemen_market.utils.logging import start_timer, stop_timer
    
    start_timer("model_fitting")
    time.sleep(0.3)  # Simulate model fitting
    duration = stop_timer("model_fitting")
    info(f"Model fitted in {duration:.2f} seconds")


def example_experiment_tracking():
    """Experiment tracking examples."""
    print("\n=== Experiment Tracking ===")
    
    # Log hyperparameters
    log_param("threshold_pct_min", 0.15)
    log_param("threshold_pct_max", 0.85)
    log_param("n_lags", 2)
    log_param("bootstrap_reps", 1000)
    
    # Log metrics during training
    for epoch in range(3):
        log_metric("log_likelihood", -1000 + epoch * 50, step=epoch)
        log_metric("aic", 2050 - epoch * 45, step=epoch)
    
    # Log model performance
    log_model_performance(
        "threshold_vecm",
        {
            "rmse": 0.045,
            "mae": 0.032,
            "r2": 0.89,
            "threshold_value": 0.23
        }
    )


def example_progress_tracking():
    """Progress tracking examples."""
    print("\n=== Progress Tracking ===")
    
    # Simulate bootstrap simulation
    n_bootstrap = 20
    with progress("Bootstrap simulation", total=n_bootstrap) as update:
        for i in range(n_bootstrap):
            time.sleep(0.1)  # Simulate computation
            update(1, f"Bootstrap iteration {i+1}/{n_bootstrap}")
            
            # Log results periodically
            if (i + 1) % 5 == 0:
                info(f"Completed {i+1} bootstrap iterations")


def example_data_pipeline():
    """Data pipeline logging examples."""
    print("\n=== Data Pipeline Logging ===")
    
    # Create sample data
    dates = pd.date_range("2015-01-01", "2023-12-31", freq="M")
    n_markets = 10
    
    # Log data shapes
    price_data = pd.DataFrame(
        np.random.randn(len(dates), n_markets),
        index=dates,
        columns=[f"Market_{i}" for i in range(n_markets)]
    )
    
    log_data_shape("raw_prices", price_data)
    
    # Process with context
    with context(pipeline_stage="preprocessing"):
        info("Removing outliers")
        cleaned_data = price_data.copy()
        log_data_shape("cleaned_prices", cleaned_data)
        
        info("Calculating returns")
        returns = cleaned_data.pct_change().dropna()
        log_data_shape("returns", returns)


@log_execution(level="INFO")
def example_decorated_function(n_simulations: int = 100):
    """Example of using the execution decorator."""
    info(f"Running {n_simulations} simulations")
    
    results = []
    for i in range(n_simulations):
        if i == 50:
            warning("Half-way through simulations")
        results.append(np.random.normal(0, 1))
    
    info(f"Simulations complete. Mean result: {np.mean(results):.4f}")
    return results


def example_error_handling():
    """Error handling and exception logging."""
    print("\n=== Error Handling ===")
    
    try:
        # Simulate data processing error
        data = pd.DataFrame({"price": [1, 2, None, 4]})
        
        with context(operation="calculate_mean_price"):
            # This will fail due to None value
            mean_price = data["price"].mean()
            
    except Exception as e:
        error("Failed to calculate mean price", 
              data_shape=data.shape,
              null_count=data.isnull().sum().sum())
        # Log full exception with traceback
        from yemen_market.utils.logging import exception
        exception("Detailed error information")


def example_custom_configuration():
    """Example of custom logger configuration."""
    print("\n=== Custom Configuration ===")
    
    # Create custom configuration
    config = LogConfig(
        log_dir=Path("custom_logs"),
        console_level="DEBUG",
        file_level="TRACE",
        rotation="50 MB",
        retention="7 days",
        track_experiments=True,
        track_performance=True
    )
    
    # Create custom logger instance
    custom_logger = EconometricLogger(config)
    
    custom_logger.info("Using custom logger configuration")
    custom_logger.debug("This debug message will appear in console")
    
    # Use with context
    with custom_logger.context(custom_setting="enabled"):
        custom_logger.info("Logging with custom context")


def example_econometric_workflow():
    """Complete econometric analysis workflow with logging."""
    print("\n=== Econometric Workflow Example ===")
    
    # Bind analysis context
    bind(
        analysis="threshold_cointegration",
        country="Yemen",
        period="2015-2023"
    )
    
    info("Starting threshold cointegration analysis for Yemen markets")
    
    # 1. Data loading phase
    with context(phase="data_loading"):
        with timer("load_price_data"):
            info("Loading WFP price data")
            # Simulate data loading
            time.sleep(0.2)
            log_data_shape("wfp_prices", pd.DataFrame(np.random.randn(1000, 20)))
            
        with timer("load_exchange_rates"):
            info("Loading exchange rate data")
            time.sleep(0.1)
            log_data_shape("exchange_rates", pd.DataFrame(np.random.randn(1000, 2)))
    
    # 2. Preprocessing phase
    with context(phase="preprocessing"):
        info("Starting data preprocessing")
        
        # Log data quality metrics
        info("Data quality check", 
             quality_metrics={
                 "missing_pct": 2.3,
                 "outlier_pct": 0.5,
                 "coverage": "95%"
             })
    
    # 3. Model estimation phase
    with context(phase="model_estimation"):
        log_param("model_type", "threshold_vecm")
        log_param("n_regimes", 2)
        
        # Simulate model fitting with progress
        with progress("Estimating threshold VECM", total=100) as update:
            for iteration in range(100):
                time.sleep(0.02)
                
                if iteration % 20 == 0:
                    log_metric("convergence_metric", 1.0 - iteration/100, step=iteration)
                    
                update(1, f"Grid search iteration {iteration}")
        
        # Log final results
        log_model_performance(
            "final_threshold_vecm",
            {
                "log_likelihood": -1234.56,
                "aic": 2489.12,
                "bic": 2534.78,
                "threshold_1": 0.15,
                "threshold_2": 0.42,
                "adjustment_speed_regime1": 0.23,
                "adjustment_speed_regime2": 0.67
            }
        )
    
    # 4. Diagnostics phase
    with context(phase="diagnostics"):
        info("Running diagnostic tests")
        
        diagnostic_results = {
            "ljung_box_pvalue": 0.34,
            "arch_test_pvalue": 0.56,
            "normality_test_pvalue": 0.12,
            "stability_test": "passed"
        }
        
        info("Diagnostic test results", diagnostics=diagnostic_results)
        
        # Check for issues
        for test, value in diagnostic_results.items():
            if isinstance(value, float) and value < 0.05:
                warning(f"Diagnostic test {test} shows potential issues: p-value={value}")
    
    info("Threshold cointegration analysis completed successfully")


if __name__ == "__main__":
    # Run all examples
    print("Yemen Market Integration - Enhanced Logging Examples")
    print("=" * 50)
    
    example_basic_usage()
    example_performance_tracking()
    example_experiment_tracking()
    example_progress_tracking()
    example_data_pipeline()
    example_decorated_function()
    example_error_handling()
    example_custom_configuration()
    example_econometric_workflow()
    
    print("\n" + "=" * 50)
    print("All examples completed. Check logs/ directory for output files.")
