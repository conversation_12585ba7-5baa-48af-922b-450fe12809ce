name: Pylint

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  lint:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10"]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pylint
        # Install project dependencies for proper import resolution
        pip install -e .
        # Install dev dependencies including ruff, black, mypy
        pip install -e ".[dev]"
    
    - name: Run Pylint on source code
      run: |
        pylint src/yemen_market/ --fail-under=8.0 \
          --disable=C0114,C0115,C0116 \
          --disable=R0903,R0913,R0914 \
          --disable=W0613,W0622 \
          --disable=E1101 \
          --max-line-length=88 \
          --good-names=i,j,k,df,ax,fig,id,x,y,z \
          --extension-pkg-whitelist=numpy,pandas,scipy,statsmodels
    
    - name: Run Pylint on scripts
      run: |
        pylint scripts/*.py --fail-under=7.5 \
          --disable=C0114,C0115,C0116 \
          --disable=R0903,R0913,R0914 \
          --disable=W0613,W0622 \
          --disable=E1101 \
          --max-line-length=88 \
          --good-names=i,j,k,df,ax,fig,id,x,y,z \
          --extension-pkg-whitelist=numpy,pandas,scipy,statsmodels
    
    - name: Run Pylint on tests
      run: |
        pylint tests/ --fail-under=7.0 \
          --disable=C0114,C0115,C0116 \
          --disable=R0903,R0913,R0914 \
          --disable=W0613,W0622,W0212 \
          --disable=E1101 \
          --disable=R0801 \
          --max-line-length=88 \
          --good-names=i,j,k,df,ax,fig,id,x,y,z \
          --extension-pkg-whitelist=numpy,pandas,scipy,statsmodels,pytest
    
    - name: Run Ruff
      run: |
        ruff check src/ tests/ scripts/
    
    - name: Check Black formatting
      run: |
        black --check src/ tests/ scripts/
    
    - name: Run MyPy
      run: |
        mypy src/
