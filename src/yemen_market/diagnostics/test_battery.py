"""Comprehensive diagnostic test battery for econometric models.

This module provides a unified interface to run all diagnostic tests
on fitted models, organizing results by test category and providing
clear interpretation guidance.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
import warnings

from ..utils.logging import bind, timer, info, warning, error, log_metric, progress
from ..models.base import BaseEconometricModel


@dataclass
class DiagnosticResult:
    """Container for a single diagnostic test result."""
    
    test_name: str
    category: str
    statistic: float
    p_value: Optional[float]
    critical_value: Optional[float]
    passed: bool
    interpretation: str
    details: Dict[str, Any]


class DiagnosticTestBattery:
    """Comprehensive diagnostic testing framework for econometric models.
    
    Organizes and runs diagnostic tests in six categories:
    1. Data Quality: Missing data, outliers, structural breaks
    2. Pre-Estimation: Unit roots, cointegration
    3. Specification: Threshold effects, spatial correlation
    4. Post-Estimation: Residual diagnostics
    5. Robustness: Sample sensitivity, parameter stability
    6. Validation: Out-of-sample performance
    """
    
    def __init__(self, model: BaseEconometricModel):
        """Initialize test battery for a specific model.
        
        Args:
            model: Fitted econometric model to test
        """
        self.model = model
        self.results: Dict[str, List[DiagnosticResult]] = {
            'data_quality': [],
            'pre_estimation': [],
            'specification': [],
            'post_estimation': [],
            'robustness': [],
            'validation': []
        }
        
        bind(diagnostic_battery=model.name)
    
    def run_all_tests(self, 
                     skip_categories: Optional[List[str]] = None,
                     include_slow: bool = True) -> Dict[str, List[DiagnosticResult]]:
        """Run all applicable diagnostic tests.
        
        Args:
            skip_categories: Categories to skip
            include_slow: Whether to include computationally intensive tests
            
        Returns:
            Dictionary of results by category
        """
        skip_categories = skip_categories or []
        
        with timer("diagnostic_battery"):
            info(f"Running comprehensive diagnostics for {self.model.name}")
            
            # Data quality tests (always run unless skipped)
            if 'data_quality' not in skip_categories:
                self._run_data_quality_tests()
            
            # Pre-estimation tests
            if 'pre_estimation' not in skip_categories:
                self._run_pre_estimation_tests()
            
            # Specification tests
            if 'specification' not in skip_categories:
                self._run_specification_tests()
            
            # Post-estimation tests (only if model is fitted)
            if 'post_estimation' not in skip_categories and self.model.is_fitted:
                self._run_post_estimation_tests()
            
            # Robustness tests (optional, can be slow)
            if 'robustness' not in skip_categories and include_slow:
                self._run_robustness_tests()
            
            # Validation tests (requires holdout data)
            if 'validation' not in skip_categories:
                self._run_validation_tests()
            
            # Summarize results
            self._summarize_results()
            
        return self.results
    
    def _run_data_quality_tests(self) -> None:
        """Run data quality diagnostic tests."""
        info("Running data quality tests")
        
        with progress("Data quality tests", total=4) as update:
            # Missing data patterns
            result = data_quality.test_missing_data_patterns(self.model.data)
            self.results['data_quality'].append(result)
            update(1)
            
            # Outlier detection
            result = data_quality.test_outliers(self.model.data)
            self.results['data_quality'].append(result)
            update(1)
            
            # Structural breaks
            result = data_quality.test_structural_breaks(self.model.data)
            self.results['data_quality'].append(result)
            update(1)
            
            # Data stationarity preview
            result = data_quality.test_stationarity_preview(self.model.data)
            self.results['data_quality'].append(result)
            update(1)
    
    def _run_pre_estimation_tests(self) -> None:
        """Run pre-estimation diagnostic tests."""
        info("Running pre-estimation tests")
        
        with progress("Pre-estimation tests", total=3) as update:
            # Unit root tests
            result = pre_estimation.test_unit_roots_battery(self.model.data)
            self.results['pre_estimation'].append(result)
            update(1)
            
            # Cointegration tests
            if hasattr(self.model, 'n_coint') and self.model.n_coint > 0:
                result = pre_estimation.test_cointegration(self.model.data)
                self.results['pre_estimation'].append(result)
                update(1)
            
            # Lag order selection
            result = pre_estimation.test_lag_order_selection(self.model.data)
            self.results['pre_estimation'].append(result)
            update(1)
    
    def _run_specification_tests(self) -> None:
        """Run model specification tests."""
        info("Running specification tests")
        
        tests_to_run = []
        
        # Threshold tests (if applicable)
        if hasattr(self.model, 'threshold'):
            tests_to_run.append(('threshold', specification.test_threshold_effects))
        
        # Spatial tests (if applicable)
        if hasattr(self.model, 'spatial_weights'):
            tests_to_run.append(('spatial', specification.test_spatial_correlation))
        
        # Linearity tests
        tests_to_run.append(('linearity', specification.test_linearity))
        
        # Parameter constancy
        tests_to_run.append(('constancy', specification.test_parameter_constancy))
        
        with progress("Specification tests", total=len(tests_to_run)) as update:
            for test_name, test_func in tests_to_run:
                try:
                    result = test_func(self.model)
                    self.results['specification'].append(result)
                except Exception as e:
                    warning(f"Failed to run {test_name} test: {e}")
                update(1)
    
    def _run_post_estimation_tests(self) -> None:
        """Run post-estimation diagnostic tests."""
        if not self.model.is_fitted:
            warning("Model not fitted, skipping post-estimation tests")
            return
        
        info("Running post-estimation tests")
        
        with progress("Post-estimation tests", total=5) as update:
            # Serial correlation
            result = post_estimation.test_serial_correlation(self.model)
            self.results['post_estimation'].append(result)
            update(1)
            
            # Heteroskedasticity
            result = post_estimation.test_heteroskedasticity(self.model)
            self.results['post_estimation'].append(result)
            update(1)
            
            # Normality
            result = post_estimation.test_normality(self.model)
            self.results['post_estimation'].append(result)
            update(1)
            
            # ARCH effects
            result = post_estimation.test_arch_effects(self.model)
            self.results['post_estimation'].append(result)
            update(1)
            
            # Model stability
            result = post_estimation.test_parameter_stability(self.model)
            self.results['post_estimation'].append(result)
            update(1)
    
    def _run_robustness_tests(self) -> None:
        """Run robustness tests."""
        info("Running robustness tests")
        
        with progress("Robustness tests", total=3) as update:
            # Sample sensitivity
            result = robustness.test_sample_sensitivity(self.model)
            self.results['robustness'].append(result)
            update(1)
            
            # Influential observations
            result = robustness.test_influential_observations(self.model)
            self.results['robustness'].append(result)
            update(1)
            
            # Bootstrap inference
            result = robustness.test_bootstrap_inference(self.model, n_boot=100)
            self.results['robustness'].append(result)
            update(1)
    
    def _run_validation_tests(self) -> None:
        """Run validation tests."""
        info("Running validation tests")
        
        # Check if we have enough data for validation
        if not hasattr(self.model, 'data') or len(self.model.data.get('dates', [])) < 50:
            warning("Insufficient data for validation tests")
            return
        
        with progress("Validation tests", total=2) as update:
            # Out-of-sample forecast accuracy
            result = validation.test_forecast_accuracy(self.model)
            self.results['validation'].append(result)
            update(1)
            
            # Cross-validation
            result = validation.test_cross_validation(self.model, n_folds=5)
            self.results['validation'].append(result)
            update(1)
    
    def _summarize_results(self) -> None:
        """Summarize diagnostic test results."""
        info("=" * 60)
        info("DIAGNOSTIC TEST SUMMARY")
        info("=" * 60)
        
        total_tests = 0
        total_passed = 0
        critical_failures = []
        
        for category, results in self.results.items():
            if not results:
                continue
                
            passed = sum(1 for r in results if r.passed)
            total = len(results)
            total_tests += total
            total_passed += passed
            
            info(f"\n{category.upper()} ({passed}/{total} passed)")
            info("-" * 40)
            
            for result in results:
                status = "✓" if result.passed else "✗"
                info(f"{status} {result.test_name}: {result.interpretation}")
                
                if not result.passed and category in ['post_estimation', 'specification']:
                    critical_failures.append(f"{category}/{result.test_name}")
        
        info("\n" + "=" * 60)
        if total_tests:
            pass_rate = 100 * total_passed / total_tests
        else:
            pass_rate = 0.0
        info(f"OVERALL: {total_passed}/{total_tests} tests passed ({pass_rate:.1f}%)")
        
        if critical_failures:
            warning(f"Critical test failures: {', '.join(critical_failures)}")
            warning("Model results may be unreliable")
        elif total_passed == total_tests:
            info("All diagnostic tests passed! ✓")
        else:
            info("Some non-critical tests failed, results should be interpreted with caution")
        
        log_metric("diagnostic_tests_passed", total_passed)
        log_metric("diagnostic_tests_total", total_tests)
        log_metric("diagnostic_pass_rate", total_passed/total_tests if total_tests > 0 else 0)
    
    def generate_report(self, output_path: Optional[str] = None) -> str:
        """Generate detailed diagnostic report.
        
        Args:
            output_path: Optional path to save report
            
        Returns:
            Report as string
        """
        report = []
        report.append(f"# Diagnostic Report for {self.model.name}")
        report.append(f"\nGenerated: {pd.Timestamp.now()}")
        report.append("\n## Summary")
        
        # Overall statistics
        total_tests = sum(len(results) for results in self.results.values())
        total_passed = sum(
            sum(1 for r in results if r.passed) 
            for results in self.results.values()
        )
        
        report.append(f"- Total tests run: {total_tests}")
        report.append(f"- Tests passed: {total_passed}")
        report.append(f"- Pass rate: {100*total_passed/total_tests:.1f}%")
        
        # Detailed results by category
        for category, results in self.results.items():
            if not results:
                continue
                
            report.append(f"\n## {category.replace('_', ' ').title()}")
            
            for result in results:
                report.append(f"\n### {result.test_name}")
                report.append(f"- **Status**: {'PASSED' if result.passed else 'FAILED'}")
                report.append(f"- **Statistic**: {result.statistic:.4f}")
                
                if result.p_value is not None:
                    report.append(f"- **P-value**: {result.p_value:.4f}")
                
                if result.critical_value is not None:
                    report.append(f"- **Critical value**: {result.critical_value:.4f}")
                
                report.append(f"- **Interpretation**: {result.interpretation}")
                
                if result.details:
                    report.append("- **Details**:")
                    for key, value in result.details.items():
                        if isinstance(value, float):
                            report.append(f"  - {key}: {value:.4f}")
                        else:
                            report.append(f"  - {key}: {value}")
        
        # Recommendations
        report.append("\n## Recommendations")
        
        critical_failures = [
            (cat, r) for cat, results in self.results.items() 
            for r in results 
            if not r.passed and cat in ['post_estimation', 'specification']
        ]
        
        if critical_failures:
            report.append("\n### Critical Issues")
            for cat, result in critical_failures:
                report.append(f"- **{result.test_name}**: {result.interpretation}")
                report.append(f"  - Consider: {self._get_recommendation(result)}")
        
        report_text = "\n".join(report)
        
        if output_path:
            with open(output_path, 'w') as f:
                f.write(report_text)
            info(f"Diagnostic report saved to {output_path}")
        
        return report_text
    
    def _get_recommendation(self, result: DiagnosticResult) -> str:
        """Get recommendation for failed test.
        
        Args:
            result: Failed test result
            
        Returns:
            Recommendation string
        """
        recommendations = {
            'serial_correlation': "Add more lags or consider HAC standard errors",
            'heteroskedasticity': "Use robust standard errors or transform variables",
            'normality': "Check for outliers or consider robust estimation",
            'arch_effects': "Consider GARCH modeling for volatility",
            'parameter_stability': "Test for structural breaks or use rolling estimation",
            'threshold_effects': "Consider threshold or regime-switching models",
            'spatial_correlation': "Include spatial lags or use spatial models"
        }
        
        for key, rec in recommendations.items():
            if key in result.test_name.lower():
                return rec
        
        return "Review model specification and data quality"


def run_standard_tests(model: BaseEconometricModel,
                      tests: Optional[List[str]] = None,
                      **kwargs) -> Dict[str, Any]:
    """Convenience function to run standard diagnostic tests.
    
    Args:
        model: Fitted model to test
        tests: Optional list of specific tests to run
        **kwargs: Additional arguments for test battery
        
    Returns:
        Dictionary of test results
    """
    battery = DiagnosticTestBattery(model)
    
    if tests:
        # Run only specified tests
        skip_categories = [
            cat for cat in battery.results.keys()
            if not any(test in cat for test in tests)
        ]
        results = battery.run_all_tests(skip_categories=skip_categories, **kwargs)
    else:
        # Run all tests
        results = battery.run_all_tests(**kwargs)
    
    # Convert to simple dictionary format
    output = {}
    for category, test_results in results.items():
        for result in test_results:
            output[result.test_name] = {
                'statistic': result.statistic,
                'p_value': result.p_value,
                'passed': result.passed,
                'interpretation': result.interpretation
            }
    
    return output


# Import all test modules after DiagnosticResult is defined
from .tests import (
    data_quality,
    pre_estimation,
    specification,
    post_estimation,
    robustness,
    validation
)