#!/usr/bin/env python3
"""Download ACLED event-level data using the ACLED API.

This script downloads detailed conflict event data for Yemen,
including coordinates, event types, and actors.
"""

import os
import sys
import requests
import pandas as pd
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.config.settings import RAW_DATA_DIR, ANALYSIS_CONFIG
from yemen_market.utils.logging import setup_logging, info, warning, error

# Set up logging
setup_logging()

# Load environment variables
load_dotenv()

# ACLED API configuration
ACLED_API_URL = "https://api.acleddata.com/acled/read"
ACLED_KEY = os.getenv("ACLED_API_KEY")
ACLED_EMAIL = os.getenv("ACLED_EMAIL")

if not ACLED_KEY or not ACLED_EMAIL:
    error("ACLED credentials not found in environment variables")
    error("Please create a .env file with ACLED_API_KEY and ACLED_EMAIL")
    sys.exit(1)


def download_acled_events(start_date: str, end_date: str, output_dir: Path) -> pd.DataFrame:
    """Download ACLED event data from API.
    
    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        output_dir: Directory to save the data
        
    Returns:
        DataFrame with ACLED events
    """
    info(f"Downloading ACLED data from {start_date} to {end_date}")
    
    # API parameters
    params = {
        'key': ACLED_KEY,
        'email': ACLED_EMAIL,
        'country': 'Yemen',
        'event_date': f"{start_date}|{end_date}",
        'event_date_where': 'BETWEEN',
        'limit': 0  # Get all records
    }
    
    try:
        # Make API request
        info("Connecting to ACLED API...")
        response = requests.get(ACLED_API_URL, params=params)
        response.raise_for_status()
        
        # Parse JSON response
        data = response.json()
        
        if 'data' in data:
            events = data['data']
            info(f"Downloaded {len(events)} events")
            
            # Convert to DataFrame
            df = pd.DataFrame(events)
            
            # Save to CSV
            output_file = output_dir / f"acled_yemen_events_{start_date}_to_{end_date}.csv"
            df.to_csv(output_file, index=False)
            info(f"Saved to: {output_file}")
            
            return df
        else:
            error("No data found in API response")
            return pd.DataFrame()
            
    except requests.exceptions.RequestException as e:
        error(f"API request failed: {e}")
        return pd.DataFrame()
    except Exception as e:
        error(f"Error processing ACLED data: {e}")
        return pd.DataFrame()


def main():
    """Download ACLED conflict event data for Yemen."""
    info("=" * 60)
    info("ACLED Event Data Download")
    info("=" * 60)
    
    # Get date range from config
    start_date = ANALYSIS_CONFIG.get('start_date', '2019-01-01')
    end_date = ANALYSIS_CONFIG.get('end_date', '2024-12-31')
    
    # Create output directory
    output_dir = RAW_DATA_DIR / "acled"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Download data
    df = download_acled_events(start_date, end_date, output_dir)
    
    if not df.empty:
        info("\nDownload Summary:")
        info(f"Total events: {len(df)}")
        
        # Show date range
        if 'event_date' in df.columns:
            df['event_date'] = pd.to_datetime(df['event_date'])
            info(f"Date range: {df['event_date'].min()} to {df['event_date'].max()}")
        
        # Show event types
        if 'event_type' in df.columns:
            info("\nEvent type distribution:")
            for event_type, count in df['event_type'].value_counts().head(10).items():
                info(f"  {event_type}: {count}")
        
        # Show actors
        if 'actor1' in df.columns:
            info("\nTop actors:")
            for actor, count in df['actor1'].value_counts().head(10).items():
                info(f"  {actor}: {count}")
        
        info("\nNext steps:")
        info("1. Run scripts/process_acled_data.py to process the events")
        info("2. The processor will calculate conflict metrics around markets")
        
        return 0
    else:
        error("Failed to download ACLED data")
        info("\nTroubleshooting:")
        info("1. Check your internet connection")
        info("2. Verify API credentials are correct")
        info("3. Check if ACLED API is accessible")
        return 1


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        info("\nDownload interrupted by user")
        sys.exit(1)
    except Exception as e:
        error(f"Unexpected error: {e}")
        sys.exit(1)