# Enhanced Logging System API Reference

The enhanced logging system provides context-aware logging with structured output, timers, and progress tracking.

## Core Functions

### Basic Logging

```python
from yemen_market.utils.logging import info, debug, warning, error

info("Processing data", records=1000, status="started")
debug("Detailed information for debugging")
warning("Missing values detected", missing=150)
error("Failed to process", error_type="ValueError")
```

### Context Management

#### bind()
Add persistent context to all subsequent log messages.

```python
from yemen_market.utils.logging import bind

# Add module context
bind(module=__name__)

# Add multiple contexts
bind(module="processor", dataset="wfp", version="2.0")
```

#### context()
Temporary context for a specific block.

```python
from yemen_market.utils.logging import context

with context(operation="data_load", file="prices.csv"):
    info("Loading data")
    # All logs in this block include the context
```

### Performance Tracking

#### timer()
Time code execution with automatic logging.

```python
from yemen_market.utils.logging import timer

with timer("process_data"):
    # Your code here
    process_large_dataset()
# Automatically logs: "process_data completed in X.XX seconds"

# With custom log level
with timer("quick_operation", level="DEBUG"):
    quick_task()
```

#### progress()
Show progress for iterative operations.

```python
from yemen_market.utils.logging import progress

# Simple progress
with progress("Processing files", total=100) as update:
    for i, file in enumerate(files):
        process_file(file)
        update(1)  # Update progress by 1

# With description updates
with progress("Processing", total=len(items)) as update:
    for item in items:
        update(1, description=f"Processing {item.name}")
```

### Data Logging

#### log_data_shape()
Log DataFrame shape and basic info.

```python
from yemen_market.utils.logging import log_data_shape

log_data_shape("raw_data", df)
# Logs: "raw_data shape: (1000, 20), memory: 156KB"

# With additional info
log_data_shape("processed", df, 
    missing=df.isnull().sum().sum(),
    dtypes=df.dtypes.value_counts().to_dict()
)
```

#### log_metric()
Log metrics and measurements.

```python
from yemen_market.utils.logging import log_metric

log_metric("accuracy", 0.95)
log_metric("processing_time", 125.3, unit="seconds")
log_metric("memory_usage", 1024, unit="MB")

# Multiple metrics
log_metric("model_performance", {
    "accuracy": 0.95,
    "precision": 0.92,
    "recall": 0.88
})
```

### Resilient Operations

#### resilient_operation()
Decorator for operations that might fail.

```python
from yemen_market.utils.logging import resilient_operation

@resilient_operation(max_retries=3, backoff=2.0)
def download_data(url):
    response = requests.get(url)
    response.raise_for_status()
    return response.content
```

## Configuration

### Setup Logging
Initialize the logging system (usually done once at startup).

```python
from yemen_market.utils.logging import setup_logging

# Basic setup
setup_logging()

# With custom configuration
setup_logging(
    level="DEBUG",
    format="detailed",
    log_file="analysis.log"
)
```

### Log Levels
- **DEBUG**: Detailed information for debugging
- **INFO**: General informational messages
- **WARNING**: Warning messages for potential issues
- **ERROR**: Error messages for failures

## Complete Example

```python
from yemen_market.utils.logging import (
    setup_logging, bind, timer, progress, 
    log_data_shape, info, warning, error
)

# Initialize logging
setup_logging()

# Set module context
bind(module="data_processor")

def process_market_data():
    with timer("full_pipeline"):
        # Load data
        with timer("data_loading"):
            info("Loading market data")
            df = pd.read_csv("markets.csv")
            log_data_shape("raw_data", df)
        
        # Process data
        with timer("data_processing"):
            with progress("Processing markets", total=len(df)) as update:
                results = []
                for idx, row in df.iterrows():
                    try:
                        result = process_market(row)
                        results.append(result)
                    except Exception as e:
                        error("Failed to process market", 
                              market_id=row['id'], 
                              error=str(e))
                    update(1)
            
            processed_df = pd.DataFrame(results)
            log_data_shape("processed_data", processed_df)
        
        # Validate results
        missing = processed_df.isnull().sum().sum()
        if missing > 0:
            warning("Missing values in output", count=missing)
        
        info("Processing complete", 
             records=len(processed_df),
             duration=timer.elapsed)
        
        return processed_df
```

## Best Practices

1. **Use structured logging**: Pass data as keyword arguments
   ```python
   # Good
   info("Processing file", filename="data.csv", size=1024)
   
   # Avoid
   info(f"Processing file data.csv with size 1024")
   ```

2. **Set context early**: Use `bind()` at module/function start
   ```python
   def process_data():
       bind(function="process_data")
       # All subsequent logs include this context
   ```

3. **Use appropriate levels**: 
   - INFO for normal operations
   - WARNING for recoverable issues
   - ERROR for failures
   - DEBUG for detailed troubleshooting

4. **Track performance**: Use timers for operations >1 second
   ```python
   with timer("expensive_operation"):
       result = complex_calculation()
   ```

5. **Show progress**: Use progress bars for loops >10 iterations
   ```python
   with progress("Processing", total=count) as update:
       for item in items:
           process(item)
           update(1)
   ```