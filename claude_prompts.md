# Claude Prompts for Yemen Market Integration

## 1. Context Compression Prompt

Use this with `/compact` command:

```
Keep: Yemen market integration project context focusing on Week 5-6 econometric modeling.  Retain:  - Project path: /Users/<USER>/Documents/GitHub yemen-market-integration/ - Current phase: Week 5-6 dual-track models ready to run - Key accomplishments: 100 completed tasks, production-ready code, no placeholders - Hardware: M3 Pro MacBook with 36GB RAM, acceleration implemented - Models: Track 1 (Bayesian TVP-VECM) and Track 2 (Threshold VECM) complete - Commands: make week5-models, enable_acceleration.py - Data: 14,208 smart panel observations, 44,122 full panel - Critical rules: Enhanced logging only, no temp files, complete all steps - Next actions: Run models on real data, analyze results, policy simulations. 
```

## 2. Run Week 5 Models with Acceleration Prompt

Use this to run the accelerated models:

```
Run the Week 5 econometric models with hardware acceleration on my M3 Pro MacBook:

1. First, ensure joblib is installed: `pip install joblib`
2. Enable acceleration: `python scripts/analysis/enable_acceleration.py`
3. Run the dual-track models: `make week5-models`

The models should use:
- Parallel bootstrap (10 cores) for threshold VECM
- 4 parallel chains for Bayesian MCMC
- Optimized threading for 36GB RAM

Monitor the output for:
- Threshold estimation (~50 events/month)
- Bootstrap p-values
- MCMC convergence (R-hat < 1.01)
- Model comparison results

Expected runtime: 5-10 minutes with acceleration (vs 20-30 without).
Save all outputs to reports/week5_results/.
```

## 3. Quick Status Check Prompt

For checking current status:

```
Check Yemen market integration project status:
- Current location: .claude/ACTIVE_CONTEXT.md
- Progress: reports/progress/README.md (85% complete)
- Week 5-6 models are production-ready
- Hardware acceleration implemented for M3 Pro
- Ready to run: make week5-models
```

## 4. Continue After Break Prompt

To continue work after a break:

```
Continue Yemen market integration project from Week 5-6 modeling phase.
Models are complete and production-ready with M3 Pro acceleration.
Check .claude/ACTIVE_CONTEXT.md for immediate next actions.
Focus on running models and analyzing results.
```

## Notes:
- The project is 85% complete overall
- All placeholder code has been replaced
- Hardware acceleration provides 3-4x speedup
- Models are ready to run on real data
- Next phase: Policy simulations (Week 6)