#!/usr/bin/env python3
"""Quick test of the corrected econometric models."""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.yemen_market.utils.logging import setup_logging, info, success, error, warning
from src.yemen_market.models.track2_simple.threshold_vecm import SimpleThresholdVECM

def main():
    """Quick test of threshold VECM with corrected bootstrap."""
    setup_logging("quick_test")
    
    info("Testing Threshold VECM with corrected bootstrap")
    
    # Create synthetic data for testing
    np.random.seed(42)
    n_obs = 100
    n_markets = 3
    
    # Generate dates
    dates = pd.date_range('2020-01-01', periods=n_obs, freq='M')
    
    # Generate conflict intensity with regime change
    conflict = np.concatenate([
        np.random.normal(30, 10, 50),  # Low conflict
        np.random.normal(70, 15, 50)   # High conflict
    ])
    conflict = np.maximum(conflict, 0)  # No negative conflict
    
    # Generate prices with different dynamics by regime
    prices = []
    for i in range(n_markets):
        # Base price with trend
        base_price = 100 + 0.1 * np.arange(n_obs) + np.random.normal(0, 2, n_obs)
        
        # Add regime-specific dynamics
        for t in range(1, n_obs):
            if conflict[t] <= 50:
                # Low conflict: faster adjustment
                adjustment = -0.15 * (base_price[t-1] - 100)
            else:
                # High conflict: slower adjustment
                adjustment = -0.05 * (base_price[t-1] - 100)
            
            base_price[t] += adjustment + np.random.normal(0, 1)
        
        prices.append(base_price)
    
    # Create panel data
    data_list = []
    for i, market in enumerate(['Market_A', 'Market_B', 'Market_C']):
        for t, date in enumerate(dates):
            data_list.append({
                'date': date,
                'market_name': market,
                'price_usd': prices[i][t],
                'conflict_intensity': conflict[t],
                'control_zone': 'Test'
            })
    
    test_data = pd.DataFrame(data_list)
    
    info(f"Created test data: {test_data.shape}")
    info(f"Conflict range: {conflict.min():.1f} to {conflict.max():.1f}")
    
    # Test the model
    try:
        model = SimpleThresholdVECM(
            threshold=50,
            n_boot=100  # Fewer for quick test
        )
        
        info("\nFitting model with threshold estimation...")
        model.fit(
            data=test_data,
            estimate_threshold=True,
            price_col='price_usd'
        )
        
        results = model.vecm_results
        
        success("\n✅ Model fitted successfully!")
        info(f"Estimated threshold: {results.threshold_value:.1f}")
        info(f"Bootstrap p-value: {results.threshold_p_value:.4f}")
        info(f"Threshold significant: {'Yes' if results.threshold_p_value < 0.05 else 'No'}")
        
        # Check if parameters differ by regime
        if results.low_regime_alpha is not None and results.high_regime_alpha is not None:
            avg_low = np.mean(results.low_regime_alpha)
            avg_high = np.mean(results.high_regime_alpha)
            info(f"\nAdjustment speeds:")
            info(f"  Low conflict: {avg_low:.4f}")
            info(f"  High conflict: {avg_high:.4f}")
            info(f"  Ratio: {abs(avg_high/avg_low):.2f}")
        
        success("\n✅ All tests passed! Models are working correctly.")
        
    except Exception as e:
        error(f"Model test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
