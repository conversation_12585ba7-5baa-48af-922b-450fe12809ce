"""Unit tests for Bayesian Time-Varying Parameter VECM implementation."""

import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

from yemen_market.models.track1_complex.tvp_vecm import BayesianTVPVECM


class TestBayesianTVPVECM:
    """Test suite for BayesianTVPVECM class."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample data for testing."""
        n_periods = 100
        dates = pd.date_range(start='2020-01-01', periods=n_periods, freq='M')
        
        # Create price data for 3 markets
        np.random.seed(42)
        prices = pd.DataFrame({
            'date': dates,
            'market_1': 100 + np.cumsum(np.random.randn(n_periods) * 2),
            'market_2': 98 + np.cumsum(np.random.randn(n_periods) * 2),
            'market_3': 102 + np.cumsum(np.random.randn(n_periods) * 2)
        })
        
        # Create conflict intensity data
        conflict = pd.Series(
            np.abs(np.random.randn(n_periods)) * 10,
            index=dates,
            name='conflict_intensity'
        )
        
        return prices, conflict
    
    @pytest.fixture
    def model(self):
        """Create model instance."""
        return BayesianTVPVECM(
            n_samples=100,  # Small for testing
            n_chains=2,
            random_seed=42
        )
    
    def test_initialization(self):
        """Test model initialization."""
        model = BayesianTVPVECM(
            n_samples=1000,
            n_chains=4,
            random_seed=123
        )
        
        assert model.n_samples == 1000
        assert model.n_chains == 4
        assert model.random_seed == 123
        assert model.trace is None
        assert model.model is None
    
    @patch('yemen_market.models.track1_complex.tvp_vecm.pm')
    def test_build_model(self, mock_pm, model, sample_data):
        """Test PyMC model building."""
        prices, conflict = sample_data
        
        # Prepare data
        price_cols = ['market_1', 'market_2', 'market_3']
        data = model.prepare_data(prices, price_cols)
        
        # Mock PyMC components
        mock_model = MagicMock()
        mock_pm.Model.return_value.__enter__.return_value = mock_model
        
        # Build model
        built_model = model.build_model(data, conflict)
        
        # Verify PyMC model was created
        mock_pm.Model.assert_called_once()
        
        # Verify model components were created
        assert mock_pm.Normal.called
        assert mock_pm.GaussianRandomWalk.called
        assert mock_pm.StudentT.called
    
    @patch('yemen_market.models.track1_complex.tvp_vecm.pm')
    def test_fit(self, mock_pm, model, sample_data):
        """Test model fitting."""
        prices, conflict = sample_data
        price_cols = ['market_1', 'market_2', 'market_3']
        
        # Mock trace
        mock_trace = MagicMock()
        mock_pm.sample.return_value = mock_trace
        
        # Fit model
        model.fit(prices, price_cols, conflict_intensity=conflict)
        
        # Verify sampling was called
        mock_pm.sample.assert_called_once_with(
            draws=model.n_samples,
            chains=model.n_chains,
            random_seed=model.random_seed,
            progressbar=True
        )
        
        assert model.trace is not None
    
    @patch('yemen_market.models.track1_complex.tvp_vecm.pm')
    def test_predict(self, mock_pm, model, sample_data):
        """Test prediction generation."""
        prices, conflict = sample_data
        price_cols = ['market_1', 'market_2', 'market_3']
        
        # Mock trace with posterior samples
        mock_trace = MagicMock()
        mock_trace.posterior = {
            'alpha': np.random.randn(2, 100, 50, 3),  # chains, draws, time, markets
            'beta': np.random.randn(2, 100, 50, 3)
        }
        model.trace = mock_trace
        model.data = model.prepare_data(prices.iloc[:50], price_cols)
        
        # Generate predictions
        predictions = model.predict(n_periods=10)
        
        assert isinstance(predictions, dict)
        assert 'mean' in predictions
        assert 'lower' in predictions
        assert 'upper' in predictions
        assert predictions['mean'].shape == (10, 3)
    
    def test_analyze_conflict_impact(self, model):
        """Test conflict impact analysis."""
        # Mock trace with required parameters
        mock_trace = MagicMock()
        mock_trace.posterior = {
            'gamma': np.random.randn(2, 100, 3),  # Impact of conflict on alpha
            'delta': np.random.randn(2, 100, 3)   # Impact of conflict on beta
        }
        model.trace = mock_trace
        
        # Analyze conflict impact
        impact = model.analyze_conflict_impact()
        
        assert isinstance(impact, dict)
        assert 'adjustment_speed_impact' in impact
        assert 'cointegration_impact' in impact
        assert 'significant_effects' in impact
    
    def test_plot_time_varying_parameters(self, model, tmp_path):
        """Test parameter plotting."""
        # Mock trace
        mock_trace = MagicMock()
        mock_trace.posterior = {
            'alpha': np.random.randn(2, 100, 50, 3),
            'beta': np.random.randn(2, 100, 50, 3)
        }
        model.trace = mock_trace
        model.market_names = ['Market 1', 'Market 2', 'Market 3']
        
        # Create plot
        save_path = tmp_path / "tvp_params.png"
        model.plot_time_varying_parameters(save_path=str(save_path))
        
        # Verify file was created
        assert save_path.exists()
    
    def test_get_posterior_summaries(self, model):
        """Test posterior summary extraction."""
        # Mock trace
        mock_trace = MagicMock()
        mock_trace.posterior = {
            'alpha': np.random.randn(2, 100, 50, 3),
            'beta': np.random.randn(2, 100, 50, 3),
            'gamma': np.random.randn(2, 100, 3),
            'sigma': np.random.randn(2, 100, 3)
        }
        model.trace = mock_trace
        
        # Get summaries
        summaries = model.get_posterior_summaries()
        
        assert isinstance(summaries, dict)
        assert 'alpha_mean' in summaries
        assert 'beta_mean' in summaries
        assert 'gamma_effects' in summaries
        assert summaries['alpha_mean'].shape == (50, 3)
    
    def test_model_without_pymc(self, model, sample_data):
        """Test model behavior when PyMC is not installed."""
        prices, conflict = sample_data
        price_cols = ['market_1', 'market_2', 'market_3']
        
        # Mock import error
        with patch('yemen_market.models.track1_complex.tvp_vecm.pm', None):
            with pytest.raises(ImportError, match="PyMC"):
                model.fit(prices, price_cols, conflict_intensity=conflict)
    
    def test_invalid_conflict_data(self, model, sample_data):
        """Test handling of invalid conflict data."""
        prices, _ = sample_data
        price_cols = ['market_1', 'market_2', 'market_3']
        
        # Wrong length conflict data
        conflict = pd.Series([1, 2, 3])
        
        with pytest.raises(ValueError):
            model.fit(prices, price_cols, conflict_intensity=conflict)