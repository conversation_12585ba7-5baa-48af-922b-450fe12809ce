# CLAUDE.md - Project Context and Development Guide

## 🚨 CRITICAL DEVELOPMENT RULES 🚨

### 1. Keep the Codebase Clean
- **NEVER** create temporary test files that won't be reused
- **NEVER** leave exploratory code in production directories
- **ALWAYS** delete failed attempts and redundant scripts immediately
- **ALWAYS** ask "Will this file be used again?" before creating it
- If creating a temporary file for testing, use a clear prefix like `temp_` and delete it after use

### 2. Complete All Steps (No Shortcuts)
- **NEVER** skip steps because they seem intensive or complicated
- **ALWAYS** implement the full solution, not a simplified version
- If a task requires 100 operations, do all 100 - don't sample 10
- If processing takes time, use progress bars and logging to show status
- Complex tasks deserve more code, not less

### 3. Use Enhanced Logging Throughout
- **ALWAYS** use the enhanced logging system, not basic print() or logging
- **ALWAYS** include timers for operations that might take >1 second
- **ALWAYS** use progress bars for loops with >10 iterations
- **ALWAYS** log data shapes after transformations
- **NEVER** use old-style logging except in backward compatibility layer

Example of proper implementation:
```python
from yemen_market.utils.logging import (
    info, timer, progress, log_data_shape, bind
)

# Set context for this module
bind(module=__name__)

# Time the entire operation
with timer("process_all_markets"):
    # Show progress for long operations
    with progress("Processing markets", total=len(markets)) as update:
        for market in markets:
            # Don't skip any markets!
            result = process_market(market)
            log_data_shape(f"market_{market}", result)
            update(1)
```


## Recent Major Improvements

### Pcode-Based Data Integration (2025-05-27)
- Implemented governorate name standardization using Yemen pcodes
- Created EnhancedWFPProcessor with smart panel generation
- Improved price coverage from 62% to 88.4%
- Reduced dataset from 44k to 14k observations (focused on real market data)

### Key Data Pipeline Enhancements
- **Smart Panels**: Only include commodity-market pairs that actually exist
- **Pcode Matching**: Standardized names (e.g., "Al Dhale'e" → "Ad Dale'")
- **Conflict Integration**: 2,016 market-month observations with intensity metrics
- **Documentation**: Comprehensive data docs in `docs/data/` folder

## Enhanced Logging System [USE THIS EVERYWHERE]
All modules MUST use the enhanced logging system:

**Basic Operations:**
```python
from yemen_market.utils.logging import (
    info, debug, warning, error,  # Basic logging
    timer, progress,              # Performance tracking
    log_data_shape, log_metric,   # Data logging
    bind, context                 # Context management
)

# ALWAYS bind module context at start
bind(module=__name__)
```

**Required Patterns:**
```python
# 1. Time operations that might be slow
with timer("operation_name"):
    result = expensive_operation()

# 2. Show progress for loops
with progress("Processing items", total=len(items)) as update:
    for item in items:
        process(item)
        update(1)  # Don't skip iterations!

# 3. Log data transformations
df = transform_data(df)
log_data_shape("transformed", df)

# 4. Track metrics
log_metric("accuracy", 0.95)
log_metric("processing_time", elapsed)
```

**NEVER DO THIS:**
```python
# DON'T use print
print("Processing...")  # NO!

# DON'T use basic logging
import logging
logging.info("Message")  # NO!

# DON'T skip progress tracking
for item in items[:10]:  # NO! Process all items
    process(item)
```


## Code Style Guidelines
- NumPy-style docstrings for all public functions
- Type hints for all parameters and returns
- Black formatting (88 char limit)
- Comprehensive unit tests (>90% coverage)
- **Enhanced logging for ALL operations** (not print statements)
- Conventional commits (feat:, fix:, docs:, test:, chore:)
- **Clean commits**: No temporary files in git history

## Common Commands
```bash
make install      # Set up environment
make download-data # Fetch latest data (to implement)
make analysis     # Run full pipeline (to implement)
make report       # Generate outputs (to implement)
make test         # Run test suite
make lint         # Check code style
make format       # Format with black
```

## Documentation and Progress Tracking Rules

### Documentation Hierarchy (NEVER VIOLATE)
1. **Progress Tracking**: ONLY in `reports/progress/README.md`
   - All percentages, timelines, completed tasks
   - Milestone tracking and sprint details
   - NO progress info in any other file

2. **Active Development**: ONLY in `.claude/ACTIVE_CONTEXT.md`
   - Current sprint focus
   - Immediate next actions (max 5)
   - "To Resume" instructions
   - Recent accomplishments (max 3)

3. **Development Rules**: ONLY in `CLAUDE.md`
   - Critical development patterns
   - Logging requirements
   - Code style guidelines
   - NO project status or progress

4. **Public Documentation**: `README.md`
   - Installation and usage only
   - NO progress badges or timelines
   - NO "recent updates" sections
   - Link to progress dashboard if needed

### Progress Update Protocol
When updating progress:
1. **FIRST**: Update `reports/progress/README.md`
2. **SECOND**: Update `.claude/ACTIVE_CONTEXT.md` with current focus only
3. **NEVER**: Add progress info to README.md, CLAUDE.md, or create new status files
4. **NEVER**: Create PROJECT_STATUS.md or similar redundant files

### File Creation Rules
Before creating ANY documentation file:
1. Check if content belongs in existing files
2. Verify it doesn't duplicate progress tracking
3. Ensure clear, unique purpose
4. If unsure, add to existing file first

## MCP Servers Configuration
For optimal Claude Code performance, enable:
- **filesystem** (default) - Code development
- **git** - Version control
- **github** or **mcp-github-project-manager** - Issue tracking
- **fetch** - API data collection

## Recent Updates (2025-11-20)

### ✅ Code Consolidation Complete
- Merged `EnhancedWFPProcessor` into main `WFPProcessor`
- Deleted all duplicate/enhanced files
- Consolidated data files (removed *_enhanced.parquet)
- Single source of truth for all components

### ✅ Documentation 100% Coverage
- Created comprehensive `docs/` structure:
  - API documentation for all modules
  - Architecture overview
  - Deployment guide
  - Getting started tutorial
- All modules fully documented

### ✅ Scripts Organization
- Reorganized scripts into logical subfolders:
  - `data_collection/`: Download scripts
  - `data_processing/`: Processing scripts  
  - `analysis/`: Analysis scripts
  - `utilities/`: Test, export, gallery scripts
- Updated all import paths

### ✅ Week 3-4 Sprint: 100% Complete
- All major tasks completed
- Minor utility scripts added
- Ready for Week 5-6 econometric modeling

### ✅ Week 5 Implementation (2025-05-28)
- **Critical Econometric Corrections Applied**:
  - Fixed Hansen (1999) bootstrap procedure for threshold testing
  - Added practical constraints (20 < threshold < 200 events)
  - Implemented Gregory-Hansen test for structural breaks
  - Simplified Bayesian VECM to discrete regimes
- **Dual-Track Models Ready**:
  - Track 1: Bayesian regime-switching VECM
  - Track 2: Simple threshold VECM with corrected bootstrap
  - Model comparison framework implemented
- **Production Code Quality (2025-05-28)**:
  - Fixed duplicate test_gregory_hansen_cointegration function
  - Refactored _calculate_regime_agreement with helper methods
  - Replaced all placeholder SE calculations with pooled variance
  - Enhanced SSR calculation to include lagged differences
  - Fixed bare except blocks throughout codebase
  - Improved information criteria calculations
  - Fixed visualization imports (plot_acf, plot_pacf)
  - Added parallel bootstrap with joblib
- **Ready to Run**: `make week5-models`
- **Hardware Acceleration**: Optimized for Apple Silicon M3 Pro
  - Parallel bootstrap with joblib (5-10x speedup)
  - Multi-chain MCMC sampling (3-4x speedup)
  - Optimized for 10 CPU cores and 36GB RAM

## Using Claude Code with This Project

### Quick Start
```bash
# 1. Navigate to project
cd /Users/<USER>/Documents/GitHub/yemen-market-integration

# 2. Start Claude Code
claude

# 3. Use prepared prompts
Use: .claude/prompts/week5_testing_prompt.md
```

### Available Prompts
- `.claude/prompts/initial_project_prompt.md` - Original requirements
- `.claude/prompts/eda_phase_prompt.md` - EDA implementation
- `.claude/prompts/week5_testing_prompt.md` - Model testing guide ⭐

### Key Commands for Week 5
```bash
make check-deps    # Verify all packages installed
make quick-test    # Test with synthetic data
make week5-models  # Run full dual-track analysis
```

### Expected Results
- Threshold: ~50 events/month (statistically significant)
- Market integration: 3-4x slower in high conflict
- Model agreement: >80% correlation between tracks

