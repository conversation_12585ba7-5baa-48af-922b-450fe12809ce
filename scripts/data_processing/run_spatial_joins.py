#!/usr/bin/env python3
"""Script to perform spatial joins between WFP markets and ACAPS control zones.

This script:
1. <PERSON><PERSON> processed WFP market data with coordinates
2. Loads ACAPS control zone boundaries
3. Maps markets to control zones over time
4. Identifies boundary markets
5. Saves mapping for further analysis
"""

import logging
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.data.spatial_joins import SpatialJoiner
from yemen_market.config.settings import PROCESSED_DATA_DIR
from yemen_market.utils.logging import setup_logging, info, warning, error

# Set up logging
setup_logging()


def run_spatial_joins():
    """Execute spatial join workflow."""
    info("=" * 60)
    info("Starting Spatial Join Process")
    info("=" * 60)
    
    # Initialize spatial joiner
    joiner = SpatialJoiner(buffer_distance_km=10.0)
    
    try:
        # Step 1: Load market coordinates
        info("\nStep 1: Loading WFP market coordinates")
        # Use processed WFP panel data which now has coordinates
        wfp_panel_path = PROCESSED_DATA_DIR / "wfp_market_panel.parquet"
        markets = joiner.load_market_coordinates(wfp_panel_path)
        info(f"Loaded {len(markets)} markets with coordinates")
        
        # Display sample of markets
        info("\nSample markets:")
        for _, market in markets.head(3).iterrows():
            info(f"  {market['market_name']} ({market['governorate']}) - "
                       f"Lat: {market['lat']:.4f}, Lon: {market['lon']:.4f}")
        
        # Step 2: Load control zones
        info("\nStep 2: Loading ACAPS control zones")
        try:
            control_zones = joiner.load_control_zones()
            if control_zones.empty:
                warning("No geometry in processed control data - need shapefile data")
                info("Please ensure ACAPS shapefiles are downloaded and processed")
                return
        except Exception as e:
            error(f"Failed to load control zones: {e}")
            info("Control zone boundaries required for spatial mapping")
            return
        
        info(f"Loaded control zones for {len(control_zones)} records")
        
        # Display control zone distribution
        if 'control_zone' in control_zones.columns:
            zone_dist = control_zones['control_zone'].value_counts()
            info("\nControl zone distribution:")
            for zone, count in zone_dist.items():
                info(f"  {zone}: {count} districts")
        
        # Step 3: Perform spatial join for current data
        info("\nStep 3: Mapping markets to control zones")
        market_zones = joiner.perform_spatial_join(markets, control_zones)
        
        # Display mapping results
        if 'control_zone' in market_zones.columns:
            zone_counts = market_zones['control_zone'].value_counts()
            info("\nMarkets per control zone:")
            for zone, count in zone_counts.items():
                info(f"  {zone}: {count} markets")
            
            # Show unmatched markets if any
            if 'distance_to_zone_km' in market_zones.columns:
                far_markets = market_zones[market_zones['distance_to_zone_km'] > 50]
                if not far_markets.empty:
                    warning(f"\n{len(far_markets)} markets are >50km from their assigned zone")
        
        # Step 4: Identify boundary markets
        info("\nStep 4: Identifying boundary markets")
        boundary_markets = joiner.identify_boundary_markets(market_zones, control_zones)
        
        if not boundary_markets.empty:
            info(f"Found {len(boundary_markets)} boundary markets")
            info("\nBoundary markets:")
            for _, market in boundary_markets.head(5).iterrows():
                info(f"  {market['market_name']}: {market['primary_zone']} "
                           f"(near: {market['nearby_zones']})")
        else:
            info("No boundary markets identified")
        
        # Step 5: Calculate zone distances
        info("\nStep 5: Calculating market-to-zone distances")
        distance_matrix = joiner.calculate_zone_distances(markets, control_zones)
        
        if not distance_matrix.empty:
            info(f"Calculated distances for {len(distance_matrix)} markets")
            
            # Show average distances
            avg_distances = distance_matrix.mean()
            info("\nAverage distance to each zone type:")
            for zone, avg_dist in avg_distances.items():
                info(f"  {zone}: {avg_dist:.1f} km")
        
        # Step 6: Create temporal mapping
        info("\nStep 6: Creating temporal market-zone mapping")
        temporal_mapping = joiner.create_temporal_mapping(markets, control_zones)
        
        if not temporal_mapping.empty:
            info(f"Created temporal mapping with {len(temporal_mapping)} records")
            
            # Check for zone changes
            if 'zone_changed' in temporal_mapping.columns:
                n_changes = temporal_mapping['zone_changed'].sum()
                markets_changed = temporal_mapping[temporal_mapping['zone_changed']]['market_id'].nunique()
                info(f"\nZone changes detected:")
                info(f"  Total changes: {n_changes}")
                info(f"  Markets affected: {markets_changed}")
        
        # Step 7: Save all outputs
        info("\nStep 7: Saving spatial join outputs")
        saved_files = joiner.save_mappings(
            market_zones,
            temporal_mapping,
            boundary_markets,
            distance_matrix
        )
        
        info("\nSaved files:")
        for file_type, path in saved_files.items():
            info(f"  {file_type}: {path.name}")
        
        # Step 8: Generate summary report
        info("\nStep 8: Generating summary statistics")
        summary = joiner.generate_summary_report(market_zones, temporal_mapping)
        
        if not summary.empty:
            info("\nSummary statistics:")
            for _, stat in summary.iterrows():
                info(f"  {stat['metric']}: {stat['value']}")
        
        info("\n" + "=" * 60)
        info("Spatial join process completed successfully!")
        info("=" * 60)
        
    except Exception as e:
        error(f"Error in spatial join process: {e}")
        sys.exit(1)


def check_prerequisites():
    """Check if required data files exist."""
    info("Checking prerequisites...")
    
    # Check for WFP data
    wfp_files = list(PROCESSED_DATA_DIR.glob("wfp_market_panel.parquet"))
    
    if not wfp_files:
        error("No WFP price data found. Run process_wfp_data.py first.")
        return False
    
    # Check for ACAPS data
    acaps_dir = PROCESSED_DATA_DIR / "control_zones"
    acaps_files = list(acaps_dir.glob("*control_zones*"))
    
    if not acaps_files:
        error("No ACAPS control zone data found. Run process_acaps_data.py first.")
        return False
    
    info("Prerequisites satisfied")
    return True


def main():
    """Main execution function."""
    info("Yemen Market Integration - Spatial Join Tool")
    
    # Check prerequisites
    if not check_prerequisites():
        error("Missing required data files. Please run data processing scripts first.")
        sys.exit(1)
    
    # Run spatial joins
    run_spatial_joins()
    
    info("\nNext steps:")
    info("1. Review boundary markets for special handling in models")
    info("2. Use temporal mapping to analyze market integration dynamics")
    info("3. Incorporate spatial weights in econometric models")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        info("\nProcess interrupted by user")
        sys.exit(0)
    except Exception as e:
        error(f"Unexpected error: {e}")
        sys.exit(1)