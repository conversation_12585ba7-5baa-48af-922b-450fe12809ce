# FeatureEngineer API Reference

The `FeatureEngineer` class creates derived features for econometric analysis including temporal, spatial, interaction, and threshold features.

## Class: FeatureEngineer

```python
from yemen_market.features import FeatureEngineer

engineer = FeatureEngineer(
    temporal_lags=[1, 2, 3],
    rolling_windows=[3, 6],
    spatial_neighbors=5
)
```

### Parameters

- **temporal_lags** (list[int]): Lag periods for temporal features (default: [1, 2, 3])
- **rolling_windows** (list[int]): Window sizes for rolling statistics (default: [3, 6])
- **spatial_neighbors** (int): Number of spatial neighbors to consider (default: 5)

### Methods

#### fit_transform()
Create all features for the input dataset.

```python
features_df = engineer.fit_transform(df)
```

**Parameters:**
- **df** (DataFrame): Input panel data

**Returns:**
- **features_df** (DataFrame): Data with all engineered features

#### create_all_features()
Alias for fit_transform().

```python
features_df = engineer.create_all_features(df)
```

### Feature Functions

#### create_temporal_features()
Create time-based features.

```python
from yemen_market.features import create_temporal_features

df_temporal = create_temporal_features(
    df,
    lags=[1, 2, 3],
    windows=[3, 6]
)
```

**Features created:**
- **Lags**: `{variable}_lag{n}` for each numeric variable
- **Differences**: `{variable}_diff`, `{variable}_pct_change`
- **Rolling stats**: `{variable}_ma{n}`, `{variable}_std{n}`, `{variable}_cv{n}`
- **Time trends**: `time_trend`, `time_trend_squared`

#### create_interaction_features()
Create interaction terms between variables.

```python
from yemen_market.features import create_interaction_features

df_interact = create_interaction_features(df)
```

**Features created:**
- **Price × Conflict**: `price_x_conflict`, `price_x_high_conflict`
- **Zone × Time**: `zone_{zone}_x_trend` for each control zone
- **Exchange × Zone**: `exchange_rate_x_{zone}` for each zone
- **Conflict × Zone**: `conflict_x_{zone}` for each zone

#### create_threshold_indicators()
Create binary threshold indicators.

```python
from yemen_market.features import create_threshold_indicators

df_threshold = create_threshold_indicators(df)
```

**Features created:**
- **Conflict regimes**: `conflict_regime_low`, `conflict_regime_medium`, `conflict_regime_high`
- **Conflict threshold**: `high_conflict_threshold` (>50 events)
- **Exchange rate**: `rate_diff_threshold_10`, `rate_diff_threshold_20`
- **Volatility**: `high_volatility` (>75th percentile)
- **Zone indicator**: `is_contested`

#### create_spatial_features()
Create spatial relationship features.

```python
from yemen_market.features import create_spatial_features

df_spatial = create_spatial_features(df, n_neighbors=5)
```

**Features created:**
- **Spatial lags**: `{variable}_spatial_lag` for price and conflict
- **Spatial differences**: `{variable}_spatial_diff`
- **Distance features**: `distance_to_other_zone`

**Requirements:**
- Requires `lat` and `lon` columns
- Needs at least 2 unique markets

#### create_conflict_features()
Create conflict-specific features.

```python
from yemen_market.features import create_conflict_features

df_conflict = create_conflict_features(df)
```

**Features created:**
- **Quartiles**: `conflict_quartile` (1-4)
- **Event ratios**: `n_battles_ratio`, `n_explosions_ratio`, etc.
- **Persistence**: `conflict_persistent` (high conflict for 3+ periods)
- **Shocks**: `conflict_shock` (large increase from previous period)
- **Actor dominance**: `dominant_actor` (actor with most events)

### Example Usage

```python
from yemen_market.features import FeatureEngineer
import pandas as pd

# Load panel data
panel = pd.read_parquet('data/processed/panels/integrated_panel.parquet')

# Initialize feature engineer
engineer = FeatureEngineer(
    temporal_lags=[1, 2, 3, 6],    # Include 6-period lag
    rolling_windows=[3, 6, 12],     # Add 12-period window
    spatial_neighbors=10            # More neighbors
)

# Create all features
features = engineer.fit_transform(panel)

# Check new features
new_cols = [col for col in features.columns if col not in panel.columns]
print(f"Created {len(new_cols)} new features")

# Feature groups
temporal = [col for col in new_cols if 'lag' in col or 'ma' in col]
interaction = [col for col in new_cols if '_x_' in col]
threshold = [col for col in new_cols if 'threshold' in col or 'regime' in col]

print(f"Temporal features: {len(temporal)}")
print(f"Interaction features: {len(interaction)}")
print(f"Threshold features: {len(threshold)}")
```

### Feature Categories

1. **Temporal Features** (30-40 features)
   - Price lags and differences
   - Moving averages and volatility
   - Time trends

2. **Interaction Features** (20-30 features)
   - Price-conflict interactions
   - Zone-specific effects
   - Time-varying relationships

3. **Threshold Features** (10-15 features)
   - Regime indicators
   - Binary thresholds
   - Structural break indicators

4. **Spatial Features** (10-20 features)
   - Spatial lags
   - Distance-based features
   - Neighbor effects

5. **Conflict Features** (15-20 features)
   - Event type breakdowns
   - Temporal patterns
   - Actor analysis

### Best Practices

- Create features after data cleaning
- Check for multicollinearity
- Consider feature selection for high-dimensional data
- Validate features make economic sense
- Document feature definitions