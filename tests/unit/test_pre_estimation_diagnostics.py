"""Unit tests for pre-estimation diagnostic tools."""

import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch
from datetime import datetime

from yemen_market.diagnostics.pre_estimation import PreEstimationDiagnostics


class TestPreEstimationDiagnostics:
    """Test suite for PreEstimationDiagnostics class."""
    
    @pytest.fixture
    def sample_prices(self):
        """Create sample price data for testing."""
        n = 100
        dates = pd.date_range(start='2020-01-01', periods=n, freq='M')
        
        # Create prices with unit root (non-stationary)
        np.random.seed(42)
        trend = np.arange(n) * 0.5
        
        prices = pd.DataFrame({
            'date': dates,
            'market_1': 100 + trend + np.cumsum(np.random.randn(n)),
            'market_2': 98 + trend * 0.9 + np.cumsum(np.random.randn(n)),
            'market_3': 102 + trend * 1.1 + np.cumsum(np.random.randn(n))
        })
        
        return prices
    
    @pytest.fixture
    def diagnostics(self, sample_prices):
        """Create PreEstimationDiagnostics instance."""
        return PreEstimationDiagnostics(
            data=sample_prices,
            price_cols=['market_1', 'market_2', 'market_3']
        )
    
    def test_initialization(self, sample_prices):
        """Test initialization."""
        price_cols = ['market_1', 'market_2', 'market_3']
        diag = PreEstimationDiagnostics(
            data=sample_prices,
            price_cols=price_cols
        )
        
        assert diag.data.equals(sample_prices)
        assert diag.price_cols == price_cols
        assert diag.results == {}
    
    def test_test_unit_roots(self, diagnostics):
        """Test unit root testing."""
        results = diagnostics.test_unit_roots(
            test_types=['adf', 'kpss', 'pp']
        )
        
        assert isinstance(results, dict)
        
        for market in diagnostics.price_cols:
            assert market in results
            assert 'adf' in results[market]
            assert 'kpss' in results[market]
            assert 'pp' in results[market]
            
            # Check ADF results structure
            adf_result = results[market]['adf']
            assert 'statistic' in adf_result
            assert 'p_value' in adf_result
            assert 'critical_values' in adf_result
            assert 'is_stationary' in adf_result
    
    def test_test_cointegration_rank(self, diagnostics):
        """Test cointegration rank testing."""
        results = diagnostics.test_cointegration_rank(
            det_order=0,
            k_ar_diff=1
        )
        
        assert isinstance(results, dict)
        assert 'trace_test' in results
        assert 'max_eig_test' in results
        assert 'selected_rank' in results
        assert 'critical_values' in results
        
        # Check rank is reasonable
        assert 0 <= results['selected_rank'] <= len(diagnostics.price_cols) - 1
    
    def test_check_structural_breaks(self, diagnostics):
        """Test structural break detection."""
        results = diagnostics.check_structural_breaks(
            test_type='sup_f',
            trim=0.15
        )
        
        assert isinstance(results, dict)
        
        for market in diagnostics.price_cols:
            assert market in results
            market_results = results[market]
            
            assert 'break_dates' in market_results
            assert 'test_statistic' in market_results
            assert 'p_value' in market_results
            
            # Break dates should be within data range
            if market_results['break_dates']:
                for break_date in market_results['break_dates']:
                    assert diagnostics.data['date'].min() <= break_date <= diagnostics.data['date'].max()
    
    def test_test_granger_causality(self, diagnostics):
        """Test Granger causality testing."""
        results = diagnostics.test_granger_causality(max_lag=4)
        
        assert isinstance(results, dict)
        
        # Check all pairwise tests
        markets = diagnostics.price_cols
        for i, market1 in enumerate(markets):
            for j, market2 in enumerate(markets):
                if i != j:
                    pair = f"{market1}->{market2}"
                    assert pair in results
                    
                    pair_result = results[pair]
                    assert 'test_statistic' in pair_result
                    assert 'p_value' in pair_result
                    assert 'optimal_lag' in pair_result
    
    def test_calculate_information_criteria(self, diagnostics):
        """Test information criteria calculation."""
        results = diagnostics.calculate_information_criteria(
            max_lag=10,
            criteria=['aic', 'bic', 'hqic']
        )
        
        assert isinstance(results, dict)
        assert 'aic' in results
        assert 'bic' in results
        assert 'hqic' in results
        assert 'optimal_lag' in results
        
        # Check lag selection
        assert 1 <= results['optimal_lag'] <= 10
        
        # Check criteria values are dictionaries with lags
        for criterion in ['aic', 'bic', 'hqic']:
            assert isinstance(results[criterion], dict)
            assert len(results[criterion]) == 10
    
    def test_test_weak_exogeneity(self, diagnostics):
        """Test weak exogeneity testing."""
        # Need to set cointegration results first
        diagnostics.results['cointegration'] = {
            'beta': np.array([[1, -0.9, -0.1]]),
            'alpha': np.array([[-0.1], [0.05], [-0.02]])
        }
        
        results = diagnostics.test_weak_exogeneity()
        
        assert isinstance(results, dict)
        
        for market in diagnostics.price_cols:
            assert market in results
            market_result = results[market]
            
            assert 'test_statistic' in market_result
            assert 'p_value' in market_result
            assert 'is_weakly_exogenous' in market_result
    
    def test_run_all_diagnostics(self, diagnostics):
        """Test running all pre-estimation diagnostics."""
        results = diagnostics.run_all_diagnostics()
        
        assert isinstance(results, dict)
        
        expected_tests = [
            'unit_roots',
            'cointegration_rank',
            'structural_breaks',
            'granger_causality',
            'information_criteria'
        ]
        
        for test in expected_tests:
            assert test in results
    
    def test_generate_summary_report(self, diagnostics, tmp_path):
        """Test summary report generation."""
        # Run diagnostics first
        diagnostics.run_all_diagnostics()
        
        # Generate report
        report_path = tmp_path / "pre_estimation_report.txt"
        report = diagnostics.generate_summary_report(output_path=str(report_path))
        
        assert isinstance(report, str)
        assert "Pre-Estimation Diagnostic Report" in report
        assert report_path.exists()
        
        # Check key sections
        assert "Unit Root Tests" in report
        assert "Cointegration Rank" in report
        assert "Structural Breaks" in report
    
    def test_plot_diagnostics(self, diagnostics, tmp_path):
        """Test diagnostic plotting."""
        # Run diagnostics first
        diagnostics.run_all_diagnostics()
        
        # Create plots
        save_dir = tmp_path / "pre_estimation_plots"
        diagnostics.plot_diagnostics(save_dir=str(save_dir))
        
        assert save_dir.exists()
        
        # Check for expected plots
        expected_plots = [
            'price_series.png',
            'first_differences.png',
            'rolling_correlation.png'
        ]
        
        for plot in expected_plots:
            assert (save_dir / plot).exists()
    
    def test_insufficient_data(self):
        """Test handling of insufficient data."""
        # Create very short data
        short_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=10, freq='M'),
            'market_1': np.random.randn(10),
            'market_2': np.random.randn(10)
        })
        
        diag = PreEstimationDiagnostics(
            data=short_data,
            price_cols=['market_1', 'market_2']
        )
        
        with pytest.raises(ValueError, match="Insufficient data"):
            diag.test_unit_roots()