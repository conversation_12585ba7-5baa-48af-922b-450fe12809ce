#!/usr/bin/env python3
"""Test script to verify HDX client functionality."""

import logging
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from yemen_market.data.hdx_client import HDXClient
from yemen_market.utils import setup_logging

# Set up logging
setup_logging()
logger = logging.getLogger(__name__)


def main():
    """Test HDX client basic functionality."""
    logger.info("=" * 60)
    logger.info("Testing HDX Client Functionality")
    logger.info("=" * 60)
    
    # Initialize client
    logger.info("\nInitializing HDX client...")
    client = HDXClient()
    
    # Test 1: Search for Yemen datasets
    logger.info("\n1. Testing dataset search...")
    try:
        datasets = client.search_datasets("Yemen food")
        logger.info(f"Found {len(datasets)} datasets matching 'Yemen food'")
        
        # Show first few results
        for i, dataset in enumerate(datasets[:3]):
            logger.info(f"\n  Dataset {i+1}:")
            logger.info(f"    ID: {dataset.get('name')}")
            logger.info(f"    Title: {dataset.get('title')}")
            logger.info(f"    Org: {dataset.get('organization', {}).get('title')}")
    except Exception as e:
        logger.error(f"Search failed: {e}")
    
    # Test 2: Get specific dataset info
    logger.info("\n2. Testing dataset info retrieval...")
    dataset_id = "wfp-food-prices-for-yemen"
    try:
        resources = client.get_dataset_resources(dataset_id)
        if resources:
            logger.info(f"Dataset '{dataset_id}' has {len(resources)} resources:")
            for res in resources[:3]:
                logger.info(f"  - {res.get('name')} ({res.get('format')})")
        else:
            logger.info("No resources found")
    except Exception as e:
        logger.error(f"Failed to get dataset info: {e}")
    
    # Test 3: Check cache
    logger.info("\n3. Testing cache functionality...")
    client.check_cache_status()
    
    # Test 4: Download a small dataset (test mode)
    logger.info("\n4. Testing download functionality...")
    logger.info("(Skipping actual download in test mode)")
    logger.info("To download data, run: python scripts/download_data.py")
    
    logger.info("\n" + "=" * 60)
    logger.info("HDX Client test completed!")
    logger.info("=" * 60)
    
    logger.info("\nNext steps:")
    logger.info("1. Set HDX_API_KEY in .env file for authentication")
    logger.info("2. Run scripts/download_data.py to download all datasets")
    logger.info("3. Process data with the pipeline scripts")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())