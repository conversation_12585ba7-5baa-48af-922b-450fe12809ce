"""Pre-estimation diagnostic tests.

This module implements tests that should be run before model estimation,
including unit root tests, cointegration tests, and lag order selection.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Union
import statsmodels.api as sm
from statsmodels.tsa.stattools import adfuller, kpss
from statsmodels.tsa.vector_ar.var_model import VAR
from statsmodels.tsa.vector_ar.vecm import coint_johansen
from statsmodels.stats.diagnostic import acorr_ljungbox
import warnings

from ...utils.logging import bind, timer, info, warning, error, log_metric, progress
from ..test_battery import DiagnosticResult


def test_unit_roots_battery(data: Dict[str, Any], 
                           tests: List[str] = ['adf', 'kpss']) -> DiagnosticResult:
    """Run battery of unit root tests.
    
    Args:
        data: Prepared data dictionary from model
        tests: List of tests to run ('adf', 'kpss', 'pp', 'dfgls')
        
    Returns:
        DiagnosticResult with summary of unit root tests
    """
    with timer("unit_root_tests"):
        info("Running unit root test battery")
        
        if 'log_prices' not in data:
            return DiagnosticResult(
                test_name="Unit Root Battery",
                category="pre_estimation",
                statistic=np.nan,
                p_value=None,
                critical_value=None,
                passed=False,
                interpretation="No price data available for testing",
                details={}
            )
        
        log_prices = data['log_prices']
        markets = data.get('markets', log_prices.columns)
        
        results_by_test = {}
        overall_stationary = []
        
        # Run tests for each series
        for test_name in tests:
            if test_name == 'adf':
                results_by_test['ADF'] = _run_adf_tests(log_prices, markets)
            elif test_name == 'kpss':
                results_by_test['KPSS'] = _run_kpss_tests(log_prices, markets)
        
        # Aggregate results
        details = {}
        for test_name, test_results in results_by_test.items():
            stationary_count = sum(1 for r in test_results.values() if r['stationary'])
            details[f'{test_name}_stationary'] = stationary_count
            details[f'{test_name}_nonstationary'] = len(test_results) - stationary_count
            
            # List non-stationary series
            nonstat = [mkt for mkt, r in test_results.items() if not r['stationary']]
            if nonstat:
                details[f'{test_name}_nonstationary_markets'] = nonstat[:5]  # First 5
        
        # Overall assessment (majority vote)
        adf_stationary = results_by_test.get('ADF', {})
        kpss_stationary = results_by_test.get('KPSS', {})
        
        for market in markets:
            votes = 0
            if market in adf_stationary and adf_stationary[market]['stationary']:
                votes += 1
            if market in kpss_stationary and kpss_stationary[market]['stationary']:
                votes += 1  # KPSS null is stationarity
            
            if votes >= len(tests) / 2:
                overall_stationary.append(market)
        
        # Create result
        n_stationary = len(overall_stationary)
        n_total = len(markets)
        passed = n_stationary == 0  # For I(1) processes, we expect non-stationarity
        
        interpretation = f"{n_total - n_stationary}/{n_total} series appear to be I(1) (non-stationary)"
        if passed:
            interpretation += " - appropriate for VECM modeling"
        else:
            interpretation += f" - {n_stationary} series may be I(0), check specification"
        
        return DiagnosticResult(
            test_name="Unit Root Battery",
            category="pre_estimation",
            statistic=float(n_total - n_stationary) / n_total,  # Proportion I(1)
            p_value=None,
            critical_value=0.8,  # Expect at least 80% I(1)
            passed=passed,
            interpretation=interpretation,
            details=details
        )


def _run_adf_tests(log_prices: pd.DataFrame, 
                  markets: List[str]) -> Dict[str, Dict[str, Any]]:
    """Run ADF tests on each series."""
    results = {}
    
    for market in markets:
        if market not in log_prices.columns:
            continue
            
        series = log_prices[market].dropna()
        if len(series) < 20:
            continue
        
        try:
            # ADF test with constant and trend
            adf_stat, p_value, used_lag, nobs, critical_values, icbest = adfuller(
                series, regression='ct', autolag='AIC'
            )
            
            results[market] = {
                'statistic': adf_stat,
                'p_value': p_value,
                'critical_5%': critical_values['5%'],
                'stationary': p_value < 0.05,
                'lags_used': used_lag
            }
        except Exception as e:
            warning(f"ADF test failed for {market}: {e}")
    
    return results


def _run_kpss_tests(log_prices: pd.DataFrame,
                   markets: List[str]) -> Dict[str, Dict[str, Any]]:
    """Run KPSS tests on each series."""
    results = {}
    
    for market in markets:
        if market not in log_prices.columns:
            continue
            
        series = log_prices[market].dropna()
        if len(series) < 20:
            continue
        
        try:
            # KPSS test with trend
            kpss_stat, p_value, lags, crit = kpss(series, regression='ct')
            
            results[market] = {
                'statistic': kpss_stat,
                'p_value': p_value,
                'critical_5%': crit['5%'],
                'stationary': p_value > 0.05,  # KPSS null is stationarity
                'lags_used': lags
            }
        except Exception as e:
            warning(f"KPSS test failed for {market}: {e}")
    
    return results




def test_cointegration(data: Dict[str, Any],
                      det_order: int = 0,
                      k_ar_diff: Optional[int] = None) -> DiagnosticResult:
    """Test for cointegration using Johansen test.
    
    Args:
        data: Prepared data dictionary
        det_order: Deterministic trend assumption
        k_ar_diff: Number of lagged differences (auto-selected if None)
        
    Returns:
        DiagnosticResult with cointegration test results
    """
    with timer("cointegration_test"):
        info("Running Johansen cointegration test")
        
        if 'log_prices' not in data:
            return DiagnosticResult(
                test_name="Johansen Cointegration",
                category="pre_estimation",
                statistic=np.nan,
                p_value=None,
                critical_value=None,
                passed=False,
                interpretation="No price data available",
                details={}
            )
        
        log_prices = data['log_prices'].dropna()
        
        # Auto-select lag order if not provided
        if k_ar_diff is None:
            var_model = VAR(log_prices)
            lag_order = var_model.select_order(maxlags=12)
            k_ar_diff = lag_order.aic
            info(f"Selected lag order: {k_ar_diff + 1}")
        
        try:
            # Run Johansen test
            joh_results = coint_johansen(log_prices.values, det_order, k_ar_diff)
            
            # Extract results
            trace_stats = joh_results.lr1
            trace_crits = joh_results.cvt[:, 1]  # 5% critical values
            eigen_stats = joh_results.lr2
            eigen_crits = joh_results.cvm[:, 1]
            
            # Determine cointegration rank
            n_coint_trace = np.sum(trace_stats > trace_crits)
            n_coint_eigen = np.sum(eigen_stats > eigen_crits)
            n_coint = min(n_coint_trace, n_coint_eigen)
            
            details = {
                'trace_stats': trace_stats.tolist()[:5],  # First 5
                'trace_crits_5%': trace_crits.tolist()[:5],
                'eigen_stats': eigen_stats.tolist()[:5],
                'eigen_crits_5%': eigen_crits.tolist()[:5],
                'n_coint_trace': int(n_coint_trace),
                'n_coint_eigen': int(n_coint_eigen),
                'selected_rank': int(n_coint),
                'lag_order': int(k_ar_diff + 1)
            }
            
            # Interpretation
            n_vars = log_prices.shape[1]
            if n_coint == 0:
                interpretation = "No cointegration found - consider VAR in differences"
                passed = False
            elif n_coint == n_vars - 1:
                interpretation = f"System is stationary ({n_coint} cointegrating vectors)"
                passed = False
            else:
                interpretation = f"Found {n_coint} cointegrating relationship(s) - proceed with VECM"
                passed = True
            
            return DiagnosticResult(
                test_name="Johansen Cointegration",
                category="pre_estimation",
                statistic=float(trace_stats[0]),  # r=0 test statistic
                p_value=None,  # Not easily available
                critical_value=float(trace_crits[0]),
                passed=passed,
                interpretation=interpretation,
                details=details
            )
            
        except Exception as e:
            error(f"Cointegration test failed: {e}")
            return DiagnosticResult(
                test_name="Johansen Cointegration",
                category="pre_estimation",
                statistic=np.nan,
                p_value=None,
                critical_value=None,
                passed=False,
                interpretation=f"Test failed: {str(e)}",
                details={}
            )


def test_lag_order_selection(data: Dict[str, Any],
                           maxlags: int = 12,
                           criteria: List[str] = ['aic', 'bic', 'hqic']) -> DiagnosticResult:
    """Select optimal lag order using information criteria.
    
    Args:
        data: Prepared data dictionary
        maxlags: Maximum number of lags to test
        criteria: List of criteria to use
        
    Returns:
        DiagnosticResult with lag order selection results
    """
    with timer("lag_order_selection"):
        info("Selecting optimal lag order")
        
        if 'dlogs' not in data:
            return DiagnosticResult(
                test_name="Lag Order Selection",
                category="pre_estimation",
                statistic=np.nan,
                p_value=None,
                critical_value=None,
                passed=False,
                interpretation="No differenced data available",
                details={}
            )
        
        dlogs = data['dlogs'].dropna()
        
        # Adjust maxlags based on sample size
        T = len(dlogs)
        maxlags = min(maxlags, int(T/4))  # Rule of thumb
        
        try:
            # Fit VAR model
            var_model = VAR(dlogs)
            lag_order_results = var_model.select_order(maxlags=maxlags)
            
            # Extract selected lags
            selected_lags = {}
            ic_values = {}
            
            for criterion in criteria:
                if hasattr(lag_order_results, criterion):
                    selected_lags[criterion.upper()] = int(getattr(lag_order_results, criterion))
                    # Get IC values
                    ic_dict = getattr(lag_order_results, f'{criterion}s', {})
                    if ic_dict:
                        ic_values[criterion.upper()] = {
                            str(k): float(v) for k, v in ic_dict.items()
                        }
            
            # Consensus lag order (mode)
            lag_values = list(selected_lags.values())
            consensus_lag = int(max(set(lag_values), key=lag_values.count))
            
            details = {
                'selected_lags': selected_lags,
                'consensus_lag': consensus_lag,
                'ic_values': ic_values,
                'maxlags_tested': maxlags,
                'sample_size': T
            }
            
            # Check if selected lag is reasonable
            if consensus_lag == 0:
                interpretation = "Selected lag order is 0 - model may be misspecified"
                passed = False
            elif consensus_lag == maxlags:
                interpretation = f"Selected lag order equals maximum ({maxlags}) - consider increasing maxlags"
                passed = False
            else:
                interpretation = f"Optimal lag order: {consensus_lag} (consensus across criteria)"
                passed = True
            
            return DiagnosticResult(
                test_name="Lag Order Selection",
                category="pre_estimation",
                statistic=float(consensus_lag),
                p_value=None,
                critical_value=None,
                passed=passed,
                interpretation=interpretation,
                details=details
            )
            
        except Exception as e:
            error(f"Lag order selection failed: {e}")
            return DiagnosticResult(
                test_name="Lag Order Selection",
                category="pre_estimation",
                statistic=np.nan,
                p_value=None,
                critical_value=None,
                passed=False,
                interpretation=f"Selection failed: {str(e)}",
                details={}
            )


def test_rank_selection(data: Dict[str, Any]) -> DiagnosticResult:
    """Test and select cointegration rank using multiple criteria.
    
    Args:
        data: Prepared data dictionary
        
    Returns:
        DiagnosticResult with rank selection results
    """
    # This is essentially the same as cointegration test
    # but focuses on rank selection rather than existence
    result = test_cointegration(data)
    result.test_name = "Cointegration Rank Selection"
    
    if 'selected_rank' in result.details:
        rank = result.details['selected_rank']
        n_vars = len(data.get('markets', []))
        
        if rank == 0:
            result.interpretation = "No cointegration (rank = 0) - use VAR in differences"
        elif rank == n_vars - 1:
            result.interpretation = f"System is I(0) (rank = {rank}) - use VAR in levels"
        else:
            result.interpretation = f"Selected cointegration rank: {rank}"
    
    return result


def test_deterministic_components(data: Dict[str, Any]) -> DiagnosticResult:
    """Test for appropriate deterministic components (constant, trend).
    
    Args:
        data: Prepared data dictionary
        
    Returns:
        DiagnosticResult with deterministic component test results
    """
    with timer("deterministic_test"):
        info("Testing for deterministic components")
        
        if 'log_prices' not in data:
            return DiagnosticResult(
                test_name="Deterministic Components",
                category="pre_estimation",
                statistic=np.nan,
                p_value=None,
                critical_value=None,
                passed=False,
                interpretation="No price data available",
                details={}
            )
        
        log_prices = data['log_prices'].dropna()
        
        # Test for trend in levels
        from scipy import stats
        trend_tests = {}
        
        for col in log_prices.columns[:5]:  # Test first 5 series
            y = log_prices[col].values
            x = np.arange(len(y))
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)
            
            trend_tests[col] = {
                'slope': float(slope),
                'p_value': float(p_value),
                'significant': p_value < 0.05
            }
        
        # Count significant trends
        n_trends = sum(1 for t in trend_tests.values() if t['significant'])
        prop_trends = n_trends / len(trend_tests)
        
        details = {
            'trend_tests': trend_tests,
            'proportion_with_trend': float(prop_trends),
            'recommendation': 'include_trend' if prop_trends > 0.5 else 'no_trend'
        }
        
        if prop_trends > 0.8:
            interpretation = "Strong evidence of deterministic trends - include trend in cointegration"
            passed = True
        elif prop_trends > 0.5:
            interpretation = "Some evidence of trends - consider including trend"
            passed = True
        else:
            interpretation = "Little evidence of deterministic trends - constant only may suffice"
            passed = True
        
        return DiagnosticResult(
            test_name="Deterministic Components",
            category="pre_estimation",
            statistic=float(prop_trends),
            p_value=None,
            critical_value=0.5,
            passed=passed,
            interpretation=interpretation,
            details=details
        )


def test_gregory_hansen_cointegration(data: Dict[str, Any],
                                    test_type: str = 'c',
                                    trim_pct: float = 0.15) -> DiagnosticResult:
    """Test for cointegration with structural breaks using Gregory-Hansen test.
    
    This test extends the Engle-Granger approach to allow for a single
    structural break in the cointegrating relationship at an unknown date.
    
    Args:
        data: Prepared data dictionary with log_prices
        test_type: Test specification
            'c' : level shift (default)
            'ct': level shift with trend
            'cs': regime shift (slope and intercept)
        trim_pct: Trimming percentage for break search (default 0.15)
        
    Returns:
        DiagnosticResult with test results and identified break date
    """
    with timer("gregory_hansen_test"):
        info("Running Gregory-Hansen cointegration test with structural break")
        
        if 'log_prices' not in data:
            return DiagnosticResult(
                test_name="Gregory-Hansen Cointegration",
                category="pre_estimation",
                statistic=np.nan,
                p_value=None,
                critical_value=None,
                passed=False,
                interpretation="No price data available for testing",
                details={}
            )
        
        log_prices = data['log_prices']
        markets = data.get('markets', log_prices.columns.tolist())
        dates = log_prices.index
        T = len(log_prices)
        
        if len(markets) < 2:
            return DiagnosticResult(
                test_name="Gregory-Hansen Cointegration",
                category="pre_estimation",
                statistic=np.nan,
                p_value=None,
                critical_value=None,
                passed=False,
                interpretation="Need at least 2 markets for cointegration test",
                details={}
            )
        
        # For simplicity, test pairwise cointegration for first two markets
        # In practice, could test all pairs or use multivariate extension
        y = log_prices[markets[0]].values
        x = log_prices[markets[1]].values
        
        # Trim observations for break search
        trim_n = int(T * trim_pct)
        break_range = range(trim_n, T - trim_n)
        
        # Store test statistics for each potential break
        test_stats = []
        
        with progress("Testing break points", total=len(break_range)) as update:
            for tau in break_range:
                # Create dummy variables for structural break
                dummy_level = np.zeros(T)
                dummy_level[tau:] = 1
                
                if test_type == 'c':
                    # Level shift only
                    X = np.column_stack([np.ones(T), x, dummy_level])
                elif test_type == 'ct':
                    # Level shift with trend
                    trend = np.arange(T)
                    X = np.column_stack([np.ones(T), trend, x, dummy_level])
                elif test_type == 'cs':
                    # Regime shift (slope and intercept)
                    dummy_slope = np.zeros(T)
                    dummy_slope[tau:] = x[tau:]
                    X = np.column_stack([np.ones(T), x, dummy_level, dummy_slope])
                else:
                    raise ValueError(f"Unknown test type: {test_type}")
                
                # OLS regression
                try:
                    beta = np.linalg.lstsq(X, y, rcond=None)[0]
                    residuals = y - X @ beta
                    
                    # ADF test on residuals
                    adf_stat = adfuller(residuals, maxlag=0, regression='nc')[0]
                    test_stats.append((tau, adf_stat))
                except Exception as e:
                    warning(f"Test failed for tau={tau}: {e}")
                    test_stats.append((tau, np.nan))
                
                update(1)
        
        # Find minimum (most negative) test statistic
        valid_stats = [(t, s) for t, s in test_stats if not np.isnan(s)]
        
        if not valid_stats:
            return DiagnosticResult(
                test_name="Gregory-Hansen Cointegration",
                category="pre_estimation",
                statistic=np.nan,
                p_value=None,
                critical_value=None,
                passed=False,
                interpretation="Test computation failed",
                details={}
            )
        
        break_date_idx, min_stat = min(valid_stats, key=lambda x: x[1])
        break_date = dates[break_date_idx]
        
        # Critical values for Gregory-Hansen test (approximate)
        # Source: Gregory and Hansen (1996), Table 1
        critical_values = {
            'c': {'1%': -5.44, '5%': -4.92, '10%': -4.69},
            'ct': {'1%': -5.80, '5%': -5.29, '10%': -5.03},
            'cs': {'1%': -5.97, '5%': -5.50, '10%': -5.23}
        }
        
        crit_val_5pct = critical_values[test_type]['5%']
        passed = min_stat < crit_val_5pct
        
        # Calculate break magnitude
        if test_type == 'c':
            # Re-estimate at optimal break
            dummy_level = np.zeros(T)
            dummy_level[break_date_idx:] = 1
            X_opt = np.column_stack([np.ones(T), x, dummy_level])
            beta_opt = np.linalg.lstsq(X_opt, y, rcond=None)[0]
            break_magnitude = beta_opt[2]  # Coefficient on level shift dummy
        else:
            break_magnitude = np.nan
        
        details = {
            'test_statistic': float(min_stat),
            'critical_value_5%': float(crit_val_5pct),
            'break_date': str(break_date),
            'break_date_index': int(break_date_idx),
            'break_proportion': float(break_date_idx / T),
            'break_magnitude': float(break_magnitude) if not np.isnan(break_magnitude) else None,
            'test_type': test_type,
            'markets_tested': markets[:2]
        }
        
        if passed:
            interpretation = (f"Cointegration with structural break detected at {break_date.strftime('%Y-%m')}"
                            f" (test stat: {min_stat:.3f} < critical: {crit_val_5pct:.3f})")
        else:
            interpretation = (f"No cointegration found even allowing for structural break"
                            f" (test stat: {min_stat:.3f} > critical: {crit_val_5pct:.3f})")
        
        log_metric("gregory_hansen_stat", min_stat)
        log_metric("gregory_hansen_break_date", break_date_idx)
        
        return DiagnosticResult(
            test_name="Gregory-Hansen Cointegration",
            category="pre_estimation",
            statistic=float(min_stat),
            p_value=None,  # No simple p-value for this test
            critical_value=float(crit_val_5pct),
            passed=passed,
            interpretation=interpretation,
            details=details
        )