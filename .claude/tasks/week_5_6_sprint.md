# Week 5-6 Sprint: Core Econometric Models

**Sprint Goal**: Implement dual-track econometric models with threshold effects

## 📅 Sprint Overview

**Duration**: 2 weeks (Week 5-6)  
**Start Date**: Ready to begin  
**Approach**: Dual-track (Complex + Simple models in parallel)

## 🎯 Week 5 Focus: Model Implementation

### Track 1: Complex Models
- [ ] Bayesian TVP-VECM with PyMC
- [ ] Multiple threshold estimation
- [ ] Conflict-modulated parameter evolution
- [ ] Network-augmented spatial weights

### Track 2: Simple Models  
- [ ] Single threshold VECM at 50 events
- [ ] Within-zone analysis (clean identification)
- [ ] Parameter equality tests across regimes
- [ ] Basic spatial weights

## 📋 Implementation Checklist

### Setup & Infrastructure
- [x] Base model classes created
- [x] Model comparison framework ready
- [x] Diagnostic test battery prepared
- [ ] PyMC environment configured
- [ ] GPU/MPS acceleration tested

### Data Preparation
- [x] Integrated panel datasets available
- [x] Smart panel with 88.4% coverage
- [x] Conflict metrics integrated
- [ ] Filter to single commodity/zone
- [ ] Create price matrices

### Model Development
- [ ] Track 1: BayesianTVPVECM class
- [ ] Track 1: Multiple threshold testing
- [ ] Track 2: SimpleThresholdVECM class
- [ ] Track 2: Regime-specific estimation
- [ ] Both: Diagnostic integration

### Testing & Validation
- [ ] Unit root test battery
- [ ] Cointegration tests
- [ ] Threshold significance tests
- [ ] Residual diagnostics
- [ ] Spatial autocorrelation tests

## 📊 Key Parameters

From EDA findings:
- **Primary threshold**: 50 conflict events/month
- **Secondary threshold**: 150 events/month (for Track 1)
- **Within-zone correlation**: 0.73 (strong)
- **Between-zone correlation**: 0.31 (weak)
- **Max exchange differential**: 89%

## 🔧 Technical Setup

```python
# Environment check
import pymc as pm
import statsmodels.api as sm
from arch.unitroot import ADF, KPSS
import geopandas as gpd

print(f"PyMC version: {pm.__version__}")
print(f"statsmodels version: {sm.__version__}")
```

## 📈 Success Metrics

**Week 5 Deliverables**:
1. [ ] Both model tracks implemented
2. [ ] Threshold at 50 events confirmed significant
3. [ ] Within-zone VECM results for Wheat
4. [ ] Diagnostic tests passing
5. [ ] Initial comparison table

**Week 6 Extensions**:
1. [ ] Multi-commodity analysis
2. [ ] Cross-zone integration tests
3. [ ] Policy simulation framework
4. [ ] Full robustness battery
5. [ ] Technical paper draft

## 🚨 Risk Mitigation

| Risk | Mitigation | Fallback |
|------|------------|----------|
| PyMC convergence issues | Increase samples/chains | Simplify model |
| Insufficient regime data | Adjust threshold | Pool with interactions |
| Computational time | Use subset first | Cloud compute |
| Model disagreement | Investigate source | Trust simple model |

## 📝 Daily Log

### Week 5
- **Monday**: 
- **Tuesday**: 
- **Wednesday**: 
- **Thursday**: 
- **Friday**: 

### Week 6
- **Monday**: 
- **Tuesday**: 
- **Wednesday**: 
- **Thursday**: 
- **Friday**: 

## 🔗 Key Resources

- [Implementation Guide](.claude/models/claude_implementation_guide.md)
- [Testing Framework](.claude/models/diagnostic_testing_framework.md)
- [Quick Start](.claude/models/quick_start_week5.md)
- [EDA Findings](reports/eda_findings.md)

---
**Status**: Ready to begin implementation