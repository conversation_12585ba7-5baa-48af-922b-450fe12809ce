"""Unit tests for spatial joins between markets and control zones."""

import json
from pathlib import Path
from unittest.mock import MagicMock, patch, call

import geopandas as gpd
import numpy as np
import pandas as pd
import pytest
from shapely.geometry import Point, Polygon

from yemen_market.data.spatial_joins import SpatialJoiner


class TestSpatialJoiner:
    """Test suite for spatial joining operations."""
    
    @pytest.fixture
    def joiner(self, tmp_path):
        """Create SpatialJoiner instance with temp directories."""
        markets_dir = tmp_path / "markets"
        control_dir = tmp_path / "control"
        output_dir = tmp_path / "output"
        return SpatialJoiner(
            markets_dir=markets_dir,
            control_dir=control_dir,
            output_dir=output_dir,
            buffer_distance_km=10.0
        )
    
    @pytest.fixture
    def sample_markets(self):
        """Create sample market GeoDataFrame."""
        data = {
            'governorate': ['Sana\'a', 'Aden', 'Taiz', 'Hadramaut', 'Al Hudaydah'],
            'district': ['Sana\'a City', 'Crater', '<PERSON>', '<PERSON><PERSON><PERSON>', 'Al Hudaydah'],
            'market_name': ['Central Market', 'Port Market', 'Old City', 'Fish Market', 'Coastal Market'],
            'lat': [15.3694, 12.7855, 13.5795, 14.5401, 14.7975],
            'lon': [44.1910, 45.0187, 44.0177, 49.1242, 42.9545]
        }
        
        gdf = gpd.GeoDataFrame(
            data,
            geometry=[Point(lon, lat) for lat, lon in zip(data['lat'], data['lon'])],
            crs='EPSG:4326'
        )
        
        gdf['market_id'] = gdf['governorate'] + '_' + gdf['market_name'].str.replace(' ', '_')
        
        return gdf
    
    @pytest.fixture
    def sample_control_zones(self):
        """Create sample control zone GeoDataFrame."""
        # Create polygons around market areas
        polygons = [
            # Sana'a region (Houthi)
            Polygon([(44.0, 15.2), (44.4, 15.2), (44.4, 15.5), (44.0, 15.5)]),
            # Aden region (Government)
            Polygon([(44.8, 12.6), (45.2, 12.6), (45.2, 13.0), (44.8, 13.0)]),
            # Taiz region (Contested)
            Polygon([(43.8, 13.4), (44.2, 13.4), (44.2, 13.8), (43.8, 13.8)]),
            # Hadramaut region (Government)
            Polygon([(48.9, 14.3), (49.3, 14.3), (49.3, 14.7), (48.9, 14.7)]),
            # Al Hudaydah region (Contested)
            Polygon([(42.7, 14.6), (43.1, 14.6), (43.1, 15.0), (42.7, 15.0)])
        ]
        
        data = {
            'governorate': ['Sana\'a', 'Aden', 'Taiz', 'Hadramaut', 'Al Hudaydah'],
            'district': ['Sana\'a City', 'Crater', 'Al Mudhaffar', 'Mukalla', 'Al Hudaydah'],
            'control_zone': ['houthi', 'government', 'contested', 'government', 'contested'],
            'date': pd.Timestamp('2024-01-01')
        }
        
        return gpd.GeoDataFrame(data, geometry=polygons, crs='EPSG:4326')
    
    @pytest.fixture
    def sample_wfp_data(self, tmp_path, sample_markets):
        """Create sample WFP data file."""
        # Create sample price data
        markets_expanded = []
        for _, market in sample_markets.iterrows():
            for date in pd.date_range('2024-01-01', periods=3, freq='M'):
                # Convert to dict excluding geometry column
                market_data = market.drop('geometry').to_dict()
                market_data['date'] = date
                market_data['price_usd'] = np.random.uniform(1, 10)
                markets_expanded.append(market_data)
        
        df = pd.DataFrame(markets_expanded)
        
        # Save as parquet
        file_path = tmp_path / "markets" / "wfp_price_data.parquet"
        file_path.parent.mkdir(exist_ok=True)
        df.to_parquet(file_path)
        
        return file_path
    
    @pytest.fixture
    def sample_control_data(self, tmp_path, sample_control_zones):
        """Create sample control zone data file."""
        # Save without geometry for CSV
        df = sample_control_zones.drop('geometry', axis=1)
        
        file_path = tmp_path / "control" / "control_zones_raw.csv"
        file_path.parent.mkdir(exist_ok=True)
        df.to_csv(file_path, index=False)
        
        return file_path
    
    def test_init(self, joiner):
        """Test SpatialJoiner initialization."""
        assert joiner.buffer_distance_km == 10.0
        assert joiner.output_dir.exists()
    
    def test_load_market_coordinates(self, joiner, sample_wfp_data):
        """Test loading market coordinates from WFP data."""
        markets = joiner.load_market_coordinates(sample_wfp_data)
        
        assert isinstance(markets, gpd.GeoDataFrame)
        assert len(markets) == 5  # 5 unique markets
        assert 'market_id' in markets.columns
        assert 'geometry' in markets.columns
        assert markets.crs == 'EPSG:4326'
    
    def test_load_market_coordinates_no_file(self, joiner):
        """Test handling when no WFP data found."""
        with pytest.raises(FileNotFoundError):
            joiner.load_market_coordinates()
    
    def test_load_market_coordinates_missing_coords(self, joiner, tmp_path):
        """Test handling markets without coordinates."""
        # Create data with missing coordinates
        df = pd.DataFrame({
            'governorate': ['Test1', 'Test2'],
            'market_name': ['Market1', 'Market2'],
            'lat': [15.0, np.nan],
            'lon': [44.0, np.nan]
        })
        
        file_path = tmp_path / "markets" / "test_data.parquet"
        file_path.parent.mkdir(exist_ok=True)
        df.to_parquet(file_path)
        
        markets = joiner.load_market_coordinates(file_path)
        assert len(markets) == 1  # Only one market with valid coordinates
    
    def test_load_control_zones_no_geometry(self, joiner, sample_control_data):
        """Test loading control zones without geometry."""
        # This should return empty GeoDataFrame since CSV has no geometry
        zones = joiner.load_control_zones(sample_control_data)
        assert isinstance(zones, gpd.GeoDataFrame)
        assert zones.empty
    
    def test_perform_spatial_join(self, joiner, sample_markets, sample_control_zones):
        """Test spatial join between markets and control zones."""
        result = joiner.perform_spatial_join(sample_markets, sample_control_zones)
        
        assert isinstance(result, gpd.GeoDataFrame)
        assert len(result) == len(sample_markets)
        assert 'control_zone' in result.columns
        assert 'market_id' in result.columns
        assert result['control_zone'].notna().all()  # All markets should be matched
    
    def test_perform_spatial_join_with_date(self, joiner, sample_markets, 
                                           sample_control_zones):
        """Test spatial join with date filtering."""
        # Add another date to control zones
        zones2 = sample_control_zones.copy()
        zones2['date'] = pd.Timestamp('2024-02-01')
        zones2['control_zone'] = 'government'  # All government in Feb
        
        all_zones = pd.concat([sample_control_zones, zones2], ignore_index=True)
        all_zones = gpd.GeoDataFrame(all_zones, crs='EPSG:4326')
        
        # Join for specific date
        result = joiner.perform_spatial_join(
            sample_markets, all_zones, 
            date=pd.Timestamp('2024-02-01')
        )
        
        # All should be government in February
        assert (result['control_zone'] == 'government').all()
    
    def test_perform_spatial_join_unmatched(self, joiner, sample_markets):
        """Test handling unmatched markets."""
        # Create zones that don't overlap with markets
        zones = gpd.GeoDataFrame({
            'governorate': ['Other'],
            'district': ['Other'],
            'control_zone': ['other'],
            'geometry': [Polygon([(50, 20), (51, 20), (51, 21), (50, 21)])]
        }, crs='EPSG:4326')
        
        result = joiner.perform_spatial_join(sample_markets, zones)
        
        # Should find nearest zone for unmatched
        assert result['control_zone'].notna().all()
        assert 'distance_to_zone_km' in result.columns
        assert (result['distance_to_zone_km'] > 0).any()
    
    def test_identify_boundary_markets(self, joiner, sample_markets, 
                                      sample_control_zones):
        """Test identifying markets near zone boundaries."""
        # First perform spatial join
        market_zones = joiner.perform_spatial_join(sample_markets, sample_control_zones)
        
        # Identify boundary markets
        boundary = joiner.identify_boundary_markets(market_zones, sample_control_zones)
        
        assert isinstance(boundary, pd.DataFrame)
        if not boundary.empty:
            assert 'market_id' in boundary.columns
            assert 'primary_zone' in boundary.columns
            assert 'nearby_zones' in boundary.columns
            assert 'is_boundary' in boundary.columns
            assert boundary['is_boundary'].all()
    
    def test_identify_boundary_markets_none(self, joiner):
        """Test when no boundary markets exist."""
        # Create isolated market
        markets = gpd.GeoDataFrame({
            'market_id': ['test'],
            'market_name': ['Test Market'],
            'control_zone': ['houthi'],
            'geometry': [Point(44, 15)]
        }, crs='EPSG:4326')
        
        # Single zone far from market
        zones = gpd.GeoDataFrame({
            'control_zone': ['houthi'],
            'geometry': [Polygon([(43, 14), (45, 14), (45, 16), (43, 16)])]
        }, crs='EPSG:4326')
        
        boundary = joiner.identify_boundary_markets(markets, zones)
        assert boundary.empty or len(boundary) == 0
    
    def test_calculate_zone_distances(self, joiner, sample_markets, 
                                     sample_control_zones):
        """Test calculating distances to all control zones."""
        distances = joiner.calculate_zone_distances(sample_markets, sample_control_zones)
        
        assert isinstance(distances, pd.DataFrame)
        assert distances.shape[0] == len(sample_markets)
        assert all(zone in distances.columns for zone in ['houthi', 'government', 'contested'])
        assert (distances >= 0).all().all()  # All distances non-negative
    
    def test_haversine_distance(self, joiner):
        """Test Haversine distance calculation."""
        # Sana'a to Aden (approximately 320 km)
        dist = joiner._haversine_distance(15.3694, 44.1910, 12.7855, 45.0187)
        assert 300 < dist < 340
        
        # Same point
        dist = joiner._haversine_distance(15.0, 44.0, 15.0, 44.0)
        assert dist == 0
    
    def test_create_temporal_mapping(self, joiner, sample_markets):
        """Test creating temporal market-zone mapping."""
        # Create zones for multiple dates
        dates = pd.date_range('2024-01-01', periods=3, freq='M')
        zones_list = []
        
        for i, date in enumerate(dates):
            zones = gpd.GeoDataFrame({
                'governorate': ['Sana\'a', 'Aden'],
                'district': ['Sana\'a City', 'Crater'],
                'control_zone': ['houthi', 'government'] if i == 0 else ['contested', 'government'],
                'date': date,
                'geometry': [
                    Polygon([(44.0, 15.2), (44.4, 15.2), (44.4, 15.5), (44.0, 15.5)]),
                    Polygon([(44.8, 12.6), (45.2, 12.6), (45.2, 13.0), (44.8, 13.0)])
                ]
            }, crs='EPSG:4326')
            zones_list.append(zones)
        
        all_zones = pd.concat(zones_list, ignore_index=True)
        all_zones = gpd.GeoDataFrame(all_zones, crs='EPSG:4326')
        
        # Create temporal mapping
        mapping = joiner.create_temporal_mapping(sample_markets[:2], all_zones)
        
        assert not mapping.empty
        assert 'date' in mapping.columns
        assert 'zone_changed' in mapping.columns
        assert len(mapping) == 2 * 3  # 2 markets × 3 dates
        
        # Check that Sana'a market changed zones
        sanaa_data = mapping[mapping['market_id'].str.contains('Sana')]
        assert sanaa_data['zone_changed'].any()
    
    def test_create_temporal_mapping_no_dates(self, joiner, sample_markets,
                                             sample_control_zones):
        """Test temporal mapping when zones have no date column."""
        zones = sample_control_zones.drop('date', axis=1)
        
        mapping = joiner.create_temporal_mapping(sample_markets, zones)
        
        assert not mapping.empty
        assert 'date' in mapping.columns
        assert len(mapping) == len(sample_markets)
    
    def test_calculate_spatial_weights(self, joiner):
        """Test spatial weight calculation."""
        # Create simple distance matrix
        distance_matrix = pd.DataFrame({
            'market1': [0, 50, 150],
            'market2': [50, 0, 100],
            'market3': [150, 100, 0]
        }, index=['market1', 'market2', 'market3'])
        
        weights = joiner.calculate_spatial_weights(distance_matrix, cutoff_km=200)
        
        assert isinstance(weights, pd.DataFrame)
        # Current implementation returns input, full implementation would calculate weights
    
    def test_save_mappings(self, joiner, sample_markets, sample_control_zones):
        """Test saving all mapping outputs."""
        # Create sample data
        market_zones = joiner.perform_spatial_join(sample_markets, sample_control_zones)
        
        temporal_mapping = market_zones.copy()
        temporal_mapping['date'] = pd.Timestamp('2024-01-01')
        temporal_mapping['zone_changed'] = False
        
        boundary_markets = pd.DataFrame({
            'market_id': ['test1'],
            'market_name': ['Test Market'],
            'primary_zone': ['houthi'],
            'nearby_zones': ['government'],
            'n_nearby_zones': [1],
            'is_boundary': [True]
        })
        
        distance_matrix = pd.DataFrame({
            'houthi': [0, 100],
            'government': [100, 0]
        }, index=['market1', 'market2'])
        
        # Save all outputs
        saved = joiner.save_mappings(
            market_zones, temporal_mapping, 
            boundary_markets, distance_matrix
        )
        
        assert 'current_geojson' in saved
        assert 'current_parquet' in saved
        assert 'temporal' in saved
        assert 'boundary' in saved
        assert 'distances' in saved
        assert 'metadata' in saved
        
        # Check files exist
        assert all(path.exists() for path in saved.values())
        
        # Check metadata content
        with open(saved['metadata']) as f:
            metadata = json.load(f)
        
        assert 'n_markets' in metadata
        assert 'n_boundary_markets' in metadata
        assert metadata['buffer_distance_km'] == 10.0
    
    def test_save_mappings_empty(self, joiner):
        """Test saving empty mappings."""
        saved = joiner.save_mappings(
            gpd.GeoDataFrame(),
            pd.DataFrame(),
            pd.DataFrame(),
            pd.DataFrame()
        )
        
        # Should still save metadata
        assert 'metadata' in saved
        assert saved['metadata'].exists()
    
    def test_generate_summary_report(self, joiner, sample_markets, 
                                    sample_control_zones):
        """Test generating summary statistics."""
        # Create mapping
        market_zones = joiner.perform_spatial_join(sample_markets, sample_control_zones)
        
        # Create temporal data with changes
        temporal_mapping = []
        for date in pd.date_range('2024-01-01', periods=3, freq='M'):
            mapping = market_zones.copy()
            mapping['date'] = date
            mapping['zone_changed'] = date > pd.Timestamp('2024-01-01')
            temporal_mapping.append(mapping)
        
        temporal_df = pd.concat(temporal_mapping, ignore_index=True)
        
        # Generate summary
        summary = joiner.generate_summary_report(market_zones, temporal_df)
        
        assert not summary.empty
        assert 'metric' in summary.columns
        assert 'value' in summary.columns
        
        # Check for expected metrics
        metrics = summary['metric'].tolist()
        assert any('markets_in_' in m for m in metrics)
        assert 'total_zone_changes' in metrics
    
    def test_integration_workflow(self, joiner, sample_wfp_data, 
                                 sample_markets, sample_control_zones):
        """Test complete spatial join workflow."""
        # Load markets
        markets = joiner.load_market_coordinates(sample_wfp_data)
        assert not markets.empty
        
        # Perform spatial join
        market_zones = joiner.perform_spatial_join(markets, sample_control_zones)
        assert not market_zones.empty
        assert market_zones['control_zone'].notna().all()
        
        # Identify boundary markets
        boundary = joiner.identify_boundary_markets(market_zones, sample_control_zones)
        assert isinstance(boundary, pd.DataFrame)
        
        # Calculate distances
        distances = joiner.calculate_zone_distances(markets, sample_control_zones)
        assert not distances.empty
        
        # Create temporal mapping
        temporal = joiner.create_temporal_mapping(markets, sample_control_zones)
        assert not temporal.empty
        
        # Save everything
        saved = joiner.save_mappings(market_zones, temporal, boundary, distances)
        assert len(saved) > 0
        
        # Generate report
        summary = joiner.generate_summary_report(market_zones, temporal)
        assert not summary.empty