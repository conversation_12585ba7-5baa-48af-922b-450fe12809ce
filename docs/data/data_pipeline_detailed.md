# Detailed Data Pipeline Documentation

## Pipeline Overview

The Yemen Market Integration data pipeline processes raw data from multiple sources into analysis-ready panel datasets. The pipeline consists of five main stages:

1. **Data Download** → 2. **Individual Processing** → 3. **Spatial Integration** → 4. **Panel Construction** → 5. **Quality Assurance**

## Stage 1: Data Download

### 1.1 HDX Data Download (`scripts/download_data.py`)

Downloads data from Humanitarian Data Exchange using the HDX Python API.

```python
# Core components
- HDXClient: Manages HDX API connections and caching
- Dataset IDs:
  - WFP Prices: "98fc9c80-5c95-4670-8e5d-d37c62bb4bd6"
  - ACAPS Control: "899c9255-c855-4dad-bbef-6a1cce07cd2d"
  - Admin Boundaries: "cod-ab-yem"
```

**Process Flow:**
1. Initialize HDX configuration (no auth required)
2. Check cache validity (7-day default)
3. Download resources if cache expired
4. Save to `data/raw/hdx/` structure

### 1.2 ACLED Data Download (`scripts/download_acled_data.py`)

Downloads conflict event data using ACLED API (requires authentication).

```python
# API Configuration
- Endpoint: https://api.acleddata.com/acled/read
- Auth: API key + email (stored in .env)
- Parameters:
  - country: Yemen
  - start_date: 2019-01-01
  - end_date: 2024-12-31
  - limit: 0 (all records)
```

**Output**: 57,509 conflict events saved to `data/raw/acled/`

## Stage 2: Individual Data Processing

### 2.1 WFP Price Processing (`yemen_market.data.wfp_processor_enhanced`)

Enhanced processor with pcode support and smart panel creation.

**Key Enhancements:**
```python
# Governorate name standardization
GOVERNORATE_MAPPINGS = {
    "Al Dhale'e": "Ad Dale'",
    "Al Hudaydah": "Al Hodeidah",
    "Amanat Al Asimah": "Sana'a City",
    "Hadramaut": "Hadramawt",
    "Sa'ada": "Sa'dah",
    "Taizz": "Ta'iz"
}

# Smart commodity selection
min_market_coverage = 0.5  # Only include if in 50%+ of markets
```

**Processing Steps:**
1. Load raw CSV (skip HXL header rows)
2. Standardize governorate names to pcodes
3. Parse dates and filter (2019-01-01 onwards)
4. Separate exchange rates from commodity prices
5. Auto-select commodities based on market coverage
6. Create market IDs: `governorate_market`
7. Save coordinates with commodity data

**Output Files:**
- `wfp_commodity_prices_enhanced.parquet`: 33,926 price records
- `wfp_exchange_rates_enhanced.parquet`: 1,609 exchange rate records
- `wfp_smart_panel.parquet`: 14,208 balanced panel observations

### 2.2 ACAPS Control Zone Processing (`yemen_market.data.acaps_processor`)

Handles complex nested ZIP structure with multiple shapefiles.

**Special Handling:**
```python
# Nested ZIP structure
main_zip/
  ├── DFA/DFA_Control_YYYYMMDD.zip
  ├── IRG/IRG_Control_YYYYMMDD.zip
  └── STC/STC_Control_YYYYMMDD.zip

# Each inner ZIP contains shapefile components
# Process all and merge with control_zone labels
```

**Processing Steps:**
1. Extract main ZIP to temporary directory
2. Find and extract nested ZIPs
3. Load shapefiles for each control actor
4. Add control_zone field (DFA, IRG, STC, AQAP)
5. Merge all geometries into single GeoDataFrame
6. Reproject to WGS84 (EPSG:4326)
7. Extract governorate/district from attributes

**Output**: 362 district-level control polygons per time period

### 2.3 ACLED Conflict Processing (`yemen_market.data.acled_processor`)

Aggregates event-level data to market-month conflict metrics.

**Key Parameters:**
```python
radius_km = 50  # Events within 50km of market
base_year = 2019  # Start year for processing
```

**Processing Steps:**
1. Load event data and market locations
2. Create spatial index for efficient distance calculations
3. For each market-month:
   - Find events within radius
   - Count by event type (battles, explosions, etc.)
   - Sum fatalities
   - Count by actor (Houthis, Government, etc.)
4. Calculate conflict intensity metric:
   ```python
   intensity = 0.7 * n_events + 0.3 * fatalities
   ```
5. Add lagged variables (lag1, lag2, lag3)
6. Calculate 3-month moving average

**Output**: 2,016 market-month observations with conflict metrics

## Stage 3: Spatial Integration (`yemen_market.data.spatial_joins`)

Links markets to control zones using point-in-polygon operations.

**Process Flow:**
1. Load market points with coordinates
2. Load control zone polygons
3. Perform spatial join:
   ```python
   markets_with_zones = gpd.sjoin(
       markets_gdf, 
       zones_gdf,
       how='left',
       predicate='within'
   )
   ```
4. Handle markets on boundaries (nearest polygon)
5. Add distance to nearest zone boundary

**Key Outputs:**
- Market-to-zone mappings
- Boundary market identification (11 markets)
- Zone change tracking over time

## Stage 4: Panel Construction (`yemen_market.data.panel_builder`)

Creates integrated analysis-ready datasets.

### 4.1 Smart Panel Creation

**Innovation**: Only creates observations for commodity-market pairs that exist in the data.

```python
# Traditional approach (problematic)
all_commodities × all_markets × all_dates = 44,122 obs
# Result: 38% missing prices (structural zeros)

# Smart approach (implemented)
for commodity in commodities:
    markets_selling = get_markets_for_commodity(commodity)
    create_panel(commodity, markets_selling, dates)
# Result: 14,208 obs with 88.4% price coverage
```

### 4.2 Integration Steps

1. **Load Components**:
   - WFP prices and exchange rates
   - Market-zone mappings
   - Conflict metrics

2. **Merge Data**:
   ```python
   # Price + zones
   panel = prices.merge(zones, on=['market_id', 'year_month'])
   
   # Add conflict (with type conversion for year_month)
   panel = panel.merge(conflict, on=['market_id', 'year_month'])
   ```

3. **Add Features**:
   - Temporal: lags, differences, moving averages
   - Conflict regimes: low/medium/high based on quantiles
   - Seasonal: quarter, Ramadan indicator

4. **Handle Missing Data**:
   - Interpolate prices (max 2 periods)
   - Forward-fill exchange rates (max 3 periods)
   - Zone averages for remaining gaps

### 4.3 Model-Specific Datasets

Creates specialized panels for different analyses:

1. **Price Transmission**: Market pairs for cointegration
2. **Exchange Pass-through**: Includes rate differentials
3. **Threshold Cointegration**: All variables including conflict
4. **Spatial Analysis**: Geographic variables and distances

## Stage 5: Quality Assurance

### 5.1 Data Validation Checks

**Automated Checks**:
- Coordinate validity (Yemen bounding box)
- Price outliers (IQR method)
- Temporal consistency (no future dates)
- Missing data patterns
- Duplicate detection

### 5.2 Coverage Metrics

**Final Dataset Statistics**:
- Markets: 28 (all major cities)
- Commodities: 23 (selected based on availability)
- Time Period: 2019-01 to 2024-12 (72 months)
- Observations: 14,208 total
- Price Coverage: 88.4%
- Control Zone Coverage: 92.9%
- Conflict Data Coverage: 60.5%

### 5.3 Known Limitations

1. **ACAPS Coverage**: Only from 2021 onwards
2. **Market Selection**: Urban bias (rural markets underrepresented)
3. **Commodity Availability**: Some items only in specific regions
4. **Conflict Reporting**: Potential undercount in contested areas

## Pipeline Execution

### Full Pipeline Run:
```bash
# 1. Download latest data
python scripts/download_data.py
python scripts/download_acled_data.py

# 2. Process individual sources
python scripts/process_wfp_data.py      # Uses enhanced processor
python scripts/process_acaps_data.py    # Handles nested ZIPs
python scripts/process_acled_data.py    # Creates conflict metrics

# 3. Spatial integration
python scripts/run_spatial_joins.py     # Maps markets to zones

# 4. Build panels
python scripts/build_panel_datasets.py  # Creates final datasets
```

### Or use Makefile:
```bash
make pipeline  # Runs all steps in sequence
```

## Output File Structure

```
data/processed/
├── wfp_commodity_prices_enhanced.parquet  # 33,926 records
├── wfp_exchange_rates_enhanced.parquet    # 1,609 records  
├── wfp_smart_panel.parquet               # 14,208 observations
├── control_zones/
│   ├── acaps_control_zones_raw.parquet   # 362 polygons
│   └── control_zones_monthly.parquet     # Time series
├── conflict/
│   ├── conflict_metrics.parquet          # 2,016 market-months
│   └── conflict_summary.parquet          # Summary stats
├── spatial/
│   ├── market_zones_temporal.parquet     # Time-varying mappings
│   └── boundary_markets.csv              # 11 boundary markets
└── panels/
    ├── integrated_panel.parquet          # Main analysis dataset
    ├── threshold_coint_panel.parquet     # For TVECM models
    └── panel_metadata.json               # Dataset documentation
```