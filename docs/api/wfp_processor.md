# WFPProcessor API Reference

The `WFPProcessor` class handles World Food Programme (WFP) price data processing with enhanced features including pcode integration and smart panel creation.

## Class: WFPProcessor

```python
from yemen_market.data import WFPProcessor

processor = WFPProcessor(
    commodities=None,
    start_date=None,
    end_date=None,
    min_market_coverage=0.3
)
```

### Parameters

- **commodities** (list[str], optional): List of commodities to analyze. If None, auto-selects based on coverage.
- **start_date** (str, optional): Start date for analysis (format: 'YYYY-MM-DD'). Defaults to config value.
- **end_date** (str, optional): End date for analysis. Defaults to config value.
- **min_market_coverage** (float): Minimum fraction of markets a commodity must appear in (default: 0.3).

### Key Methods

#### process_price_data()
Process WFP price data with enhanced filtering and pcode support.

```python
commodity_prices, exchange_rates = processor.process_price_data(raw_data_path=None)
```

**Returns:**
- **commodity_prices** (DataFrame): Processed commodity price data
- **exchange_rates** (DataFrame): Processed exchange rate data

#### create_smart_panels()
Create smart panel data that respects commodity availability.

```python
smart_panel = processor.create_smart_panels(commodity_df)
```

**Parameters:**
- **commodity_df** (DataFrame): DataFrame with commodity prices

**Returns:**
- **smart_panel** (DataFrame): Panel data with better coverage (88.4%+)

#### extract_commodity_prices()
Extract raw commodity price data for panel building.

```python
prices = processor.extract_commodity_prices(df)
```

#### extract_exchange_rates()
Extract exchange rate data from price data.

```python
exchange_rates = processor.extract_exchange_rates(df)
```

### Attributes

#### Governorate Mappings
```python
GOVERNORATE_MAPPINGS = {
    "Al Dhale'e": "Ad Dale'",
    "Al Hudaydah": "Al Hodeidah",
    "Amanat Al Asimah": "Sana'a City",
    "Hadramaut": "Hadramawt",
    "Sa'ada": "Sa'dah",
    "Taizz": "Ta'iz"
}
```

#### Zone Classifications
```python
HOUTHI_GOVERNORATES = ["Sana'a", "Sa'ada", "Hajjah", ...]
GOVERNMENT_GOVERNORATES = ["Aden", "Lahj", "Abyan", ...]
```

### Example Usage

```python
from yemen_market.data import WFPProcessor

# Initialize processor
processor = WFPProcessor(
    min_market_coverage=0.5,  # Only include widely-available commodities
    start_date='2019-01-01'
)

# Process data
commodity_prices, exchange_rates = processor.process_price_data()

# Create smart panels
smart_panel = processor.create_smart_panels(commodity_prices)

# Check coverage
coverage = smart_panel['price_usd'].notna().sum() / len(smart_panel) * 100
print(f"Price coverage: {coverage:.1f}%")  # Should be ~88.4%
```

### Data Processing Pipeline

1. **Load raw data** from CSV
2. **Standardize governorate names** using pcode mappings
3. **Filter by date range** and clean data
4. **Separate exchange rates** from commodity prices
5. **Auto-select commodities** based on market coverage
6. **Create smart panels** respecting actual availability
7. **Save processed data** with comprehensive summaries

### Output Files

- `wfp_commodity_prices.parquet`: Raw processed commodity prices
- `wfp_exchange_rates.parquet`: Exchange rate data
- `wfp_smart_panel.parquet`: Smart panel with 88.4% coverage
- `wfp_processing_summary.json`: Processing metadata and statistics