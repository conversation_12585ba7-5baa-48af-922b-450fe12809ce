# Deployment Guide

This guide covers deployment and setup of the Yemen Market Integration project.

## Prerequisites

- Python 3.9 or higher
- Conda (recommended) or pip
- Git
- At least 8GB RAM
- 10GB free disk space

## Installation

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/yemen-market-integration.git
cd yemen-market-integration
```

### 2. Set Up Environment

#### Using Conda (Recommended)

```bash
# Create environment from file
conda env create -f environment.yml

# Activate environment
conda activate yemen-market

# Install package in development mode
pip install -e .
```

#### Using pip

```bash
# Create virtual environment
python -m venv venv

# Activate environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Install requirements
pip install -r requirements.txt
pip install -e .
```

### 3. Configure Environment Variables

Create a `.env` file in the project root:

```bash
# ACLED API credentials
ACLED_API_KEY=your_acled_api_key
ACLED_API_EMAIL=<EMAIL>

# HDX configuration (optional)
HDX_API_KEY=your_hdx_api_key
HDX_SITE=prod  # or 'test' for testing
```

### 4. Verify Installation

```bash
# Run tests
pytest tests/

# Check imports
python -c "import yemen_market; print('Installation successful!')"
```

## Data Setup

### 1. Download Initial Data

```bash
# Download all HDX datasets
python scripts/data_collection/download_data.py

# Download ACLED conflict data (requires API key)
python scripts/data_collection/download_acled_data.py
```

### 2. Process Data

Run the processing pipeline in order:

```bash
# Process WFP price data
python scripts/data_processing/process_wfp_data.py

# Process ACAPS control zones
python scripts/data_processing/process_acaps_data.py

# Process ACLED conflict data
python scripts/data_processing/process_acled_data.py

# Run spatial joins
python scripts/data_processing/run_spatial_joins.py

# Build analysis panels
python scripts/analysis/build_panel_datasets.py
```

### 3. Verify Data

```bash
# Run pipeline test
python scripts/utilities/test_full_pipeline.py
```

## Running Analysis

### Jupyter Notebooks

```bash
# Start Jupyter
jupyter notebook

# Navigate to notebooks/ directory
# Run notebooks in order:
# 1. 01-data-validation.ipynb
# 2. 02-price-patterns.ipynb
# 3. 03-spatial-analysis.ipynb
```

### Command Line Analysis

```python
from yemen_market.data import PanelBuilder
from yemen_market.features import FeatureEngineer

# Load data
builder = PanelBuilder()
panel = builder.build_integrated_panel()

# Create features
engineer = FeatureEngineer()
features = engineer.fit_transform(panel)

print(f"Panel shape: {panel.shape}")
print(f"Features created: {len(features.columns) - len(panel.columns)}")
```

## Production Deployment

### Docker Container

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    gdal-bin \
    libgdal-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY . .
RUN pip install -e .

# Run pipeline
CMD ["python", "scripts/utilities/test_full_pipeline.py"]
```

Build and run:

```bash
docker build -t yemen-market .
docker run -v $(pwd)/data:/app/data yemen-market
```

### Cloud Deployment

#### AWS EC2

1. Launch EC2 instance (t3.large or larger)
2. Install dependencies:
   ```bash
   sudo yum update -y
   sudo yum install python3.9 git -y
   ```
3. Clone repository and follow installation steps
4. Set up cron jobs for data updates

#### GitHub Actions

See `.github/workflows/` for CI/CD pipelines:
- Automated testing on push
- Daily data updates
- Weekly full pipeline runs

## Monitoring

### Logging

Logs are written to `logs/` directory:
- `yemen_market.log`: Main application log
- `pipeline_YYYYMMDD.log`: Daily pipeline runs

### Health Checks

```python
# scripts/health_check.py
from yemen_market.utils.logging import info, error
import pandas as pd
from pathlib import Path

def check_data_freshness():
    """Check if data is up to date."""
    panel_file = Path('data/processed/panels/integrated_panel.parquet')
    
    if not panel_file.exists():
        error("Panel file not found!")
        return False
    
    panel = pd.read_parquet(panel_file)
    latest_date = panel['date'].max()
    days_old = (pd.Timestamp.now() - latest_date).days
    
    if days_old > 30:
        error(f"Data is {days_old} days old!")
        return False
    
    info(f"Data is current (last update: {latest_date})")
    return True

if __name__ == "__main__":
    check_data_freshness()
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Reinstall package
   pip install -e . --force-reinstall
   ```

2. **Missing Data Files**
   ```bash
   # Re-download data
   python scripts/data_collection/download_data.py --force
   ```

3. **Memory Errors**
   - Use smart panels instead of full panels
   - Process data in chunks
   - Increase system swap space

4. **ACLED API Errors**
   - Check API credentials in .env
   - Verify API rate limits
   - Use cached data if available

### Debug Mode

```bash
# Run with debug logging
YEMEN_LOG_LEVEL=DEBUG python scripts/data_processing/process_wfp_data.py
```

## Maintenance

### Daily Tasks
- Check logs for errors
- Verify data freshness
- Monitor disk space

### Weekly Tasks
- Run full pipeline test
- Update ACLED data
- Generate reports

### Monthly Tasks
- Update dependencies
- Review and archive logs
- Backup processed data

## Support

For issues or questions:
1. Check documentation in `docs/`
2. Review logs in `logs/`
3. Run diagnostic scripts
4. Open GitHub issue with details