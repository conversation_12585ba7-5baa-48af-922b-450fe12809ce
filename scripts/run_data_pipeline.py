#!/usr/bin/env python3
"""Run complete data pipeline for Yemen market integration analysis.

This script orchestrates the entire data pipeline from download to panel creation.
It should be run before any model estimation.
"""

import sys
import subprocess
from pathlib import Path
import pandas as pd
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.yemen_market.utils.logging import setup_logging, info, warning, error, success, timer

def run_command(cmd: str, description: str) -> bool:
    """Run a command and return success status."""
    info(f"Running: {description}")
    try:
        result = subprocess.run(cmd.split(), capture_output=True, text=True, check=True)
        success(f"✓ {description} completed")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        error(f"✗ {description} failed")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def check_data_exists() -> dict:
    """Check which data files already exist."""
    data_dir = Path("data")
    raw_dir = data_dir / "raw"
    processed_dir = data_dir / "processed"
    
    exists = {
        'wfp_prices': (raw_dir / "wfp" / "wfp_food_prices_yem.csv").exists(),
        'acaps_control': (raw_dir / "acaps" / "yemen_areas_of_control.geojson").exists(),
        'acled_conflict': (raw_dir / "acled" / "yemen_conflict_events.csv").exists(),
        'boundaries': (raw_dir / "boundaries" / "yem_adm2.geojson").exists(),
        'integrated_panel': (processed_dir / "panels" / "integrated_panel.parquet").exists()
    }
    
    return exists

def main():
    """Run the complete data pipeline."""
    setup_logging("data_pipeline")
    
    with timer("complete_data_pipeline"):
        info("=" * 60)
        info("Yemen Market Integration - Data Pipeline")
        info(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        info("=" * 60)
        
        # Check existing data
        exists = check_data_exists()
        info("\nData status:")
        for name, status in exists.items():
            status_str = "✓ exists" if status else "✗ missing"
            info(f"  {name}: {status_str}")
        
        # Step 1: Download data (if needed)
        if not all(exists.values()):
            info("\n" + "="*40)
            info("Step 1: Downloading data")
            info("="*40)
            
            if not run_command(
                "python scripts/data_collection/download_data.py",
                "Download all data from HDX"
            ):
                error("Data download failed - check internet connection and HDX availability")
                return 1
        else:
            info("\n✓ All raw data already downloaded")
        
        # Step 2: Process WFP price data
        info("\n" + "="*40)
        info("Step 2: Processing WFP price data")
        info("="*40)
        
        if not run_command(
            "python scripts/data_processing/process_wfp_data.py",
            "Process WFP price data"
        ):
            error("WFP data processing failed")
            return 1
        
        # Step 3: Process ACAPS control data
        info("\n" + "="*40)
        info("Step 3: Processing ACAPS control zones")
        info("="*40)
        
        if not run_command(
            "python scripts/data_processing/process_acaps_data.py",
            "Process ACAPS areas of control"
        ):
            error("ACAPS data processing failed")
            return 1
        
        # Step 4: Process ACLED conflict data
        info("\n" + "="*40)
        info("Step 4: Processing ACLED conflict data")
        info("="*40)
        
        if not run_command(
            "python scripts/data_processing/process_acled_data.py",
            "Process ACLED conflict events"
        ):
            error("ACLED data processing failed")
            return 1
        
        # Step 5: Run spatial joins
        info("\n" + "="*40)
        info("Step 5: Running spatial joins")
        info("="*40)
        
        if not run_command(
            "python scripts/data_processing/run_spatial_joins.py",
            "Join markets to control zones"
        ):
            error("Spatial joins failed")
            return 1
        
        # Step 6: Build panel datasets
        info("\n" + "="*40)
        info("Step 6: Building integrated panel")
        info("="*40)
        
        # Create the panel builder script if it doesn't exist
        panel_script = Path("scripts/data_processing/build_panel_datasets.py")
        if not panel_script.exists():
            create_panel_builder_script(panel_script)
        
        if not run_command(
            "python scripts/data_processing/build_panel_datasets.py",
            "Build integrated panel dataset"
        ):
            error("Panel building failed")
            return 1
        
        # Verify final output
        final_panel = Path("data/processed/panels/integrated_panel.parquet")
        if final_panel.exists():
            df = pd.read_parquet(final_panel)
            success("\n" + "="*60)
            success("✓ Data pipeline completed successfully!")
            success(f"  Final panel shape: {df.shape}")
            success(f"  Date range: {df['date'].min()} to {df['date'].max()}")
            success(f"  Markets: {df['market_name'].nunique()}")
            success(f"  Commodities: {df['commodity'].nunique()}")
            success("="*60)
        else:
            error("Final panel not created!")
            return 1
        
        return 0

def create_panel_builder_script(path: Path):
    """Create the panel builder script if it doesn't exist."""
    content = '''#!/usr/bin/env python3
"""Build integrated panel dataset from processed components."""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.yemen_market.data.panel_builder import PanelDatasetBuilder
from src.yemen_market.utils.logging import setup_logging, info, success, timer

def main():
    """Build integrated panel dataset."""
    setup_logging("panel_builder")
    
    with timer("build_panel"):
        info("Building integrated panel dataset")
        
        # Initialize builder
        builder = PanelDatasetBuilder()
        
        # Build the panel
        panel_df = builder.build_integrated_panel()
        
        # Save
        output_dir = Path("data/processed/panels")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_path = output_dir / "integrated_panel.parquet"
        panel_df.to_parquet(output_path, index=False)
        
        success(f"Saved integrated panel to {output_path}")
        info(f"Panel shape: {panel_df.shape}")
        info(f"Date range: {panel_df['date'].min()} to {panel_df['date'].max()}")

if __name__ == "__main__":
    main()
'''
    path.write_text(content)
    path.chmod(0o755)

if __name__ == "__main__":
    sys.exit(main())
