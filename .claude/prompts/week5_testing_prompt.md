# Claude Code Prompt: Week 5 Model Testing and Documentation Update

## Context
You are working on the Yemen Market Integration project. The Week 5 sprint code has been implemented with critical econometric corrections applied. Now you need to:
1. Run the models on actual data
2. Verify the corrections are working
3. Update documentation with results
4. Prepare for Week 6 extensions

## Your Tasks

### 1. Initial Setup and Dependency Check
```bash
# First, check all dependencies are installed
make check-deps

# If any are missing, install them:
pip install pymc arviz  # For Bayesian models
```

### 2. Run Data Pipeline (if needed)
```bash
# Check if integrated panel exists
ls -la data/processed/panels/integrated_panel.parquet

# If not, run the pipeline:
make run-pipeline
```

### 3. Quick Test with Synthetic Data
```bash
# Verify models work with test data
make quick-test

# Expected output:
# - Threshold around 50 events
# - Significant threshold effect
# - Different adjustment speeds by regime
```

### 4. Run Full Week 5 Models
```bash
# Run dual-track implementation
make week5-models

# This will:
# - Load Yemen wheat data (Houthi zone)
# - Run Track 2: Simple Threshold VECM
# - Run Track 1: Bayesian regime-switching
# - Compare results
# - Generate visualizations
```

### 5. Interactive Analysis (Optional)
```bash
# For detailed exploration
jupyter lab notebooks/04_models/01_week5_implementation.ipynb
```

### 6. Update Documentation Based on Results

After running the models, update the following files:

#### A. Update `.claude/models/week5_results.md` (create new file)
```markdown
# Week 5 Model Results

## Date: [TODAY'S DATE]

## Data Summary
- Commodity: Wheat
- Zone: Houthi
- Markets: [NUMBER]
- Time period: [START] to [END]
- Coverage: [X]%

## Key Findings

### Threshold Estimation
- Estimated threshold: [X] events/month
- Bootstrap p-value: [X]
- 95% CI: [[X], [X]]
- Statistically significant: [YES/NO]

### Adjustment Speeds
| Regime | Track 1 (Bayesian) | Track 2 (Threshold) |
|--------|-------------------|---------------------|
| Low conflict | [X] | [X] |
| High conflict | [X] | [X] |
| Ratio | [X] | [X] |

### Model Agreement
- Parameter correlation: [X]
- Regime classification agreement: [X]%
- Recommendation: [Use Track 2 / Investigate further]

### Regime Dynamics
- Expected duration in low: [X] months
- Expected duration in high: [X] months
- Current state: [X]% in high conflict

## Diagnostic Tests
- Unit roots: [PASSED/FAILED]
- Cointegration: [X] relationships found
- Structural breaks: [DATE if found]
- Serial correlation: [PASSED/FAILED]
- Heteroskedasticity: [PASSED/FAILED]

## Visualizations Generated
- [ ] threshold_dynamics.png
- [ ] price_conflict_series.png
- [ ] adjustment_speed_comparison.png

## Issues Encountered
[List any problems or unexpected results]

## Next Steps
- [ ] Extend to Rice and Sugar
- [ ] Analyze Government zone
- [ ] Run policy simulations
```

#### B. Update `CLAUDE.md` - Add Results Section
Add after the "Implementation Workflow" section:

```markdown
## Model Results Summary

### Week 5 Findings (Threshold VECM)
- **Conflict threshold**: [X] events/month (p < 0.05)
- **Market integration**: [X]x slower in high conflict
- **Half-life to equilibrium**:
  - Low conflict: [X] months
  - High conflict: [X] months
- **Model validation**: Track 1 and Track 2 show [strong/moderate/weak] agreement

### Key Takeaways
1. The 50-event threshold is [confirmed/revised to X]
2. Market integration is severely impaired above threshold
3. Policy intervention should target keeping conflict below [X] events

See `.claude/models/week5_results.md` for detailed results.
```

#### C. Update `.claude/project_memory.md`
Add to the development status:

```markdown
## Week 5 Model Results ([DATE])
- Threshold VECM: ✅ Implemented and tested
- Bayesian VECM: ✅ Implemented [and converged / with issues]
- Key finding: Threshold at [X] events is significant
- Model agreement: [X] correlation
- Diagnostics: [X/Y] tests passed
- Next: Multi-commodity and cross-zone analysis
```

### 7. Create Week 6 Sprint Plan

Create `.claude/models/week6_tasks.md`:

```markdown
# Week 6 Tasks: Extensions and Robustness

## Objectives
1. Extend analysis to multiple commodities
2. Compare across control zones
3. Run policy simulations
4. Conduct robustness checks

## Monday: Multi-Commodity Analysis
- [ ] Run models for Rice
- [ ] Run models for Sugar
- [ ] Compare thresholds across commodities
- [ ] Test parameter stability

## Tuesday: Cross-Zone Comparison
- [ ] Analyze Government-controlled markets
- [ ] Compare Houthi vs Government parameters
- [ ] Test for different thresholds by zone
- [ ] Examine border market effects

## Wednesday: Policy Simulations
- [ ] Exchange rate unification scenario
- [ ] Conflict reduction to <50 events
- [ ] Infrastructure investment effects
- [ ] Calculate welfare implications

## Thursday: Robustness Checks
- [ ] Alternative threshold variables
- [ ] Sample period sensitivity
- [ ] Leave-one-market-out
- [ ] Bootstrap confidence bands

## Friday: Synthesis
- [ ] Create summary tables
- [ ] Generate final visualizations
- [ ] Write technical appendix
- [ ] Prepare Week 7-8 plan
```

### 8. Troubleshooting Guide

If you encounter issues:

#### PyMC not installed:
```bash
pip install pymc arviz
# Or skip Track 1 and use Track 2 only
```

#### Memory errors:
- Reduce number of markets analyzed
- Use subset of data
- Close other applications

#### Convergence issues (Bayesian):
- Increase target_accept to 0.9
- Run more samples (4000)
- Simplify model structure

#### No cointegration found:
- Check data quality
- Try Gregory-Hansen test
- Consider structural breaks

### 9. Git Commit

After completing tasks:
```bash
git add -A
git commit -m "feat(models): complete Week 5 dual-track implementation

- Run threshold VECM with corrected bootstrap
- Implement simplified Bayesian regime-switching
- Confirm 50-event threshold significance
- Document results and diagnostics
- Model agreement: [X] correlation"

git push
```

## Expected Outcomes

By the end of this session, you should have:
1. ✅ Verified models work on actual Yemen data
2. ✅ Confirmed or revised the 50-event threshold
3. ✅ Compared Track 1 and Track 2 results
4. ✅ Updated all documentation with findings
5. ✅ Created Week 6 plan
6. ✅ Committed results to git

## Key Success Metrics
- Threshold test p-value < 0.05
- Model parameter correlation > 0.7
- Diagnostic tests mostly pass
- Clear policy implications identified

Remember: The goal is to validate the natural experiment at 50 conflict events and quantify market integration impairment.
