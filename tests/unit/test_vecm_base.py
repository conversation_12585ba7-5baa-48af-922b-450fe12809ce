"""Tests for VECM base classes and enhancements."""

import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch
from yemen_market.models.base import (
    VECMBase, VECMResults, ModelType,
    ThresholdModelMixin, SpatialModelMixin, PanelModelMixin
)


class TestVECMResults:
    """Test VECMResults dataclass."""
    
    def test_vecm_results_initialization(self):
        """Test VECMResults can be initialized with required fields."""
        alpha = np.array([[0.1, 0.2], [0.3, 0.4]])
        beta = np.array([[1.0, 0.5], [0.5, 1.0]])
        
        results = VECMResults(
            alpha=alpha,
            beta=beta,
            n_obs=100,
            n_coint=2,
            n_lags=2,
            converged=True
        )
        
        assert np.array_equal(results.alpha, alpha)
        assert np.array_equal(results.beta, beta)
        assert results.n_obs == 100
        assert results.n_coint == 2
        assert results.converged is True
    
    def test_vecm_results_optional_fields(self):
        """Test VECMResults optional fields have correct defaults."""
        results = VECMResults(
            alpha=np.array([]),
            beta=np.array([]),
            n_obs=0,
            n_coint=0,
            n_lags=1,
            converged=False
        )
        
        assert results.gamma is None
        assert results.threshold_value is None
        assert results.time_varying_params is None
        assert results.spatial_coefficients is None
        assert results.log_likelihood == 0.0


class TestVECMBase:
    """Test VECMBase class functionality."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample panel data for testing."""
        np.random.seed(42)
        dates = pd.date_range('2020-01-01', periods=100, freq='M')
        markets = ['Market_A', 'Market_B', 'Market_C']
        
        data = []
        for date in dates:
            for market in markets:
                data.append({
                    'date': date,
                    'market_name': market,
                    'price_usd': np.random.uniform(50, 150),
                    'conflict_intensity': np.random.uniform(0, 100)
                })
        
        return pd.DataFrame(data)
    
    @pytest.fixture
    def vecm_model(self):
        """Create a concrete VECM model for testing."""
        class ConcreteVECM(VECMBase):
            def fit(self, data, **kwargs):
                self.is_fitted = True
                return self
            
            def predict(self, steps=1, exog=None):
                return pd.DataFrame(np.random.randn(steps, 3))
            
            def _calculate_residuals(self):
                return pd.Series(np.random.randn(100))
        
        return ConcreteVECM(n_coint=1, n_lags=2)
    
    def test_vecm_base_initialization(self, vecm_model):
        """Test VECMBase initialization."""
        assert vecm_model.n_coint == 1
        assert vecm_model.n_lags == 2
        assert vecm_model.deterministic == 'ci'
        assert vecm_model.vecm_results is None
        assert vecm_model.name == "VECM"
    
    def test_prepare_data(self, vecm_model, sample_data):
        """Test data preparation for VECM."""
        prepared = vecm_model.prepare_data(
            sample_data,
            price_cols=['price_usd']
        )
        
        assert 'log_prices' in prepared
        assert 'dlogs' in prepared
        assert 'n_obs' in prepared
        assert 'n_vars' in prepared
        assert 'markets' in prepared
        assert 'dates' in prepared
        
        # Check dimensions
        assert prepared['n_vars'] == 3  # 3 markets
        assert prepared['n_obs'] == 99  # One less due to differencing
        
        # Check log transformation
        assert np.all(prepared['log_prices'] > 0)  # Log prices should be positive
        
        # Check differencing
        assert prepared['dlogs'].shape[0] == prepared['n_obs']
    
    @patch('yemen_market.models.base.coint_johansen')
    def test_test_cointegration(self, mock_johansen, vecm_model, sample_data):
        """Test cointegration testing."""
        # Mock Johansen test results
        mock_result = Mock()
        mock_result.lr1 = np.array([20.0, 10.0, 5.0])
        mock_result.cvt = np.array([[15.0, 18.0, 25.0],
                                   [8.0, 12.0, 18.0],
                                   [3.0, 6.0, 12.0]])
        mock_result.lr2 = np.array([15.0, 8.0, 3.0])
        mock_result.cvm = np.array([[12.0, 15.0, 20.0],
                                   [6.0, 10.0, 15.0],
                                   [2.0, 5.0, 10.0]])
        mock_result.eig = np.array([0.15, 0.08, 0.03])
        mock_result.evec = np.random.randn(3, 3)
        
        mock_johansen.return_value = mock_result
        
        prepared = vecm_model.prepare_data(sample_data, ['price_usd'])
        results = vecm_model.test_cointegration(prepared)
        
        assert 'trace_stats' in results
        assert 'eigen_stats' in results
        assert 'selected_rank' in results
        assert results['selected_rank'] >= 0
        assert results['selected_rank'] <= 3
    
    def test_calculate_error_correction_terms(self, vecm_model):
        """Test ECT calculation."""
        log_prices = np.random.randn(100, 3)
        beta = np.random.randn(3, 2)
        
        ect = vecm_model.calculate_error_correction_terms(log_prices, beta)
        
        assert ect.shape == (100, 2)
        assert np.allclose(ect, log_prices @ beta)
    
    def test_estimate_adjustment_speeds(self, vecm_model):
        """Test adjustment speed estimation."""
        dlogs = np.random.randn(100, 3)
        ect = np.random.randn(99, 2)
        
        results = vecm_model.estimate_adjustment_speeds(
            dlogs, ect, lags=2
        )
        
        assert 'alpha' in results
        assert 'alpha_se' in results
        assert results['alpha'].shape == (3, 2)
        assert results['alpha_se'].shape == (3, 2)
    
    def test_test_weak_exogeneity(self, vecm_model, sample_data):
        """Test weak exogeneity testing."""
        # Set up model with results
        vecm_model.is_fitted = True
        vecm_model.data = {'markets': ['Market_A', 'Market_B', 'Market_C']}
        vecm_model.vecm_results = VECMResults(
            alpha=np.array([[0.1, 0.2], [0.01, 0.02], [0.3, 0.4]]),
            alpha_se=np.array([[0.05, 0.05], [0.05, 0.05], [0.05, 0.05]]),
            beta=np.array([[1, 0], [0, 1], [0.5, 0.5]]),
            n_obs=100,
            n_params=10,
            n_coint=2,
            n_lags=2,
            converged=True
        )
        
        results = vecm_model.test_weak_exogeneity()
        
        assert isinstance(results, dict)
        assert 'Market_A' in results
        assert 'Market_B' in results
        assert 'Market_C' in results
        
        # Check Market_B has small alpha (likely weakly exogenous)
        assert 'is_weakly_exogenous' in results['Market_B']


class TestModelMixins:
    """Test model mixin functionality."""
    
    def test_threshold_model_mixin(self):
        """Test ThresholdModelMixin methods."""
        class ThresholdVECM(VECMBase, ThresholdModelMixin):
            def __init__(self):
                super().__init__()
                self.data = pd.DataFrame({
                    'price': np.random.randn(100),
                    'conflict': np.random.uniform(0, 100, 100)
                })
            
            def fit(self, data, **kwargs):
                return self
            
            def predict(self, steps=1, exog=None):
                return pd.DataFrame()
            
            def _calculate_residuals(self):
                return pd.Series()
        
        model = ThresholdVECM()
        
        # Test that mixin methods are available
        assert hasattr(model, 'estimate_threshold')
        assert hasattr(model, 'plot_threshold_search')
    
    def test_spatial_model_mixin(self):
        """Test SpatialModelMixin methods."""
        class SpatialVECM(VECMBase, SpatialModelMixin):
            def __init__(self):
                super().__init__()
                self.data = pd.DataFrame({
                    'market_name': ['A', 'B', 'C'],
                    'latitude': [15.0, 16.0, 17.0],
                    'longitude': [44.0, 45.0, 46.0]
                })
            
            def fit(self, data, **kwargs):
                return self
            
            def predict(self, steps=1, exog=None):
                return pd.DataFrame()
            
            def _calculate_residuals(self):
                return pd.Series(np.random.randn(100))
            
            def get_residuals(self):
                return self._calculate_residuals()
        
        model = SpatialVECM()
        
        # Test that mixin methods are available
        assert hasattr(model, 'calculate_spatial_weights')
        assert hasattr(model, 'test_spatial_autocorrelation')
    
    def test_panel_model_mixin(self):
        """Test PanelModelMixin methods."""
        class PanelVECM(VECMBase, PanelModelMixin):
            def __init__(self):
                super().__init__()
                self.data = pd.DataFrame()
                self.results = Mock()
            
            def fit(self, data, **kwargs):
                return self
            
            def predict(self, steps=1, exog=None):
                return pd.DataFrame()
            
            def _calculate_residuals(self):
                return pd.Series()
        
        model = PanelVECM()
        
        # Test that mixin methods are available
        assert hasattr(model, 'test_poolability')
        assert hasattr(model, 'hausman_test')


class TestModelType:
    """Test ModelType enum."""
    
    def test_model_type_values(self):
        """Test ModelType enum has correct values."""
        assert ModelType.VECM.value == "vecm"
        assert ModelType.THRESHOLD_VECM.value == "threshold_vecm"
        assert ModelType.TVP_VECM.value == "tvp_vecm"
        assert ModelType.SPATIAL_VECM.value == "spatial_vecm"
        assert ModelType.BAYESIAN_VECM.value == "bayesian_vecm"
    
    def test_model_type_members(self):
        """Test all ModelType members are accessible."""
        expected_members = [
            'VECM', 'THRESHOLD_VECM', 'TVP_VECM', 
            'SPATIAL_VECM', 'BAYESIAN_VECM'
        ]
        
        for member in expected_members:
            assert hasattr(ModelType, member)