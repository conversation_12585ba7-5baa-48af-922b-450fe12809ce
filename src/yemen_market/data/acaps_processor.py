"""ACAPS areas of control processor for Yemen territorial data.

This module processes ACAPS bi-weekly areas of control data, tracking territorial
changes between Houthi, Government, and Contested zones at the district level.
"""

import zipfile
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import geopandas as gpd
import pandas as pd
import numpy as np

from ..config.settings import RAW_DATA_DIR, INTERIM_DATA_DIR, PROCESSED_DATA_DIR
from ..utils.logging import (
    bind, context, timer, progress, log_data_shape,
    info, warning, error, debug
)


class ACAPSProcessor:
    """Process ACAPS areas of control data for Yemen territorial analysis.
    
    This processor handles:
    - Extracting control zone data from ACAPS downloads
    - Processing bi-weekly territorial control updates
    - Creating time series of control changes by district
    - Mapping districts to control zones over time
    - Aligning with WFP market data temporal resolution
    - Handling transitions between control zones
    
    Attributes:
        acaps_dir: Directory containing ACAPS raw data
        output_dir: Directory for processed control zone data
        control_zones: Valid control zone categories
    """
    
    # Control zone categories based on actual ACAPS data
    CONTROL_ZONES = {
        'DFA': ['DFA', 'De-facto Authority', 'Ho<PERSON>i', '<PERSON><PERSON>', 'SPC', 'Sana\'a'],
        'IRG': ['IRG', 'Government', 'Internationally Recognized Government', 'Hadi', 'Aden', 'GOY'],
        'STC': ['STC', 'Southern Transitional Council'],
        'AQAP': ['AQAP', 'AQAP Presence', 'Al-Qaeda'],
        'contested': ['Contested', 'Disputed', 'Mixed', 'Unclear', 'DFA,IRG', 'IRG,STC'],
        'other': ['Other', 'Local', 'Tribal', 'Unknown']
    }
    
    # Standard column mappings for ACAPS data
    COLUMN_MAPPINGS = {
        'ADM0_EN': 'country',
        'ADM1_EN': 'governorate',
        'ADM2_EN': 'district',
        'ADM1_PCODE': 'governorate_pcode',
        'ADM2_PCODE': 'district_pcode',
        'Control': 'control_zone',
        'control': 'control_zone',
        'Actor': 'control_zone',
        'actor': 'control_zone',
        'Controller': 'control_zone',
        'controller': 'control_zone'
    }
    
    def __init__(self, acaps_dir: Optional[Path] = None, 
                 output_dir: Optional[Path] = None):
        """Initialize ACAPS processor.
        
        Args:
            acaps_dir: Directory containing ACAPS raw data
            output_dir: Directory for processed output
        """
        self.acaps_dir = acaps_dir or RAW_DATA_DIR / "acaps"
        self.output_dir = output_dir or PROCESSED_DATA_DIR / "control_zones"
        self.interim_dir = INTERIM_DATA_DIR / "acaps"
        
        # Create directories if they don't exist
        self.acaps_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.interim_dir.mkdir(parents=True, exist_ok=True)
        
        info("ACAPS processor initialized", data_dir=str(self.acaps_dir))
    
    def extract_control_data(self, zip_path: Path) -> gpd.GeoDataFrame:
        """Extract control zone data from ACAPS zip file.
        
        ACAPS files have nested structure:
        - Main ZIP contains nested ZIP with shapefiles
        - Shapefiles are split by control zone (DFA.shp, IRG.shp, STC.shp, etc.)
        - Need to combine all shapefiles into single dataset
        
        Args:
            zip_path: Path to ACAPS zip file
            
        Returns:
            GeoDataFrame with control zone data
            
        Raises:
            ValueError: If required files not found in archive
        """
        with timer(f"extract_{zip_path.stem}"):
            info("Extracting control data", file=zip_path.name)
        
        # Extract to temporary directory
        extract_dir = self.interim_dir / f"temp_{zip_path.stem}"
        extract_dir.mkdir(exist_ok=True)
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as main_zip:
                main_zip.extractall(extract_dir)
            
            # Find nested ZIP files
            nested_zips = list(extract_dir.rglob("*.zip"))
            
            # Extract nested ZIPs
            for nested_zip in nested_zips:
                if 'shapefile' in nested_zip.name.lower():
                    nested_dir = nested_zip.parent / nested_zip.stem
                    nested_dir.mkdir(exist_ok=True)
                    with zipfile.ZipFile(nested_zip, 'r') as nz:
                        nz.extractall(nested_dir)
            
            # Find all shapefiles
            shp_files = list(extract_dir.rglob("*.shp"))
            
            if not shp_files:
                # Try Excel format
                excel_files = list(extract_dir.rglob("*.xlsx"))
                if excel_files:
                    return self._extract_from_excel_file(excel_files[0])
                raise ValueError(f"No shapefile or Excel file found in {zip_path}")
            
            # Process each shapefile and combine
            all_gdfs = []
            
            for shp_file in shp_files:
                try:
                    gdf = gpd.read_file(shp_file)
                    
                    # Skip empty or very small shapefiles (like AQAP presence markers)
                    if len(gdf) == 0:
                        continue
                    
                    # Determine control zone from filename or data
                    control_zone = self._determine_control_zone(shp_file, gdf)
                    
                    # Add control zone if not present
                    if 'irg_or_dfa' not in gdf.columns:
                        gdf['irg_or_dfa'] = control_zone
                    
                    all_gdfs.append(gdf)
                    debug(f"Loaded {shp_file.name}", shape=gdf.shape, zone=control_zone)
                    
                except Exception as e:
                    warning(f"Error reading {shp_file.name}", error=str(e))
                    continue
            
            if not all_gdfs:
                raise ValueError(f"No valid shapefiles found in {zip_path}")
            
            # Combine all GeoDataFrames
            combined_gdf = pd.concat(all_gdfs, ignore_index=True)
            
            # Ensure it's a GeoDataFrame
            if not isinstance(combined_gdf, gpd.GeoDataFrame):
                combined_gdf = gpd.GeoDataFrame(combined_gdf)
            
            # Standardize column names
            combined_gdf = self._standardize_columns(combined_gdf)
            
            # Extract date from filename
            date_str = self._extract_date_from_filename(zip_path.name)
            if date_str:
                combined_gdf['date'] = pd.to_datetime(date_str)
            
            log_data_shape("combined_control_zones", combined_gdf)
            
            return combined_gdf
            
        finally:
            # Clean up temporary directory
            import shutil
            if extract_dir.exists():
                shutil.rmtree(extract_dir)
    
    def _determine_control_zone(self, shp_file: Path, gdf: gpd.GeoDataFrame) -> str:
        """Determine control zone from filename or data.
        
        Args:
            shp_file: Path to shapefile
            gdf: GeoDataFrame with shapefile data
            
        Returns:
            Control zone name
        """
        filename = shp_file.stem.lower()
        
        # Check filename
        if 'dfa' in filename or 'de-facto' in filename:
            return 'DFA'
        elif 'irg' in filename:
            return 'IRG'
        elif 'stc' in filename:
            return 'STC'
        elif 'aqap' in filename:
            return 'AQAP'
        
        # Check data columns
        if 'irg_or_dfa' in gdf.columns and not gdf['irg_or_dfa'].isna().all():
            return gdf['irg_or_dfa'].mode()[0]
        
        return 'Unknown'
    
    def _extract_from_excel_file(self, excel_path: Path) -> pd.DataFrame:
        """Extract control data from Excel file.
        
        Args:
            excel_path: Path to Excel file
            
        Returns:
            DataFrame with control zone data
        """
        df = pd.read_excel(excel_path, engine='openpyxl')
        
        # Standardize column names
        df = self._standardize_columns(df)
        
        return df
    
    def _standardize_columns(self, df: Union[pd.DataFrame, gpd.GeoDataFrame]
                           ) -> Union[pd.DataFrame, gpd.GeoDataFrame]:
        """Standardize column names across different ACAPS formats.
        
        Args:
            df: Input dataframe
            
        Returns:
            DataFrame with standardized column names
        """
        # Update column mappings for actual ACAPS structure
        actual_mappings = {
            'ADM0_EN': 'country',
            'ADM1_EN': 'governorate', 
            'ADM2_EN': 'district',
            'adm1_name': 'governorate',
            'adm2_name': 'district',
            'ADM1_PCODE': 'governorate_pcode',
            'ADM2_PCODE': 'district_pcode',
            'adm2_pco_1': 'district_pcode',
            'actors': 'actors_detail',  # Detailed actor info
            'irg_or_dfa': 'control_zone',  # Primary control zone
            'Actors_cod': 'actors_code'
        }
        
        # Rename columns based on mapping, but avoid duplicates
        rename_dict = {}
        existing_new_names = set()
        
        for col in df.columns:
            for old_name, new_name in actual_mappings.items():
                if col == old_name:  # Exact match for ACAPS columns
                    # Check if we already have this column name
                    if new_name not in df.columns and new_name not in existing_new_names:
                        rename_dict[col] = new_name
                        existing_new_names.add(new_name)
                    break
        
        if rename_dict:
            df = df.rename(columns=rename_dict)
        
        # Ensure we have control_zone column
        if 'control_zone' not in df.columns and 'irg_or_dfa' in df.columns:
            df['control_zone'] = df['irg_or_dfa']
        
        # Log available columns
        debug("Standardized columns", 
              original=list(rename_dict.keys()),
              renamed=list(rename_dict.values()),
              all_columns=list(df.columns))
        
        return df
    
    def _extract_date_from_filename(self, filename: str) -> Optional[str]:
        """Extract date from ACAPS filename.
        
        Args:
            filename: ACAPS filename (e.g., '20240513 Yemen Analysis Hub - Areas of control.zip')
            
        Returns:
            Date string in YYYY-MM-DD format or None
        """
        import re
        
        # Remove 'b' suffix if present (e.g., '20241024b' -> '20241024')
        filename = filename.replace('b ', ' ').replace('b_', '_')
        
        # Try different date patterns
        patterns = [
            r'(\d{8})',  # YYYYMMDD
            r'(\d{4})-?(\d{2})-?(\d{2})',  # YYYY-MM-DD or YYYY MM DD
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename)
            if match:
                if len(match.groups()) == 1:
                    # YYYYMMDD format
                    date_str = match.group(1)
                    return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                else:
                    # YYYY-MM-DD format
                    return f"{match.group(1)}-{match.group(2)}-{match.group(3)}"
        
        return None
    
    def standardize_control_zones(self, control_zone: str) -> str:
        """Standardize control zone names to consistent categories.
        
        Args:
            control_zone: Raw control zone name
            
        Returns:
            Standardized control zone category
        """
        if pd.isna(control_zone):
            return 'unknown'
        
        zone_str = str(control_zone).strip()
        
        # Direct matches for primary zones
        if zone_str in ['DFA', 'IRG', 'STC', 'AQAP']:
            return zone_str
        
        # Check for mixed control (e.g., "DFA,IRG" or "IRG,STC")
        if ',' in zone_str or 'mixed' in zone_str.lower():
            return 'contested'
        
        # Check variations
        zone_lower = zone_str.lower()
        for category, variations in self.CONTROL_ZONES.items():
            for variation in variations:
                if variation.lower() == zone_lower or variation.lower() in zone_lower:
                    return category
        
        # Default handling
        if zone_str and zone_str != 'Unknown':
            debug(f"Unrecognized control zone: {zone_str}")
            
        return 'other'
    
    def process_all_files(self, start_date: Optional[str] = None,
                         end_date: Optional[str] = None) -> pd.DataFrame:
        """Process all ACAPS control zone files in directory.
        
        Args:
            start_date: Start date for filtering (YYYY-MM-DD)
            end_date: End date for filtering (YYYY-MM-DD)
            
        Returns:
            Combined DataFrame with all control zone data
        """
        # Find all ACAPS zip files
        zip_files = list(self.acaps_dir.glob("*[Aa]reas*[Cc]ontrol*.zip"))
        zip_files.extend(list(self.acaps_dir.glob("*Yemen*Analysis*.zip")))
        zip_files.extend(list(self.acaps_dir.glob("*ACAPS*.zip")))
        zip_files = list(set(zip_files))  # Remove duplicates
        
        if not zip_files:
            warning("No ACAPS control files found", directory=str(self.acaps_dir))
            return pd.DataFrame()
        
        info("Found ACAPS control files", count=len(zip_files))
        
        # Process each file
        all_data = []
        with progress("Processing ACAPS files", total=len(zip_files)) as update:
            for zip_path in sorted(zip_files):
                try:
                    # Extract date from filename
                    date_str = self._extract_date_from_filename(zip_path.name)
                    if date_str:
                        file_date = pd.to_datetime(date_str)
                        
                        # Filter by date range if specified
                        if start_date and file_date < pd.to_datetime(start_date):
                            continue
                        if end_date and file_date > pd.to_datetime(end_date):
                            continue
                    
                    # Process file
                    data = self.extract_control_data(zip_path)
                    
                    # Add source file info
                    data['source_file'] = zip_path.name
                    
                    all_data.append(data)
                    update(1, f"Processed {zip_path.name}")
                    
                except zipfile.BadZipFile:
                    warning(f"Skipping corrupted ZIP file: {zip_path.name}")
                    continue
                except Exception as e:
                    error("Error processing file", file=zip_path.name, error=str(e))
                    continue
        
        if not all_data:
            warning("No data extracted from ACAPS files")
            return pd.DataFrame()
        
        # Combine all data
        if isinstance(all_data[0], gpd.GeoDataFrame):
            combined = gpd.GeoDataFrame(pd.concat(all_data, ignore_index=True))
        else:
            combined = pd.concat(all_data, ignore_index=True)
        
        # Standardize control zones
        if 'control_zone' in combined.columns:
            combined['control_zone_raw'] = combined['control_zone']
            combined['control_zone'] = combined['control_zone_raw'].apply(
                self.standardize_control_zones
            )
        
        # Sort by date and district
        if 'date' in combined.columns:
            combined = combined.sort_values(['date', 'governorate', 'district'])
        
        log_data_shape("combined_control_data", combined)
        info(
            "Processed control zone records",
            records=len(combined),
            governorates=combined['governorate'].nunique() if 'governorate' in combined.columns else 0,
            districts=combined['district'].nunique() if 'district' in combined.columns else 0
        )
        
        return combined
    
    def create_control_time_series(self, control_data: pd.DataFrame) -> pd.DataFrame:
        """Create time series of control changes by district.
        
        Args:
            control_data: Combined control zone data
            
        Returns:
            Time series DataFrame with control changes
        """
        if control_data.empty:
            return pd.DataFrame()
        
        # Ensure we have required columns
        required_cols = ['date', 'governorate', 'district', 'control_zone']
        if not all(col in control_data.columns for col in required_cols):
            error("Missing required columns for time series", 
                  available=list(control_data.columns), required=required_cols)
            return pd.DataFrame()
        
        # Create time series
        time_series = control_data[required_cols].copy()
        
        # Add district identifier
        time_series['district_id'] = (time_series['governorate'] + '_' + 
                                     time_series['district'])
        
        # Pivot to wide format
        ts_wide = time_series.pivot_table(
            index='date',
            columns='district_id', 
            values='control_zone',
            aggfunc='first'
        )
        
        # Forward fill to handle missing dates
        ts_wide = ts_wide.fillna(method='ffill')
        
        # Melt back to long format
        ts_long = ts_wide.reset_index().melt(
            id_vars='date',
            var_name='district_id',
            value_name='control_zone'
        )
        
        # Split district_id back to governorate and district
        ts_long[['governorate', 'district']] = ts_long['district_id'].str.split(
            '_', n=1, expand=True
        )
        
        # Calculate control changes
        ts_long = ts_long.sort_values(['district_id', 'date'])
        ts_long['previous_control'] = ts_long.groupby('district_id')['control_zone'].shift(1)
        ts_long['control_changed'] = ts_long['control_zone'] != ts_long['previous_control']
        
        # Add transition type
        ts_long['transition_type'] = ts_long.apply(
            lambda x: f"{x['previous_control']}_to_{x['control_zone']}" 
            if x['control_changed'] and pd.notna(x['previous_control']) else None,
            axis=1
        )
        
        return ts_long
    
    def align_to_monthly(self, time_series: pd.DataFrame) -> pd.DataFrame:
        """Align bi-weekly control data to monthly frequency.
        
        Args:
            time_series: Time series data with bi-weekly updates
            
        Returns:
            Monthly aligned control zone data
        """
        if time_series.empty:
            return pd.DataFrame()
        
        # Convert date to period for monthly grouping
        time_series['month'] = pd.to_datetime(time_series['date']).dt.to_period('M')
        
        # For each district-month, take the most recent control zone
        monthly = time_series.sort_values('date').groupby(
            ['governorate', 'district', 'month']
        ).agg({
            'control_zone': 'last',
            'control_changed': 'any',  # True if any change in month
            'date': 'last'  # Keep last date for reference
        }).reset_index()
        
        # Convert period back to timestamp (first day of month)
        monthly['date'] = monthly['month'].dt.to_timestamp()
        monthly = monthly.drop('month', axis=1)
        
        # Calculate monthly stability score (% of time in same control)
        stability = time_series.groupby(
            ['governorate', 'district', time_series['date'].dt.to_period('M')]
        ).agg({
            'control_zone': lambda x: x.mode()[0] if not x.empty else None,
            'control_changed': 'sum'
        }).reset_index()
        
        stability.columns = ['governorate', 'district', 'month', 
                           'dominant_control', 'n_changes']
        stability['date'] = stability['month'].dt.to_timestamp()
        
        # Merge stability info
        monthly = monthly.merge(
            stability[['governorate', 'district', 'date', 'n_changes']],
            on=['governorate', 'district', 'date'],
            how='left'
        )
        
        return monthly
    
    def save_processed_data(self, control_data: pd.DataFrame,
                          time_series: pd.DataFrame,
                          monthly_data: pd.DataFrame) -> Dict[str, Path]:
        """Save processed control zone data to disk.
        
        Args:
            control_data: Raw combined control data
            time_series: Time series of control changes
            monthly_data: Monthly aligned control data
            
        Returns:
            Dictionary of saved file paths
        """
        saved_files = {}
        
        # Save raw combined data
        if not control_data.empty:
            raw_path = self.output_dir / "acaps_control_zones_raw.parquet"
            control_data.to_parquet(raw_path, index=False)
            saved_files['raw'] = raw_path
            info("Saved raw control data", path=str(raw_path), size=len(control_data))
            
            # Also save as CSV for easier inspection
            csv_path = self.output_dir / "acaps_control_zones_raw.csv"
            control_data.to_csv(csv_path, index=False)
            saved_files['raw_csv'] = csv_path
        
        # Save time series
        if not time_series.empty:
            ts_path = self.output_dir / "control_zone_time_series.parquet"
            time_series.to_parquet(ts_path, index=False)
            saved_files['time_series'] = ts_path
            info("Saved control time series", path=str(ts_path), size=len(time_series))
        
        # Save monthly data
        if not monthly_data.empty:
            monthly_path = self.output_dir / "control_zones_monthly.parquet"
            monthly_data.to_parquet(monthly_path, index=False)
            saved_files['monthly'] = monthly_path
            info("Saved monthly control data", path=str(monthly_path), size=len(monthly_data))
            
            # Also save as CSV
            monthly_csv = self.output_dir / "control_zones_monthly.csv"
            monthly_data.to_csv(monthly_csv, index=False)
            saved_files['monthly_csv'] = monthly_csv
        
        # Save metadata
        metadata = {
            'processed_date': datetime.now().isoformat(),
            'n_files_processed': len(control_data['source_file'].unique()) if 'source_file' in control_data.columns else 0,
            'date_range': {
                'start': str(control_data['date'].min()) if 'date' in control_data.columns else None,
                'end': str(control_data['date'].max()) if 'date' in control_data.columns else None
            },
            'n_districts': len(control_data[['governorate', 'district']].drop_duplicates()) if not control_data.empty else 0,
            'control_zones': list(control_data['control_zone'].unique()) if 'control_zone' in control_data.columns else []
        }
        
        import json
        metadata_path = self.output_dir / "processing_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        saved_files['metadata'] = metadata_path
        
        return saved_files
    
    def generate_summary_stats(self, monthly_data: pd.DataFrame) -> pd.DataFrame:
        """Generate summary statistics for control zone data.
        
        Args:
            monthly_data: Monthly control zone data
            
        Returns:
            Summary statistics DataFrame
        """
        if monthly_data.empty:
            return pd.DataFrame()
        
        # Overall control zone distribution
        control_dist = monthly_data['control_zone'].value_counts()
        
        # Changes over time
        changes_by_month = monthly_data.groupby('date')['control_changed'].sum()
        
        # Districts by control zone over time
        control_by_month = monthly_data.groupby(['date', 'control_zone']).size().unstack(fill_value=0)
        
        # Stability by governorate
        stability_by_gov = monthly_data.groupby('governorate').agg({
            'control_changed': 'mean',
            'n_changes': 'mean'
        })
        
        # Create summary
        summary = pd.DataFrame({
            'metric': ['total_districts', 'total_months', 'avg_changes_per_month'],
            'value': [
                monthly_data[['governorate', 'district']].drop_duplicates().shape[0],
                monthly_data['date'].nunique(),
                monthly_data.groupby('date')['control_changed'].sum().mean()
            ]
        })
        
        info("Generated summary statistics for control zones")
        
        return summary