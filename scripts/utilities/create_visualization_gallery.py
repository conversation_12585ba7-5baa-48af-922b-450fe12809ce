#!/usr/bin/env python3
"""Create a visualization gallery from analysis outputs.

This script generates a gallery of key visualizations from the analysis,
organizing them by category with descriptions.
"""

import sys
from pathlib import Path
import shutil
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.utils.logging import info, warning, error, bind, timer, progress
from yemen_market.visualization.price_dynamics import PriceDynamicsVisualizer

# Set up logging
bind(module=__name__)


def create_gallery_structure(gallery_dir: Path):
    """Create directory structure for the gallery."""
    subdirs = [
        "data_quality",
        "price_patterns", 
        "spatial_analysis",
        "conflict_dynamics",
        "market_integration"
    ]
    
    for subdir in subdirs:
        (gallery_dir / subdir).mkdir(parents=True, exist_ok=True)
    
    info(f"✅ Created gallery structure with {len(subdirs)} categories")
    return subdirs


def generate_sample_visualizations(gallery_dir: Path):
    """Generate sample visualizations using the analysis data."""
    try:
        from yemen_market.data.wfp_processor import WFPProcessor
        import pandas as pd
        import matplotlib.pyplot as plt
        import seaborn as sns
        
        info("Generating sample visualizations...")
        
        # Set style
        plt.style.use('seaborn-v0_8-darkgrid')
        sns.set_palette("husl")
        
        # Load some data for visualization
        smart_panel_path = project_root / "data" / "processed" / "wfp_smart_panel.parquet"
        if smart_panel_path.exists():
            data = pd.read_parquet(smart_panel_path)
            
            # 1. Price coverage heatmap
            fig, ax = plt.subplots(figsize=(12, 8))
            coverage_pivot = data.groupby(['commodity', 'governorate'])['price_usd'].apply(
                lambda x: x.notna().sum() / len(x) * 100
            ).unstack(fill_value=0)
            
            sns.heatmap(coverage_pivot, cmap='YlOrRd', annot=True, fmt='.0f', ax=ax)
            ax.set_title('Price Coverage by Commodity and Governorate (%)', fontsize=16)
            plt.tight_layout()
            fig.savefig(gallery_dir / 'data_quality' / 'price_coverage_heatmap.png', dpi=300)
            plt.close()
            info("✅ Created price coverage heatmap")
            
            # 2. Time series of price indices
            fig, ax = plt.subplots(figsize=(14, 8))
            key_commodities = ['Wheat', 'Rice (Imported)', 'Sugar', 'Oil (Vegetable)']
            
            for commodity in key_commodities:
                if commodity in data['commodity'].values:
                    comm_data = data[data['commodity'] == commodity].copy()
                    monthly_avg = comm_data.groupby('date')['price_usd'].mean()
                    if len(monthly_avg) > 0:
                        ax.plot(monthly_avg.index, monthly_avg.values, label=commodity, linewidth=2)
            
            ax.set_xlabel('Date', fontsize=12)
            ax.set_ylabel('Average Price (USD)', fontsize=12)
            ax.set_title('Price Trends for Key Commodities', fontsize=16)
            ax.legend(loc='best')
            plt.xticks(rotation=45)
            plt.tight_layout()
            fig.savefig(gallery_dir / 'price_patterns' / 'commodity_price_trends.png', dpi=300)
            plt.close()
            info("✅ Created price trends chart")
            
            # 3. Market distribution map placeholder
            fig, ax = plt.subplots(figsize=(10, 10))
            ax.text(0.5, 0.5, 'Market Distribution Map\n\n(Generated from spatial analysis notebook)', 
                   ha='center', va='center', fontsize=20, transform=ax.transAxes)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
            fig.savefig(gallery_dir / 'spatial_analysis' / 'market_distribution_map.png', dpi=300)
            plt.close()
            info("✅ Created market distribution placeholder")
            
        else:
            warning("Smart panel data not found, creating placeholder visualizations")
            
    except Exception as e:
        error(f"Error generating visualizations: {e}")


def create_gallery_index(gallery_dir: Path, categories: list):
    """Create an index.html file for the visualization gallery."""
    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yemen Market Integration - Visualization Gallery</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        h1 {{
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #555;
            margin-top: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }}
        .category {{
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .image-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }}
        .image-item {{
            text-align: center;
        }}
        .image-item img {{
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }}
        .image-item p {{
            margin-top: 10px;
            color: #666;
            font-size: 14px;
        }}
        .metadata {{
            text-align: center;
            color: #888;
            font-size: 12px;
            margin-top: 40px;
        }}
    </style>
</head>
<body>
    <h1>Yemen Market Integration - Visualization Gallery</h1>
    
    <p style="text-align: center; color: #666;">
        Key visualizations from the exploratory data analysis phase
    </p>
"""
    
    category_descriptions = {
        "data_quality": "Data coverage, missing values, and quality metrics",
        "price_patterns": "Price trends, volatility, and seasonal patterns",
        "spatial_analysis": "Geographic distribution and spatial relationships",
        "conflict_dynamics": "Conflict intensity and its relationship with markets",
        "market_integration": "Market connectivity and price transmission"
    }
    
    for category in categories:
        html_content += f"""
    <div class="category">
        <h2>{category.replace('_', ' ').title()}</h2>
        <p>{category_descriptions.get(category, 'Analysis visualizations')}</p>
        
        <div class="image-grid">
"""
        
        # Find images in this category
        category_dir = gallery_dir / category
        images = list(category_dir.glob("*.png")) + list(category_dir.glob("*.jpg"))
        
        if images:
            for img in sorted(images):
                img_title = img.stem.replace('_', ' ').title()
                html_content += f"""
            <div class="image-item">
                <img src="{category}/{img.name}" alt="{img_title}">
                <p>{img_title}</p>
            </div>
"""
        else:
            html_content += """
            <div class="image-item">
                <p style="color: #999;">No visualizations yet in this category</p>
            </div>
"""
        
        html_content += """
        </div>
    </div>
"""
    
    html_content += f"""
    <div class="metadata">
        <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>Yemen Market Integration Analysis - EDA Phase</p>
    </div>
</body>
</html>
"""
    
    index_path = gallery_dir / "index.html"
    with open(index_path, 'w') as f:
        f.write(html_content)
    
    info(f"✅ Created gallery index: {index_path}")


def create_readme(gallery_dir: Path):
    """Create a README file for the gallery."""
    readme_content = """# Visualization Gallery

This directory contains key visualizations from the Yemen market integration analysis.

## Categories

- **Data Quality**: Coverage analysis, missing data patterns, and data validation
- **Price Patterns**: Time series, trends, volatility, and structural breaks
- **Spatial Analysis**: Geographic patterns, spatial autocorrelation, and clustering
- **Conflict Dynamics**: Conflict intensity and its impact on markets
- **Market Integration**: Price transmission, market connectivity, and threshold effects

## Viewing the Gallery

Open `index.html` in a web browser to view the interactive gallery.

## Adding Visualizations

To add new visualizations:
1. Save the image file (PNG or JPG) in the appropriate category folder
2. Use descriptive filenames with underscores (e.g., `price_volatility_by_zone.png`)
3. Re-run the gallery creation script to update the index

## Source Notebooks

These visualizations are generated from:
- `notebooks/01-data-validation.ipynb`
- `notebooks/02-price-patterns.ipynb`
- `notebooks/03-spatial-analysis.ipynb`

Generated on: {date}
""".format(date=datetime.now().strftime('%Y-%m-%d'))
    
    readme_path = gallery_dir / "README.md"
    with open(readme_path, 'w') as f:
        f.write(readme_content)
    
    info(f"✅ Created gallery README: {readme_path}")


def main():
    """Create the visualization gallery."""
    info("=" * 60)
    info("Creating Visualization Gallery")
    info("=" * 60)
    
    # Create gallery directory
    gallery_dir = project_root / "reports" / "visualization_gallery"
    
    if gallery_dir.exists():
        warning(f"Gallery directory already exists: {gallery_dir}")
        response = input("Overwrite? (y/n): ")
        if response.lower() != 'y':
            info("Gallery creation cancelled")
            return 0
        shutil.rmtree(gallery_dir)
    
    gallery_dir.mkdir(parents=True, exist_ok=True)
    
    with timer("gallery_creation"):
        # Create structure
        categories = create_gallery_structure(gallery_dir)
        
        # Generate sample visualizations
        generate_sample_visualizations(gallery_dir)
        
        # Copy any existing visualizations from notebooks/figures
        figures_dir = project_root / "reports" / "figures"
        if figures_dir.exists():
            info(f"Copying existing figures from {figures_dir}")
            for fig in figures_dir.glob("*.png"):
                # Try to categorize based on filename
                if "coverage" in fig.name or "missing" in fig.name:
                    dest = gallery_dir / "data_quality" / fig.name
                elif "price" in fig.name or "trend" in fig.name:
                    dest = gallery_dir / "price_patterns" / fig.name
                elif "spatial" in fig.name or "map" in fig.name:
                    dest = gallery_dir / "spatial_analysis" / fig.name
                elif "conflict" in fig.name:
                    dest = gallery_dir / "conflict_dynamics" / fig.name
                else:
                    dest = gallery_dir / "market_integration" / fig.name
                
                shutil.copy2(fig, dest)
                info(f"  Copied: {fig.name}")
        
        # Create gallery index
        create_gallery_index(gallery_dir, categories)
        
        # Create README
        create_readme(gallery_dir)
    
    info("\n" + "=" * 60)
    info("✅ Visualization gallery created successfully!")
    info(f"Location: {gallery_dir}")
    info(f"View gallery: Open {gallery_dir}/index.html in a web browser")
    
    return 0


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        info("\nGallery creation interrupted by user")
        sys.exit(1)
    except Exception as e:
        error(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)