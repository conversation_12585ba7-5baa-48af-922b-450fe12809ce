"""HDX Client for downloading Yemen market data from Humanitarian Data Exchange.

This module provides authenticated access to HDX datasets, particularly WFP food
prices and other humanitarian data for Yemen market integration analysis.
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Union
from urllib.parse import urljoin

import pandas as pd
import requests
from hdx.api.configuration import Configuration
from hdx.data.dataset import Dataset
from hdx.data.resource import Resource

from ..config.settings import HDX_CONFIG, RAW_DATA_DIR
from ..utils.logging import (
    bind, context, timer, progress, log_data_shape,
    info, warning, error, debug, exception
)


class HDXClient:
    """Client for downloading and caching HDX humanitarian data.
    
    This client handles authentication, rate limiting, and caching for HDX API
    requests. It focuses on retrieving WFP food price data and related datasets
    for Yemen market analysis.
    
    Attributes:
        config: HDX API configuration
        cache_dir: Directory for caching downloaded data
        cache_days: Number of days to keep cached data
        rate_limit_delay: Seconds to wait between API requests
    """
    
    BASE_URL = "https://data.humdata.org/api/3/"
    
    # Key dataset IDs for Yemen market analysis
    DATASETS = {
        "wfp_food_prices": "wfp-food-prices-for-yemen",
        "admin_boundaries": "cod-ab-yem",
        "population": "yemen-population-data",
        "acaps_access": "yemen-access-constraints",
        "acaps_control": "yemen-areas-of-control",
        "acled_conflict": "423a7d11-cc86-4226-8a77-4bbbc51371c4"  # ACLED Yemen conflict data
    }
    
    def __init__(
        self, 
        cache_dir: Optional[Path] = None,
        cache_days: Optional[int] = None,
        rate_limit_delay: float = 0.5
    ):
        """Initialize HDX client with configuration.
        
        Args:
            cache_dir: Directory for caching data. Defaults to RAW_DATA_DIR/hdx
            cache_days: Days to keep cached data. Defaults to HDX_CONFIG value
            rate_limit_delay: Seconds between API requests to avoid rate limits
        """
        self.cache_dir = cache_dir or RAW_DATA_DIR / "hdx"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.cache_days = cache_days or HDX_CONFIG.get("cache_days", 7)
        self.rate_limit_delay = rate_limit_delay
        
        # Initialize HDX configuration (check if already exists)
        try:
            Configuration.create(
                hdx_site=HDX_CONFIG.get("site", "prod"),
                user_agent=HDX_CONFIG.get("user_agent", "YemenMarketAnalysis"),
                hdx_read_only=True
            )
        except Exception as e:
            if "Configuration already created" not in str(e):
                raise
        
        info("HDX client initialized", cache_dir=str(self.cache_dir), cache_days=self.cache_days)
    
    def _get_cache_path(self, dataset_id: str, resource_name: str) -> Path:
        """Generate cache file path for a resource.
        
        Args:
            dataset_id: HDX dataset identifier
            resource_name: Name of the resource file
            
        Returns:
            Path to cache file
        """
        return self.cache_dir / dataset_id / resource_name
    
    def _is_cache_valid(self, cache_path: Path) -> bool:
        """Check if cached file is still valid.
        
        Args:
            cache_path: Path to cached file
            
        Returns:
            True if cache is valid, False otherwise
        """
        if not cache_path.exists():
            return False
        
        cache_time = datetime.fromtimestamp(cache_path.stat().st_mtime)
        return datetime.now() - cache_time < timedelta(days=self.cache_days)
    
    def _download_resource(
        self, 
        resource: Resource, 
        cache_path: Path
    ) -> Optional[Path]:
        """Download a resource from HDX.
        
        Args:
            resource: HDX Resource object
            cache_path: Path to save downloaded file
            
        Returns:
            Path to downloaded file or None if failed
        """
        try:
            cache_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Apply rate limiting
            time.sleep(self.rate_limit_delay)
            
            # Download the resource using HDX API
            url, downloaded_path = resource.download(folder=str(cache_path.parent))
            info("Downloaded resource", url=url, path=str(downloaded_path))
            
            # Move to correct cache path if needed
            if Path(downloaded_path) != cache_path:
                Path(downloaded_path).rename(cache_path)
            
            info("Resource cached", path=str(cache_path))
            return cache_path
            
        except Exception as e:
            error("Failed to download resource", error=str(e))
            return None
    
    def get_dataset(self, dataset_id: str) -> Optional[Dataset]:
        """Retrieve an HDX dataset by ID.
        
        Args:
            dataset_id: HDX dataset identifier
            
        Returns:
            HDX Dataset object or None if not found
        """
        try:
            dataset = Dataset.read_from_hdx(dataset_id)
            if dataset:
                info("Retrieved dataset", title=dataset.get('title'), dataset_id=dataset_id)
            return dataset
        except Exception as e:
            error("Failed to retrieve dataset", dataset_id=dataset_id, error=str(e))
            return None
    
    def download_wfp_food_prices(self, force_refresh: bool = False) -> Optional[pd.DataFrame]:
        """Download WFP food price data for Yemen.
        
        This is the primary dataset for market integration analysis, containing
        commodity prices and exchange rates at market level.
        
        Args:
            force_refresh: Force download even if cache is valid
            
        Returns:
            DataFrame with WFP food price data or None if failed
        """
        dataset_id = self.DATASETS["wfp_food_prices"]
        
        with timer("download_wfp_food_prices"):
            try:
                dataset = self.get_dataset(dataset_id)
                if not dataset:
                    return None
                
                # Find CSV resource in the dataset
                csv_resource = None
                for resource in dataset.get_resources():
                    if resource.get_file_type() == 'csv':
                        csv_resource = resource
                        break
                
                if not csv_resource:
                    error("No CSV resource found in WFP dataset")
                    return None
                
                # Check cache
                resource_name = f"wfp_food_prices_{datetime.now().strftime('%Y%m')}.csv"
                cache_path = self._get_cache_path(dataset_id, resource_name)
                
                if not force_refresh and self._is_cache_valid(cache_path):
                    info("Using cached WFP data", cache_path=str(cache_path))
                    df = pd.read_csv(cache_path)
                    log_data_shape("cached_wfp_data", df)
                    return df
                
                # Download fresh data
                downloaded_path = self._download_resource(csv_resource, cache_path)
                if downloaded_path:
                    df = pd.read_csv(downloaded_path)
                    log_data_shape("fresh_wfp_data", df)
                    return df
                
                return None
                
            except Exception as e:
                error("Failed to download WFP food prices", error=str(e))
                return None
    
    def download_acled_conflict_data(self, force_refresh: bool = False) -> Optional[Path]:
        """Download ACLED conflict data for Yemen.
        
        Args:
            force_refresh: Force download even if cached data exists
            
        Returns:
            Path to downloaded ACLED CSV file or None if failed
        """
        with timer("download_acled_data"):
            info("Downloading ACLED conflict data")
            
        dataset_id = HDXClient.DATASETS.get("acled_conflict", "423a7d11-cc86-4226-8a77-4bbbc51371c4")
        
        try:
            # Get dataset
            dataset = self.get_dataset(dataset_id)
            if not dataset:
                error(f"ACLED dataset not found: {dataset_id}")
                return None
            
            # Find appropriate resource
            resources = dataset.get_resources()
            target_resource = None
            
            # Check available formats
            available_formats = [r.get('format', '').lower() for r in resources]
            info(f"Available formats: {available_formats}")
            
            # Try to find CSV, XLSX, or JSON format
            for resource in resources:
                format_type = resource.get('format', '').lower()
                if format_type in ['csv', 'xlsx', 'xls', 'json']:
                    target_resource = resource
                    break
            
            if not target_resource:
                error("No suitable data format found in ACLED dataset")
                error(f"Available formats: {available_formats}")
                return None
            
            # Download the resource
            filename = target_resource.get('name', 'acled_yemen_data')
            format_ext = target_resource.get('format', 'csv').lower()
            if not filename.endswith(f'.{format_ext}'):
                filename += f'.{format_ext}'
                
            # Create cache path
            cache_path = self._get_cache_path(dataset_id, filename)
            
            # Check if we should use cache
            if not force_refresh and self._is_cache_valid(cache_path):
                info(f"Using cached ACLED data: {cache_path}")
                file_path = cache_path
            else:
                # Download the resource
                file_path = self._download_resource(
                    target_resource, 
                    cache_path
                )
            
            if file_path:
                info(f"ACLED data downloaded: {file_path}")
                # Copy to acled directory
                acled_dir = RAW_DATA_DIR / "acled"
                acled_dir.mkdir(parents=True, exist_ok=True)
                
                target_path = acled_dir / filename
                import shutil
                shutil.copy2(file_path, target_path)
                info(f"ACLED data copied to: {target_path}")
                
                return target_path
            
            return None
            
        except Exception as e:
            error(f"Error downloading ACLED data: {e}")
            return None
    
    def download_admin_boundaries(self, force_refresh: bool = False) -> Optional[Dict[str, Path]]:
        """Download Yemen administrative boundary shapefiles.
        
        Args:
            force_refresh: Force download even if cache is valid
            
        Returns:
            Dictionary mapping admin levels to shapefile paths or None
        """
        dataset_id = self.DATASETS["admin_boundaries"]
        
        try:
            dataset = self.get_dataset(dataset_id)
            if not dataset:
                return None
            
            boundary_files = {}
            
            for resource in dataset.get_resources():
                # Look for shapefiles (usually in zip format)
                if 'adm' in resource.get('name', '').lower():
                    resource_name = resource.get('name')
                    cache_path = self._get_cache_path(dataset_id, resource_name)
                    
                    if not force_refresh and self._is_cache_valid(cache_path):
                        info("Using cached boundary file", file=resource_name)
                        boundary_files[resource_name] = cache_path
                    else:
                        downloaded_path = self._download_resource(resource, cache_path)
                        if downloaded_path:
                            boundary_files[resource_name] = downloaded_path
            
            return boundary_files if boundary_files else None
            
        except Exception as e:
            error("Failed to download admin boundaries", error=str(e))
            return None
    
    def download_all_datasets(
        self, 
        force_refresh: bool = False
    ) -> Dict[str, Union[pd.DataFrame, Dict, None]]:
        """Download all required datasets for Yemen market analysis.
        
        Args:
            force_refresh: Force download even if cache is valid
            
        Returns:
            Dictionary mapping dataset names to downloaded data
        """
        results = {}
        
        with progress("Downloading all datasets", total=2) as update:
            # Download WFP food prices (primary dataset)
            info("Downloading WFP food price data...")
            results['wfp_prices'] = self.download_wfp_food_prices(force_refresh)
            update(1, "Downloaded WFP prices")
            
            # Download administrative boundaries
            info("Downloading administrative boundaries...")
            results['admin_boundaries'] = self.download_admin_boundaries(force_refresh)
            update(1, "Downloaded boundaries")
        
        # Additional datasets can be added here as needed
        # For now, we focus on the core datasets
        
        # Summary
        successful = sum(1 for v in results.values() if v is not None)
        info("Download complete", successful=successful, total=len(results))
        
        return results
    
    def get_metadata(self, dataset_id: str) -> Optional[Dict]:
        """Retrieve metadata for an HDX dataset.
        
        Args:
            dataset_id: HDX dataset identifier
            
        Returns:
            Dictionary with dataset metadata or None
        """
        try:
            dataset = self.get_dataset(dataset_id)
            if dataset:
                metadata = {
                    'title': dataset.get('title'),
                    'organization': dataset.get('organization', {}).get('title'),
                    'updated': dataset.get('last_modified'),
                    'tags': [tag.get('name') for tag in dataset.get('tags', [])],
                    'resources': [
                        {
                            'name': r.get('name'),
                            'format': r.get('format'),
                            'size': r.get('size'),
                            'last_modified': r.get('last_modified')
                        }
                        for r in dataset.get_resources()
                    ]
                }
                return metadata
            return None
        except Exception as e:
            error("Failed to get metadata", dataset_id=dataset_id, error=str(e))
            return None
    
    def clear_cache(self, older_than_days: Optional[int] = None):
        """Clear cached files older than specified days.
        
        Args:
            older_than_days: Clear files older than this many days.
                           If None, clears all cache.
        """
        if older_than_days is None:
            # Clear all cache
            count = 0
            for file in self.cache_dir.rglob('*'):
                if file.is_file():
                    file.unlink()
                    count += 1
            info("Cache cleared", files_removed=count)
        else:
            # Clear old files
            cutoff = datetime.now() - timedelta(days=older_than_days)
            count = 0
            for file in self.cache_dir.rglob('*'):
                if file.is_file():
                    if datetime.fromtimestamp(file.stat().st_mtime) < cutoff:
                        file.unlink()
                        count += 1
            info("Old cache cleared", files_removed=count, older_than_days=older_than_days)