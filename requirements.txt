# Core dependencies
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0
statsmodels>=0.14.0
linearmodels>=5.0
arch>=6.0
scikit-learn>=1.3.0

# Numerical computing and optimization
jax>=0.6.0

# Geospatial packages
geopandas>=0.13.0
shapely>=2.0.0
fiona>=1.9.0
pyproj>=3.5.0
libpysal>=4.7.0
spreg>=1.3.0
esda>=2.7.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.14.0
ipython>=9.0.0

# Data access
hdx-python-api>=6.0.0
requests>=2.28.0
pyarrow>=12.0.0
python-dotenv>=1.0.0

# Analysis tools
networkx>=3.1
shap>=0.42.0
joblib>=1.3.0

# Logging
loguru>=0.7.0
structlog>=24.0.0
rich>=13.0.0
python-json-logger>=2.0.0

# Jupyter
jupyter>=1.0.0
ipykernel>=6.25.0

# Experiment tracking and monitoring
mlflow>=2.22.0
neptune>=1.14.0

# Week 5-6 Models (Bayesian)
pymc>=5.10.0
arviz>=0.17.0
pytensor>=2.18.0

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0
black>=23.0.0
isort>=5.12.0
