{"hardware": {"platform": "apple_silicon_m3_pro", "cpu_cores": 10, "gpu_cores": 18, "memory_gb": 36}, "pymc": {"chains": 4, "cores": 4, "target_accept": 0.9, "max_treedepth": 12, "init": "adapt_diag", "tune": 1000, "draws": 2000}, "bootstrap": {"n_jobs": 10, "backend": "threading", "batch_size": 100, "verbose": 10}, "numpy": {"threads": 10, "block_size": 512}, "data": {"chunk_size": 5000, "cache_enabled": true, "compression": "snappy"}}