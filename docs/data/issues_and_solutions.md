# Data Pipeline Issues and Solutions

## Overview

This document details the challenges encountered during data processing and the solutions implemented. Each issue is documented with its impact, root cause, and resolution.

## 1. ACAPS Nested ZIP Structure

### Issue Description
ACAPS control zone files come as ZIPs containing subdirectories with additional ZIPs, each containing shapefiles for different control actors (DFA, IRG, STC).

### Impact
- Standard shapefile readers failed
- Initial processing extracted 0 records
- Manual extraction was time-consuming and error-prone

### Root Cause
```
20240327 Yemen Analysis Hub - Areas of control.zip
├── DFA/
│   └── DFA_Control_27032024.zip  # Another ZIP!
│       ├── DFA_Control_27032024.shp
│       ├── DFA_Control_27032024.dbf
│       └── ...
├── IRG/
│   └── IRG_Control_27032024.zip
└── STC/
    └── STC_Control_27032024.zip
```

### Solution Implemented
```python
def extract_nested_zips(main_zip_path):
    """Extract nested ZIP structure and process all shapefiles."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Extract main ZIP
        with zipfile.ZipFile(main_zip_path, 'r') as main_zip:
            main_zip.extractall(temp_dir)
        
        # Find and extract nested ZIPs
        for inner_zip in Path(temp_dir).rglob("*.zip"):
            extract_dir = inner_zip.parent / inner_zip.stem
            with zipfile.ZipFile(inner_zip, 'r') as zf:
                zf.extractall(extract_dir)
        
        # Load all shapefiles
        gdfs = []
        for shp in Path(temp_dir).rglob("*.shp"):
            if "lookup" not in str(shp).lower():
                gdf = gpd.read_file(shp)
                # Add control zone based on directory
                control_zone = shp.parts[-3].upper()  # DFA, IRG, STC
                gdf['control_zone'] = control_zone
                gdfs.append(gdf)
        
        return pd.concat(gdfs, ignore_index=True)
```

### Result
Successfully extracted 362 control zone polygons per time period.

## 2. Commodity Name Mismatch (Title Case Issue)

### Issue Description
WFP commodity names didn't match between configuration and actual data, resulting in 0 price records extracted.

### Impact
- Initial extraction: 0 commodity prices
- Panel building failed
- Missing all price analysis data

### Root Cause
Configuration used lowercase commodity names:
```python
commodities = ["wheat", "rice (imported)", "sugar"]
```

But WFP data uses Title Case:
```
"Wheat", "Rice (Imported)", "Sugar"
```

### Solution Implemented
```python
# In WFPProcessor.__init__
if commodities is not None:
    # Standardize to Title Case
    self.commodities = [c.strip().title() for c in commodities]
    
    # Handle special cases
    commodity_corrections = {
        'Rice (imported)': 'Rice (Imported)',
        'Beans (kidney red)': 'Beans (Kidney Red)',
        'Oil (vegetable)': 'Oil (Vegetable)',
        'Peas (yellow, split)': 'Peas (Yellow, Split)'
    }
    
    self.commodities = [
        commodity_corrections.get(c, c) for c in self.commodities
    ]
```

### Result
Extraction improved from 0 to 26,708 commodity price records.

## 3. Missing Market Coordinates

### Issue Description
Spatial joins failed because WFP panel data didn't include latitude/longitude coordinates.

### Impact
- Spatial joins returned 0 matches
- No market-to-control-zone mapping
- Econometric models couldn't proceed

### Root Cause
The aggregation step in `create_market_panel()` dropped coordinate columns:
```python
# Original code
panel = df.groupby(['governorate', 'market_name', 'year_month']).agg({...})
# Lost lat/lon columns!
```

### Solution Implemented
```python
def create_market_panel(self, df):
    """Create market-level panel preserving coordinates."""
    # ... aggregation code ...
    
    # Preserve coordinates
    market_coords = df[['governorate', 'market_name', 'lat', 'lon']].drop_duplicates()
    panel = panel.merge(
        market_coords, 
        on=['governorate', 'market_name'], 
        how='left'
    )
    
    return panel
```

### Result
Successfully mapped 28 markets to control zones with 92.9% coverage.

## 4. Governorate Name Standardization

### Issue Description
WFP uses different governorate spellings than official Yemen pcodes, preventing proper data integration.

### Examples of Mismatches
| WFP Name | Pcode Standard | 
|----------|----------------|
| Al Dhale'e | Ad Dale' |
| Al Hudaydah | Al Hodeidah |
| Amanat Al Asimah | Sana'a City |
| Hadramaut | Hadramawt |
| Sa'ada | Sa'dah |
| Taizz | Ta'iz |

### Impact
- Lost 1 market in data processing
- Inconsistent aggregations
- Failed joins with boundary data

### Solution Implemented
```python
class EnhancedWFPProcessor:
    GOVERNORATE_MAPPINGS = {
        "Al Dhale'e": "Ad Dale'",
        "Al Hudaydah": "Al Hodeidah",
        "Amanat Al Asimah": "Sana'a City",
        "Hadramaut": "Hadramawt",
        "Sa'ada": "Sa'dah",
        "Taizz": "Ta'iz"
    }
    
    def standardize_governorate_name(self, name):
        """Standardize to pcode convention."""
        return self.GOVERNORATE_MAPPINGS.get(name, name)
```

### Result
100% governorate matching after standardization.

## 5. Structural Missing Prices (38% Missing)

### Issue Description
Creating a balanced panel with all commodity-market-time combinations resulted in 38% missing prices.

### Root Cause Analysis
```python
# Investigation revealed:
- Meat products: Only sold in 2 markets (not 28)
- Oil (Sunflower): Only in 16 markets
- Sorghum/Millet: Limited regional availability

# Problem: Forcing all combinations
all_commodities × all_markets × all_times = massive missing data
```

### Impact
- 38% missing prices in panel
- Biased imputation requirements
- Unrealistic market assumptions

### Solution Implemented
Smart panel creation that respects commodity availability:

```python
def create_smart_panels(self, commodity_df):
    """Create panels based on actual commodity availability."""
    # Auto-select commodities in 50%+ of markets
    commodity_coverage = self._analyze_commodity_coverage(commodity_df)
    selected = commodity_coverage[commodity_coverage >= 0.5].index
    
    # Create commodity-specific panels
    panels = []
    for commodity in selected:
        comm_data = commodity_df[commodity_df['commodity'] == commodity]
        comm_markets = comm_data['market_id'].unique()
        
        # Only create panel for markets that sell this commodity
        panel = create_balanced_panel(commodity, comm_markets, dates)
        panels.append(panel)
    
    return pd.concat(panels)
```

### Result
- Reduced from 44,122 to 14,208 observations
- Improved coverage from 62% to 88.4%
- Eliminated structural missing data

## 6. ACLED API Data Access

### Issue Description
Initial attempts to use ACLED data from HDX only provided aggregated monthly summaries, not event-level data needed for spatial analysis.

### Impact
- Couldn't calculate market-specific conflict metrics
- No spatial buffer analysis possible
- Missing conflict intensity variables

### Solution Implemented
Direct API access with authentication:

```python
# Store credentials securely
# .env file:
ACLED_API_KEY=your_key_here
ACLED_EMAIL=<EMAIL>

# API call with parameters
params = {
    'key': os.getenv('ACLED_API_KEY'),
    'email': os.getenv('ACLED_EMAIL'),
    'country': 'Yemen',
    'start_date': '2019-01-01',
    'end_date': '2024-12-31',
    'limit': 0  # Get all records
}
```

### Result
Downloaded 57,509 event-level records enabling market-specific conflict metrics.

## 7. Period[M] Type Mismatch

### Issue Description
Merging conflict data with price panel failed due to incompatible date types - string vs Period[M].

### Error Message
```
MergeError: You are trying to merge on period[M] and object columns
```

### Solution Implemented
```python
# In panel_builder.py
if 'conflict' in data:
    conflict = data['conflict'].copy()
    
    # Convert year_month to period if string
    if conflict['year_month'].dtype == 'object':
        conflict['year_month'] = pd.to_datetime(
            conflict['year_month']
        ).dt.to_period('M')
```

### Result
Successful merge with 60.5% conflict data coverage.

## 8. Large Number of Deprecation Warnings

### Issue Description
Pandas fillna with method='ffill'/'bfill' deprecated, causing hundreds of warnings.

### Solution for Future
```python
# Old (deprecated)
df.fillna(method='ffill')

# New approach
df.ffill()  # or df.bfill()
```

Note: Left as-is for now to maintain compatibility, but should be updated in future refactoring.

## Summary of Improvements

### Before Fixes
- 0 commodity prices extracted
- 0 successful spatial joins
- 38% missing prices in panel
- No conflict data integration
- Name mismatches losing data

### After Fixes
- 33,926 commodity prices processed
- 28 markets mapped to zones
- 88.4% price coverage
- 2,016 conflict observations integrated
- 100% governorate matching with pcodes

## Lessons Learned

1. **Always inspect raw data formats** - Don't assume standard structures
2. **Test with small samples first** - Catch issues early
3. **Preserve all columns through aggregations** - Coordinates are critical
4. **Respect data availability** - Don't force unrealistic combinations
5. **Use official standards** - Pcodes prevent name mismatch issues
6. **Secure API credentials** - Use environment variables, never hardcode

## Recommendations for Future

1. **Implement pcode columns throughout** - Add admin1_pcode, admin2_pcode
2. **Create data quality dashboard** - Monitor coverage metrics
3. **Automate validation checks** - Catch issues before modeling
4. **Version control data schemas** - Track structure changes
5. **Document assumptions clearly** - Why 50km radius? Why 50% coverage threshold?