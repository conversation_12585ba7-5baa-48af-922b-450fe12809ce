"""Configuration settings for Yemen Market Integration Analysis."""

from pathlib import Path
import os
from typing import Dict, Any

# Project root directory
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent

# Data directories
DATA_DIR = PROJECT_ROOT / "data"
RAW_DATA_DIR = DATA_DIR / "raw"
INTERIM_DATA_DIR = DATA_DIR / "interim"
PROCESSED_DATA_DIR = DATA_DIR / "processed"
EXTERNAL_DATA_DIR = DATA_DIR / "external"

# Output directories
REPORTS_DIR = PROJECT_ROOT / "reports"
FIGURES_DIR = REPORTS_DIR / "figures"
TABLES_DIR = REPORTS_DIR / "tables"

# HDX Configuration
HDX_CONFIG = {
    "site": "prod",
    "user_agent": "YemenMarketAnalysis",
    "cache_days": 7,
}

# Model parameters
MODEL_PARAMS = {
    "threshold_vecm": {
        "n_thresholds": 2,
        "grid_search_pct": (0.15, 0.85),
        "n_lags": 2,
        "bootstrap_reps": 1000,
    },
    "tvp_vecm": {
        "n_chains": 4,
        "n_samples": 2000,
        "tune": 1000,
    },
    "spatial": {
        "distance_threshold": 200,  # km
        "control_penalty": 0.1,
        "correlation_threshold": 0.8,
    },
}

# Analysis parameters
ANALYSIS_CONFIG = {
    "start_date": "2019-01-01",
    "end_date": "2024-12-31",
    "commodities": [
        # Grains and cereals
        "wheat", "wheat flour", "rice (imported)", "sorghum", "millet",
        # Proteins
        "beans (kidney red)", "beans (white)", "lentils", "peas (yellow, split)", 
        "eggs", "meat (chicken)", "meat (mutton)",
        # Oils and fats
        "oil (vegetable)", "oil (sunflower)",
        # Vegetables
        "onions", "potatoes", "tomatoes",
        # Other essentials
        "sugar", "salt",
        # Fuel (important for transport costs)
        "fuel (diesel)", "fuel (petrol-gasoline)", "fuel (gas)"
    ],
    "confidence_level": 0.95,
    "rolling_window": 24,  # months
}

# Logging configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        },
    },
    "handlers": {
        "default": {
            "level": "INFO",
            "formatter": "standard",
            "class": "logging.StreamHandler",
        },
        "file": {
            "level": "DEBUG",
            "formatter": "standard",
            "class": "logging.FileHandler",
            "filename": PROJECT_ROOT / "analysis.log",
        },
    },
    "loggers": {
        "": {
            "handlers": ["default", "file"],
            "level": "INFO",
            "propagate": True,
        }
    },
}

# Create directories if they don't exist
for directory in [
    RAW_DATA_DIR,
    INTERIM_DATA_DIR,
    PROCESSED_DATA_DIR,
    EXTERNAL_DATA_DIR,
    FIGURES_DIR,
    TABLES_DIR,
]:
    directory.mkdir(parents=True, exist_ok=True)
