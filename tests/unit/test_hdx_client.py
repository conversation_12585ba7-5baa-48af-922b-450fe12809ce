"""Unit tests for HDX Client."""

import json
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import pandas as pd
import pytest
from hdx.data.dataset import Dataset
from hdx.data.resource import Resource

from yemen_market.data.hdx_client import HDXClient


@pytest.fixture
def hdx_client(tmp_path):
    """Create HDX client with temporary cache directory."""
    return HDXClient(cache_dir=tmp_path / "hdx_cache", cache_days=7)


@pytest.fixture
def mock_dataset():
    """Create mock HDX dataset."""
    dataset = MagicMock(spec=Dataset)
    dataset.get.return_value = {
        'title': 'WFP Food Prices for Yemen',
        'organization': {'title': 'World Food Programme'},
        'last_modified': '2024-01-15T00:00:00',
        'tags': [{'name': 'food'}, {'name': 'prices'}]
    }
    
    # Mock resources
    csv_resource = MagicMock()
    csv_resource.get_file_type.return_value = 'csv'
    csv_resource.get.return_value = 'wfp_prices.csv'
    csv_resource.get_download_url.return_value = 'https://data.humdata.org/dataset/wfp_prices.csv'
    
    dataset.get_resources.return_value = [csv_resource]
    return dataset


@pytest.fixture
def mock_wfp_data():
    """Create mock WFP price data."""
    return pd.DataFrame({
        'date': pd.date_range('2023-01-01', periods=10, freq='M'),
        'market': ['Sana\'a'] * 5 + ['Aden'] * 5,
        'commodity': ['Wheat'] * 10,
        'price': [100, 105, 110, 108, 112, 95, 98, 102, 100, 103],
        'currency': ['YER'] * 10,
        'usdprice': [0.4, 0.42, 0.44, 0.43, 0.45, 0.38, 0.39, 0.41, 0.4, 0.41]
    })


class TestHDXClient:
    """Test HDX client functionality."""
    
    def test_initialization(self, tmp_path):
        """Test client initialization."""
        cache_dir = tmp_path / "test_cache"
        client = HDXClient(cache_dir=cache_dir, cache_days=14, rate_limit_delay=1.0)
        
        assert client.cache_dir == cache_dir
        assert client.cache_days == 14
        assert client.rate_limit_delay == 1.0
        assert cache_dir.exists()
    
    def test_cache_path_generation(self, hdx_client):
        """Test cache path generation."""
        path = hdx_client._get_cache_path("test-dataset", "test-file.csv")
        
        assert path == hdx_client.cache_dir / "test-dataset" / "test-file.csv"
    
    def test_cache_validity_check(self, hdx_client, tmp_path):
        """Test cache validity checking."""
        # Create a test file
        test_file = tmp_path / "test.csv"
        test_file.write_text("test data")
        
        # Fresh file should be valid
        assert hdx_client._is_cache_valid(test_file)
        
        # Old file should be invalid
        old_time = datetime.now() - timedelta(days=10)
        import os
        os.utime(test_file, (old_time.timestamp(), old_time.timestamp()))
        assert not hdx_client._is_cache_valid(test_file)
        
        # Non-existent file should be invalid
        assert not hdx_client._is_cache_valid(tmp_path / "nonexistent.csv")
    
    @patch('yemen_market.data.hdx_client.Dataset.read_from_hdx')
    def test_get_dataset_success(self, mock_read_hdx, hdx_client, mock_dataset):
        """Test successful dataset retrieval."""
        mock_read_hdx.return_value = mock_dataset
        
        dataset = hdx_client.get_dataset("test-dataset")
        
        assert dataset is not None
        mock_read_hdx.assert_called_once_with("test-dataset")
    
    @patch('yemen_market.data.hdx_client.Dataset.read_from_hdx')
    def test_get_dataset_failure(self, mock_read_hdx, hdx_client):
        """Test dataset retrieval failure."""
        mock_read_hdx.side_effect = Exception("API Error")
        
        dataset = hdx_client.get_dataset("test-dataset")
        
        assert dataset is None
    
    @patch('yemen_market.data.hdx_client.requests.get')
    def test_download_resource(self, mock_get, hdx_client, tmp_path):
        """Test resource download."""
        # Mock response
        mock_response = Mock()
        mock_response.iter_content.return_value = [b"test data"]
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Mock resource
        resource = Mock()
        resource.get_download_url.return_value = "https://test.com/data.csv"
        
        # Download
        cache_path = tmp_path / "data.csv"
        result = hdx_client._download_resource(resource, cache_path)
        
        assert result == cache_path
        assert cache_path.exists()
        assert cache_path.read_text() == "test data"
    
    @patch('yemen_market.data.hdx_client.Dataset.read_from_hdx')
    @patch('yemen_market.data.hdx_client.requests.get')
    def test_download_wfp_food_prices(self, mock_get, mock_read_hdx, hdx_client, mock_dataset, mock_wfp_data):
        """Test WFP food price download."""
        mock_read_hdx.return_value = mock_dataset
        
        # Mock response with CSV data
        mock_response = Mock()
        mock_response.iter_content.return_value = [mock_wfp_data.to_csv(index=False).encode()]
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Download data
        df = hdx_client.download_wfp_food_prices()
        
        assert df is not None
        assert len(df) == 10
        assert 'market' in df.columns
        assert 'price' in df.columns
    
    @patch('yemen_market.data.hdx_client.Dataset.read_from_hdx')
    def test_download_wfp_cached(self, mock_read_hdx, hdx_client, mock_wfp_data):
        """Test using cached WFP data."""
        # Create cached file
        dataset_id = hdx_client.DATASETS["wfp_food_prices"]
        resource_name = f"wfp_food_prices_{datetime.now().strftime('%Y%m')}.csv"
        cache_path = hdx_client._get_cache_path(dataset_id, resource_name)
        cache_path.parent.mkdir(parents=True, exist_ok=True)
        mock_wfp_data.to_csv(cache_path, index=False)
        
        # Should use cache without calling API
        df = hdx_client.download_wfp_food_prices()
        
        assert df is not None
        assert len(df) == 10
        mock_read_hdx.assert_not_called()
    
    @patch('yemen_market.data.hdx_client.Dataset.read_from_hdx')
    def test_download_admin_boundaries(self, mock_read_hdx, hdx_client):
        """Test admin boundary download."""
        # Mock dataset with shapefile resources
        dataset = Mock()
        
        resource1 = Mock()
        resource1.get.return_value = 'yem_adm1.zip'
        
        resource2 = Mock()
        resource2.get.return_value = 'yem_adm2.zip'
        
        dataset.get_resources.return_value = [resource1, resource2]
        mock_read_hdx.return_value = dataset
        
        with patch.object(hdx_client, '_download_resource') as mock_download:
            mock_download.side_effect = [
                hdx_client.cache_dir / 'yem_adm1.zip',
                hdx_client.cache_dir / 'yem_adm2.zip'
            ]
            
            result = hdx_client.download_admin_boundaries()
            
            assert result is not None
            assert len(result) == 2
    
    def test_get_metadata(self, hdx_client, mock_dataset):
        """Test metadata retrieval."""
        with patch.object(hdx_client, 'get_dataset', return_value=mock_dataset):
            metadata = hdx_client.get_metadata("test-dataset")
            
            assert metadata is not None
            assert metadata['title'] == 'WFP Food Prices for Yemen'
            assert metadata['organization'] == 'World Food Programme'
            assert len(metadata['tags']) == 2
    
    def test_clear_cache_all(self, hdx_client, tmp_path):
        """Test clearing all cache."""
        # Create test files
        (hdx_client.cache_dir / "test1.csv").touch()
        (hdx_client.cache_dir / "test2.csv").touch()
        
        hdx_client.clear_cache()
        
        # Check all files removed
        files = list(hdx_client.cache_dir.rglob('*'))
        assert len([f for f in files if f.is_file()]) == 0
    
    def test_clear_cache_old_files(self, hdx_client, tmp_path):
        """Test clearing old cached files."""
        # Create files with different ages
        new_file = hdx_client.cache_dir / "new.csv"
        old_file = hdx_client.cache_dir / "old.csv"
        
        new_file.touch()
        old_file.touch()
        
        # Make old file older
        old_time = datetime.now() - timedelta(days=10)
        import os
        os.utime(old_file, (old_time.timestamp(), old_time.timestamp()))
        
        # Clear files older than 5 days
        hdx_client.clear_cache(older_than_days=5)
        
        assert new_file.exists()
        assert not old_file.exists()
    
    @patch('yemen_market.data.hdx_client.Dataset.read_from_hdx')
    def test_download_all_datasets(self, mock_read_hdx, hdx_client, mock_dataset):
        """Test downloading all datasets."""
        mock_read_hdx.return_value = mock_dataset
        
        with patch.object(hdx_client, 'download_wfp_food_prices', return_value=pd.DataFrame()):
            with patch.object(hdx_client, 'download_admin_boundaries', return_value={}):
                results = hdx_client.download_all_datasets()
                
                assert 'wfp_prices' in results
                assert 'admin_boundaries' in results
                assert results['wfp_prices'] is not None
                assert results['admin_boundaries'] is not None