# Methodology Notes - Key Decisions

## Data Frequency
- **Decision**: Use monthly aggregation instead of weekly
- **Rationale**: WFP data mostly monthly updates despite weekly target
- **Implication**: Adjust threshold estimation window accordingly

## Exchange Rate Mapping
- **Decision**: Map markets to control zones using spatial joins with ACAPS data
- **Method**: Point-in-polygon using geopandas
- **Challenge**: Handle markets near boundaries with buffer zones

## Model Selection Hierarchy
1. **Primary**: Bayesian TVP-VECM for main results
2. **Secondary**: Multiple threshold VECM for robustness
3. **Supporting**: M-TAR for asymmetric adjustment
4. **Validation**: Markov regime-switching models

## Threshold Estimation
- **Grid search range**: 15th to 85th percentile of ECM
- **Multiple thresholds**: Sequential testing (max 3)
- **Inference**: Wild bootstrap (1000 replications)

## Spatial Weights
- **Base**: Inverse distance (200km cutoff)
- **Adjustment**: 90% penalty for crossing control boundaries
- **Network**: Inferred from price correlation (>0.8)

## Policy Simulations
- **Scenarios**: Immediate, gradual (12-month), stepped (quarterly)
- **Uncertainty**: Bootstrap fan charts
- **Sensitivity**: Vary across conflict intensity regimes

## Machine Learning Integration
- **Random Forest**: Feature importance via SHAP
- **LSTM**: 6-month ahead convergence forecasts
- **Validation**: Time series cross-validation (expanding window)

## Key Assumptions
1. Exchange rates in WFP data reflect market conditions
2. ACAPS control boundaries are accurate within districts
3. Monthly aggregation preserves essential dynamics
4. Transaction costs are symmetric within regimes
