# Data Pipeline Guide

**Status**: 100% Complete ✅ | Last Updated: 2025-05-27

> 📚 **For comprehensive documentation**, see the [Data Documentation](../data/) folder which includes:
> - [Data Sources](../data/data_sources.md) - Detailed source descriptions
> - [Pipeline Details](../data/data_pipeline_detailed.md) - Step-by-step processing
> - [Issues & Solutions](../data/issues_and_solutions.md) - Challenges resolved
> - [Final Datasets](../data/final_dataset_description.md) - Output specifications

## Overview

The Yemen Market Integration data pipeline is a comprehensive system for collecting, processing, and integrating multiple humanitarian data sources into analysis-ready datasets. The pipeline has been enhanced with pcode-based matching and smart panel creation, achieving 88.4% price coverage.

## Pipeline Architecture

```mermaid
graph TD
    A[HDX API] --> B[HDXClient]
    B --> C[Raw Data Storage]
    C --> D[WFPProcessor]
    C --> E[ACAPSProcessor]
    D --> F[Processed Data]
    E --> F
    F --> G[SpatialJoiner]
    G --> H[Spatial Mappings]
    H --> I[PanelBuilder]
    F --> I
    I --> J[Analysis-Ready Panels]
```

## Components

### 1. HDXClient (`src/yemen_market/data/hdx_client.py`)

**Purpose**: Fetches humanitarian data from the Humanitarian Data Exchange (HDX) platform.

**Key Features**:
- Authenticated API access using HDX credentials
- Built-in caching mechanism (7-day default)
- Rate limiting to respect API constraints
- Automatic retry with exponential backoff
- Support for multiple dataset types

**Usage**:
```python
from yemen_market.data import HDXClient

client = HDXClient()
# Download WFP price data
filepath = client.download_dataset('wfp-food-prices-for-yemen')
# Download admin boundaries
boundaries = client.download_dataset('cod-ab-yem')
```

**Configuration**:
- Set `HDX_API_KEY` in environment or `.env` file
- Cache directory: `data/raw/{dataset_name}/`
- Rate limit: 0.5 seconds between requests

### 2. WFPProcessor (`src/yemen_market/data/wfp_processor.py`)

**Purpose**: Processes World Food Programme price data, extracting commodity prices and exchange rates.

**Key Features**:
- Standardizes commodity names and units
- Extracts official and parallel exchange rates
- Calculates USD prices from local currency
- Creates price indices (base=100)
- Generates market-level panel datasets
- Handles missing data appropriately

**Data Processing Steps**:
1. Parse CSV with proper data types
2. Standardize column names
3. Extract exchange rates from price ratios
4. Convert prices to USD
5. Calculate temporal indices
6. Create balanced panels

**Output Files**:
- `wfp_price_data.parquet` - All processed price records
- `exchange_rates.parquet` - Market-level exchange rates
- `price_indices.parquet` - Commodity price indices
- `market_panel.parquet` - Balanced market panel

### 3. ACAPSProcessor (`src/yemen_market/data/acaps_processor.py`)

**Purpose**: Processes ACAPS bi-weekly areas of control data to track territorial dynamics.

**Key Features**:
- Extracts control zone data from shapefiles/Excel
- Standardizes control zone names (Houthi/Government/Contested/STC)
- Creates time series of territorial changes
- Tracks control zone transitions
- Aligns bi-weekly data to monthly frequency
- Calculates stability metrics

**Control Zone Categories**:
```python
CONTROL_ZONES = {
    'houthi': ['Houthi', 'Ansar Allah', 'SPC'],
    'government': ['Government', 'IRG', 'GOY'],
    'contested': ['Contested', 'Disputed', 'Mixed'],
    'stc': ['STC', 'Southern Transitional Council'],
    'other': ['Other', 'Local', 'Tribal']
}
```

**Output Files**:
- `acaps_control_zones_raw.parquet` - All control records
- `control_zone_time_series.parquet` - Temporal changes
- `control_zones_monthly.parquet` - Monthly aligned data

### 4. SpatialJoiner (`src/yemen_market/data/spatial_joins.py`)

**Purpose**: Maps WFP markets to ACAPS control zones using spatial operations.

**Key Features**:
- Spatial join of market points to control polygons
- Temporal tracking of zone assignments
- Identification of boundary markets (within 10km of borders)
- Distance calculations to all control zones
- Handles unmatched markets by finding nearest zone
- Multiple output formats (GeoJSON, Parquet)

**Spatial Operations**:
1. Load market coordinates from WFP data
2. Load control zone boundaries from ACAPS
3. Perform point-in-polygon spatial join
4. Calculate distances for unmatched markets
5. Identify markets near zone boundaries
6. Create temporal mapping over time

**Output Files**:
- `market_zones_current.geojson` - Current mapping for GIS
- `market_zones_temporal.parquet` - Time series mapping
- `boundary_markets.csv` - Markets near boundaries
- `market_zone_distances.parquet` - Distance matrix

### 5. PanelBuilder (`src/yemen_market/data/panel_builder.py`)

**Purpose**: Integrates all data sources into analysis-ready panel datasets.

**Key Features**:
- Merges prices, exchange rates, and control zones
- Creates balanced panel structures
- Adds temporal features (lags, differences, rolling stats)
- Handles missing data with appropriate methods
- Generates model-specific datasets
- Calculates derived features (exchange rate differentials)

**Panel Types Created**:
1. **Integrated Panel**: Complete dataset with all variables
2. **Price Transmission Panel**: For market pair analysis
3. **Exchange Pass-through Panel**: For currency analysis
4. **Threshold Cointegration Panel**: For threshold models
5. **Spatial Panel**: With geographic information

**Temporal Features Added**:
- Lagged variables (1-3 periods)
- First differences and percentage changes
- Moving averages (3-month)
- Rolling standard deviations
- Time trends and seasonal indicators

## Running the Pipeline

### Full Pipeline Execution

```bash
# 1. Download all data
python scripts/download_data.py

# 2. Process WFP data
python scripts/process_wfp_data.py

# 3. Process ACAPS data
python scripts/process_acaps_data.py

# 4. Create spatial mappings
python scripts/run_spatial_joins.py

# 5. Build panel datasets
python scripts/build_panel_datasets.py
```

### Individual Component Usage

```python
from yemen_market.data import (
    HDXClient, WFPProcessor, ACAPSProcessor, 
    SpatialJoiner, PanelBuilder
)

# Download data
client = HDXClient()
client.download_dataset('wfp-food-prices-for-yemen')

# Process prices
processor = WFPProcessor()
price_data = processor.process_price_file('path/to/wfp_data.csv')

# Process control zones
acaps = ACAPSProcessor()
control_data = acaps.process_all_files()

# Map markets to zones
joiner = SpatialJoiner()
markets = joiner.load_market_coordinates()
zones = joiner.load_control_zones()
mapping = joiner.perform_spatial_join(markets, zones)

# Build panels
builder = PanelBuilder()
data = builder.load_component_data()
panel = builder.create_price_panel(data)
```

## Data Quality Checks

The pipeline includes multiple quality checks:

1. **Input Validation**:
   - File format verification
   - Required column checks
   - Data type validation

2. **Processing Checks**:
   - Duplicate detection
   - Range validation for prices/rates
   - Temporal consistency
   - Spatial validity

3. **Output Validation**:
   - Panel balance verification
   - Missing data reporting
   - Summary statistics generation

## Configuration

Key settings in `src/yemen_market/config/settings.py`:

```python
# HDX Configuration
HDX_CONFIG = {
    'hdx_site': 'prod',  # or 'demo' for testing
    'user_agent': 'Yemen-Market-Analysis',
    'cache_days': 7
}

# Analysis Configuration
ANALYSIS_CONFIG = {
    'key_commodities': ['Wheat', 'Rice', 'Sugar', 'Cooking oil', 'Red beans'],
    'start_date': '2020-01-01',
    'end_date': None,  # Use latest available
    'exchange_rate_types': ['official', 'parallel']
}
```

## Error Handling

The pipeline implements robust error handling:

- **Network Errors**: Automatic retry with exponential backoff
- **Data Errors**: Logged warnings with graceful degradation
- **Missing Files**: Clear error messages with resolution steps
- **Invalid Data**: Validation errors with specific details

## Logging

All components use the enhanced context-aware logging system:

```python
from yemen_market.utils.logging import get_logger, log_operation
logger = get_logger(__name__)

# Features:
# - Context tracking (operation type, component, duration)
# - Structured output (JSON for analysis)
# - Operation decorators for automatic tracking
# - Progress indicators for long operations

# Logs are written to:
# - Console (INFO level with color coding)
# - File: logs/yemen_market_{date}.log (DEBUG level with full context)
```

See [Logging Guide](logging_guide.md) for detailed usage.

## Testing

Each component has comprehensive unit tests:

```bash
# Test individual components
pytest tests/unit/test_hdx_client.py
pytest tests/unit/test_wfp_processor.py
pytest tests/unit/test_acaps_processor.py
pytest tests/unit/test_spatial_joins.py
pytest tests/unit/test_panel_builder.py

# Run all tests with coverage
pytest tests/ --cov=yemen_market --cov-report=html
```

## Output Data Structure

```
data/
├── raw/                            # Original downloaded files
│   ├── hdx/                       # HDX datasets
│   ├── acaps/                     # ACAPS shapefiles
│   └── wfp/                       # WFP data files
└── processed/                      # Analysis-ready data
    ├── wfp/
    │   ├── wfp_price_data.parquet      # All price records
    │   ├── exchange_rates.parquet       # Exchange rate time series
│   ├── price_indices.parquet        # Price indices by commodity
│   └── market_panel.parquet         # Market-level panel
├── control_zones/
│   ├── acaps_control_zones_raw.parquet  # Raw control data
│   ├── control_zone_time_series.parquet # Temporal changes
│   └── control_zones_monthly.parquet    # Monthly aggregated
├── spatial/
│   ├── market_zones_current.geojson     # Current mapping
│   ├── market_zones_temporal.parquet    # Historical mapping
│   ├── boundary_markets.csv             # Border markets
│   └── market_zone_distances.parquet    # Distance matrix
└── panels/
    ├── integrated_panel.parquet         # Main analysis dataset
    ├── price_transmission_panel.parquet # Market pairs
    ├── exchange_passthrough_panel.parquet # Currency analysis
    ├── threshold_coint_panel.parquet    # Threshold models
    └── spatial_panel.parquet            # Geographic analysis
```

## Troubleshooting

### Common Issues

1. **HDX Authentication Failed**
   - Check HDX_API_KEY in environment
   - Verify internet connection
   - Try HDX demo site for testing

2. **Missing Data Files**
   - Run scripts in order (download → process)
   - Check data directories exist
   - Verify file permissions

3. **Memory Errors**
   - Process data in chunks
   - Increase available RAM
   - Use subset of commodities

4. **Spatial Join Failures**
   - Ensure CRS compatibility
   - Check for valid geometries
   - Verify shapefile completeness

## Next Steps

With the data pipeline complete, you can:

1. Load the integrated panel for exploratory analysis
2. Use model-specific panels for econometric estimation
3. Access spatial mappings for geographic visualization
4. Query time series for specific markets or commodities

The pipeline creates a solid foundation for the econometric analysis of market integration in Yemen.