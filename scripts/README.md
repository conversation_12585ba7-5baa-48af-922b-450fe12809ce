# Scripts Directory

This directory contains all executable scripts for the Yemen Market Integration project, organized by functionality.

## Directory Structure

```
scripts/
├── data_collection/     # Scripts for downloading and collecting raw data
├── data_processing/     # Scripts for processing and transforming data
├── analysis/           # Scripts for building analysis datasets
└── utilities/          # Utility scripts for testing, exporting, etc.
```

## Data Collection Scripts

### `download_data.py`
Downloads all required datasets from Humanitarian Data Exchange (HDX).
```bash
python scripts/data_collection/download_data.py
```

### `download_acled_data.py`
Downloads ACLED conflict event data using the ACLED API.
```bash
python scripts/data_collection/download_acled_data.py
```

## Data Processing Scripts

### `process_wfp_data.py`
Processes WFP food price data with enhanced pcode support and smart panel creation.
```bash
python scripts/data_processing/process_wfp_data.py
```

### `process_acaps_data.py`
Processes ACAPS control zone shapefiles to extract temporal control information.
```bash
python scripts/data_processing/process_acaps_data.py
```

### `process_acled_data.py`
Processes ACLED conflict data and calculates market-level conflict metrics.
```bash
python scripts/data_processing/process_acled_data.py
```

### `run_spatial_joins.py`
Performs spatial joins to map markets to control zones and identify boundary markets.
```bash
python scripts/data_processing/run_spatial_joins.py
```

## Analysis Scripts

### `build_panel_datasets.py`
Builds integrated panel datasets for econometric analysis.
```bash
python scripts/analysis/build_panel_datasets.py
```

## Utility Scripts

### `test_full_pipeline.py`
Runs end-to-end tests on the entire data processing pipeline.
```bash
python scripts/utilities/test_full_pipeline.py
```

### `export_notebooks_to_pdf.py`
Exports Jupyter notebooks to PDF format for sharing and archival.
```bash
python scripts/utilities/export_notebooks_to_pdf.py
```

### `create_visualization_gallery.py`
Creates an organized gallery of visualizations from the analysis.
```bash
python scripts/utilities/create_visualization_gallery.py
```

## Typical Workflow

1. **Data Collection**
   ```bash
   python scripts/data_collection/download_data.py
   python scripts/data_collection/download_acled_data.py
   ```

2. **Data Processing**
   ```bash
   python scripts/data_processing/process_wfp_data.py
   python scripts/data_processing/process_acaps_data.py
   python scripts/data_processing/process_acled_data.py
   python scripts/data_processing/run_spatial_joins.py
   ```

3. **Analysis**
   ```bash
   python scripts/analysis/build_panel_datasets.py
   ```

4. **Testing & Export**
   ```bash
   python scripts/utilities/test_full_pipeline.py
   python scripts/utilities/export_notebooks_to_pdf.py
   python scripts/utilities/create_visualization_gallery.py
   ```

## Requirements

All scripts require the Yemen Market package to be installed:
```bash
pip install -e .
```

## Notes

- Scripts should be run from the project root directory
- Each script includes comprehensive logging using the enhanced logging system
- Most scripts can be run with `--help` for usage information
- Processing scripts check for prerequisites before running