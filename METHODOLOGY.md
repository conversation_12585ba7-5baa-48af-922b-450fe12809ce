# Enhanced Methodology for Yemen Market Integration Analysis

## 3. Methodology

### 3.1 Data Sources

Our analysis leverages a comprehensive dataset drawn from multiple sources to capture Yemen's complex market dynamics:

#### 3.1.1 Primary Data Sources

• **Commodity Prices and Exchange Rates**: Weekly prices for key staples—wheat, rice, and sugar—collected from multiple Yemeni markets by the World Food Programme (WFP). The dataset crucially includes market-level exchange rates, enabling analysis of dual exchange rate dynamics. High-frequency data permits detailed tracking of price movements across different political control zones. Coverage: March 2009 - December 2024.

• **Areas of Control**: District-level control status from ACAPS Yemen Analysis Hub, providing bi-weekly updates on territorial control (Houthi vs. Government). Coverage: August 2021 - October 2024. These data enable mapping of markets to political zones for exchange rate differential analysis.

• **Geographic Data**: Market locations with coordinates and administrative boundaries sourced from HDX Common Operational Datasets. We augment traditional distance metrics with control boundary penalties to capture how political fragmentation affects spatial market relationships.

• **Conflict Intensity**: Security incidents from the Armed Conflict Location & Event Data Project (ACLED), supplemented by control change frequency as a proxy for conflict intensity. We create time-varying conflict indices at market level.

#### 3.1.2 Supplementary Data Requirements

• **Fuel Prices**: District-level fuel prices as proxy for transport costs (if available from ACAPS)
• **Aid Flows**: OCHA Financial Tracking Service data for exchange rate instruments
• **Infrastructure**: OpenStreetMap road conditions for enhanced spatial weights
• **International Prices**: FAO GIEWS international commodity prices for external shock controls

### 3.2 Enhanced Econometric Framework

We implement a multi-stage approach addressing the methodological limitations identified in the review:

#### 3.2.1 Time-Varying Parameter Framework

Given the dynamic nature of Yemen's conflict, we replace static parameter assumptions with time-varying specifications:

**Bayesian TVP-VECM Specification**:
$$\Delta P_t = \alpha_t + \beta_t EC_{t-1} + \sum_{i=1}^{p} \Gamma_{i,t} \Delta P_{t-i} + \epsilon_t$$

where all parameters evolve according to random walks:
$$\theta_t = \theta_{t-1} + \nu_t, \quad \nu_t \sim N(0, Q)$$

This captures gradual changes in market integration as conflict intensity fluctuates.

#### 3.2.2 Multiple Threshold Estimation

We extend the single-threshold model to allow for multiple regimes:

$$\Delta P_t = \begin{cases}
A_1 X_t(\beta) + u_{1t} & \text{if } w_t(\beta) \leq \gamma_1 \\
A_2 X_t(\beta) + u_{2t} & \text{if } \gamma_1 < w_t(\beta) \leq \gamma_2 \\
A_3 X_t(\beta) + u_{3t} & \text{if } w_t(\beta) > \gamma_2
\end{cases}$$

Thresholds $\gamma_1$ and $\gamma_2$ are estimated using sequential testing (Gonzalo and Pitarakis 2002).

#### 3.2.3 Endogeneity Correction

To address bidirectional causality between exchange rates and commodity prices:

**Instrumental Variables Approach**:
- **Instruments**: 
  - International oil price shocks (affects government revenue)
  - Aid disbursement announcements (foreign currency inflows)
  - Lag structures exploiting timing differences

**Control Function Approach**:
First stage: $ER_{diff,t} = \pi_0 + \pi_1 Z_t + \pi_2 X_t + v_t$
Second stage: Include $\hat{v}_t$ in main regression

#### 3.2.4 Network-Augmented Spatial Analysis

We construct enhanced spatial weight matrices incorporating both geographic and network dimensions:

$$W_{ij,t}^* = W_{ij}^{geo} \times \delta_{ij,t}^{control} \times \eta_{ij}^{network}$$

where:
- $W_{ij}^{geo}$: Geographic distance-based weights
- $\delta_{ij,t}^{control}$: Penalty for crossing control boundaries (time-varying)
- $\eta_{ij}^{network}$: Trading relationship strength (inferred from price correlations)

#### 3.2.5 Regime-Switching Framework

Implement Markov-switching models to capture discrete conflict states:

$$P_t = \mu_{S_t} + \sum_{j=1}^{p} \phi_{j,S_t} P_{t-j} + \sigma_{S_t} \epsilon_t$$

where $S_t \in \{1, 2, 3\}$ represents low, medium, and high conflict intensity regimes.

### 3.3 Identification Strategy

#### 3.3.1 Natural Experiments

Exploit quasi-random variation from:
- Sudden changes in territorial control
- Unexpected coalition airstrikes affecting specific markets
- Policy announcements (e.g., Central Bank relocation)

#### 3.3.2 Regression Discontinuity

For markets near control boundaries:
$$Y_{it} = \alpha + \beta \cdot \mathbf{1}[Control_i = Houthi] + f(distance_{i}) + \epsilon_{it}$$

#### 3.3.3 Synthetic Control Methods

For major policy interventions, construct synthetic controls from unaffected markets.

### 3.4 Machine Learning Integration

Complement econometric analysis with ML techniques:

#### 3.4.1 Pattern Detection
```python
# Random Forest for interaction effects
rf_model = RandomForestRegressor(n_estimators=1000)
rf_model.fit(X_features, price_differential)

# SHAP values for interpretation
explainer = shap.TreeExplainer(rf_model)
shap_values = explainer.shap_values(X_features)
```

#### 3.4.2 Price Convergence Prediction
LSTM networks for forecasting convergence patterns under different scenarios.

### 3.5 Enhanced Robustness Framework

#### 3.5.1 Comprehensive Diagnostic Testing

| Test | Purpose | Implementation |
|------|---------|----------------|
| Pesaran CD | Cross-sectional dependence | Factor models if detected |
| Andrews Sup-F | Unknown breakpoints | Multiple break detection |
| Hansen J-stat | Overidentification | Validate instruments |
| Kleibergen-Paap | Weak instruments | Robust inference |
| Spatial HAC | Spatial correlation in errors | Conley (1999) standard errors |

#### 3.5.2 Sensitivity Analyses

1. **Rolling Window Estimation**: 24-month windows to detect parameter instability
2. **Bootstrap Inference**: Wild bootstrap for threshold models
3. **Leave-One-Out**: Influence of individual markets
4. **Alternative Specifications**: 
   - Quantile cointegration
   - Nonparametric methods
   - High-frequency event studies

### 3.6 Policy Simulation Framework

#### 3.6.1 Dynamic Transition Modeling

Replace static simulations with dynamic paths:

$$ER_{diff,t}^{sim} = ER_{diff,0} \times (1 - \lambda(t))$$

where $\lambda(t)$ represents different convergence scenarios:
- Immediate: $\lambda(t) = \mathbf{1}[t > 0]$
- Gradual: $\lambda(t) = \min(t/T, 1)$
- Stepped: $\lambda(t) = \lfloor t/k \rfloor \times \delta$

#### 3.6.2 Uncertainty Quantification

- Bootstrap confidence bands for all scenarios
- Fan charts showing probability distributions
- Scenario analysis across conflict intensity levels

### 3.7 Implementation Workflow

```mermaid
graph TD
    A[Data Collection] --> B[Geographic Mapping]
    B --> C[Control Zone Assignment]
    C --> D[Exchange Rate Differentials]
    D --> E[Network Inference]
    E --> F[TVP-VECM Estimation]
    F --> G[Threshold Testing]
    G --> H[Robustness Checks]
    H --> I[Policy Simulations]
    I --> J[Uncertainty Quantification]
```

### 3.8 Computational Considerations

All models estimated using:
- **Software**: Python 3.10+ with statsmodels, pymc3, scikit-learn
- **Parallel Processing**: joblib for bootstrap and cross-validation
- **Reproducibility**: Fixed random seeds, version-controlled environment

This enhanced methodology addresses identified gaps while maintaining computational feasibility with available HDX data. The multi-pronged approach combining time-varying parameters, network analysis, and machine learning provides robust insights into Yemen's complex market dynamics.
