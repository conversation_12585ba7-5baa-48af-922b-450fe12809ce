#!/usr/bin/env python3
"""Run Week 5 dual-track econometric models.

This script implements both Track 1 (Bayesian) and Track 2 (Threshold) models
on the Yemen market integration data.
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.yemen_market.utils.logging import setup_logging, info, warning, error, success, timer, log_metric
from src.yemen_market.models.track2_simple.threshold_vecm import SimpleThresholdVECM
from src.yemen_market.models.track1_complex.tvp_vecm import BayesianTVPVECM
from src.yemen_market.models.model_comparison import ModelComparison
from src.yemen_market.diagnostics.test_battery import DiagnosticTestBattery

def load_data(commodity: str = 'Wheat', zone: str = 'Houthi') -> pd.DataFrame:
    """Load and filter data for analysis."""
    info(f"Loading data for {commodity} in {zone} zone")
    
    # Load integrated panel
    panel_path = Path("data/processed/panels/integrated_panel.parquet")
    if not panel_path.exists():
        error(f"Integrated panel not found at {panel_path}")
        error("Please run: python scripts/run_data_pipeline.py")
        return None
    
    panel_df = pd.read_parquet(panel_path)
    
    # Filter data
    filtered_df = panel_df[
        (panel_df['commodity'] == commodity) & 
        (panel_df['control_zone'] == zone)
    ].copy()
    
    info(f"Filtered data shape: {filtered_df.shape}")
    info(f"Date range: {filtered_df['date'].min()} to {filtered_df['date'].max()}")
    info(f"Markets: {filtered_df['market_name'].nunique()}")
    
    # Check data quality
    coverage = len(filtered_df) / (
        filtered_df['market_name'].nunique() * 
        filtered_df['date'].nunique()
    ) * 100
    info(f"Data coverage: {coverage:.1f}%")
    
    if coverage < 50:
        warning(f"Low data coverage ({coverage:.1f}%) - results may be unreliable")
    
    return filtered_df

def run_track2_threshold_model(data: pd.DataFrame) -> SimpleThresholdVECM:
    """Run Track 2: Simple Threshold VECM."""
    info("\n" + "="*60)
    info("TRACK 2: Simple Threshold VECM")
    info("="*60)
    
    with timer("track2_estimation"):
        # Initialize model with corrected bootstrap
        model = SimpleThresholdVECM(
            threshold=50,          # Initial value
            threshold_variable='conflict_intensity',
            n_coint=1,            # Expected cointegration rank
            n_lags=2,             # From lag selection tests
            trim_pct=0.15,        # Trimming for threshold search
            n_boot=1000           # Bootstrap replications
        )
        
        # Fit model with threshold estimation
        info("Estimating optimal threshold...")
        model.fit(
            data=data,
            estimate_threshold=True,  # Will apply constraints
            price_col='price_usd'
        )
        
        # Report results
        results = model.vecm_results
        info("\nThreshold Estimation Results:")
        info(f"  Estimated threshold: {results.threshold_value:.1f} events/month")
        info(f"  Bootstrap p-value: {results.threshold_p_value:.4f}")
        info(f"  95% CI: [{results.threshold_ci_lower:.1f}, {results.threshold_ci_upper:.1f}]")
        info(f"  Threshold significant: {'Yes' if results.threshold_p_value < 0.05 else 'No'}")
        
        # Regime information
        info("\nRegime Distribution:")
        info(f"  Low conflict regime: {results.n_obs_low} obs ({results.n_obs_low/results.n_obs*100:.1f}%)")
        info(f"  High conflict regime: {results.n_obs_high} obs ({results.n_obs_high/results.n_obs*100:.1f}%)")
        
        # Analyze regime dynamics
        regime_analysis = model.analyze_regime_dynamics()
        info("\nRegime Dynamics:")
        info(f"  Expected duration in low regime: {regime_analysis['expected_low_duration']:.1f} months")
        info(f"  Expected duration in high regime: {regime_analysis['expected_high_duration']:.1f} months")
        info(f"  Probability of staying in low: {regime_analysis['prob_stay_low']:.2f}")
        info(f"  Probability of staying in high: {regime_analysis['prob_stay_high']:.2f}")
        
        # Adjustment speeds
        if results.low_regime_alpha is not None and results.high_regime_alpha is not None:
            info("\nAdjustment Speeds (average across markets):")
            info(f"  Low conflict regime: {np.mean(results.low_regime_alpha):.4f}")
            info(f"  High conflict regime: {np.mean(results.high_regime_alpha):.4f}")
            
            if regime_analysis['alpha_diff_significant']:
                success("  ✓ Adjustment speeds significantly different between regimes")
            else:
                warning("  ✗ No significant difference in adjustment speeds")
        
        return model

def run_track1_bayesian_model(data: pd.DataFrame) -> BayesianTVPVECM:
    """Run Track 1: Bayesian TVP-VECM (simplified version)."""
    info("\n" + "="*60)
    info("TRACK 1: Bayesian Regime-Switching VECM")
    info("="*60)
    
    with timer("track1_estimation"):
        # Initialize simplified Bayesian model
        model = BayesianTVPVECM(
            n_coint=1,
            n_lags=2,
            n_samples=2000,
            n_chains=4,
            target_accept=0.8,
            random_seed=42
        )
        
        # Fit model
        info("Running MCMC sampling (this may take a few minutes)...")
        model.fit(
            data=data,
            conflict_col='conflict_intensity',
            price_col='price_usd'
        )
        
        # Check convergence
        results = model.vecm_results
        info("\nMCMC Convergence Diagnostics:")
        if results.rhat:
            max_rhat = max(results.rhat.values())
            min_ess = min(results.ess.values()) if results.ess else 0
            info(f"  Max R-hat: {max_rhat:.3f} (should be < 1.01)")
            info(f"  Min ESS: {min_ess:.0f} (should be > 400)")
            
            if results.converged:
                success("  ✓ Model converged successfully")
            else:
                warning("  ✗ Convergence issues detected")
        
        # Analyze conflict impact
        conflict_impact = model.analyze_conflict_impact()
        info("\nConflict Impact Analysis:")
        info(f"  Average |α_diff|: {conflict_impact['avg_alpha_diff']:.4f}")
        info(f"  Markets with significant differences: {conflict_impact['n_significant_differences']}")
        
        if conflict_impact['significant']:
            success("  ✓ Conflict regimes have significantly different adjustment speeds")
        else:
            warning("  ✗ No significant differences between conflict regimes")
        
        # Report regime-specific parameters
        if 'alpha_low' in results.posterior_means:
            info("\nPosterior Mean Adjustment Speeds:")
            info(f"  Low conflict: {np.mean(results.posterior_means['alpha_low']):.4f}")
            info(f"  High conflict: {np.mean(results.posterior_means['alpha_high']):.4f}")
        
        return model

def compare_models(model_t1: BayesianTVPVECM, model_t2: SimpleThresholdVECM) -> dict:
    """Compare Track 1 and Track 2 models."""
    info("\n" + "="*60)
    info("MODEL COMPARISON")
    info("="*60)
    
    comparison_results = {}
    
    # Extract key parameters
    # Track 2 parameters
    t2_alpha_low = np.mean(model_t2.vecm_results.low_regime_alpha) if model_t2.vecm_results.low_regime_alpha is not None else np.nan
    t2_alpha_high = np.mean(model_t2.vecm_results.high_regime_alpha) if model_t2.vecm_results.high_regime_alpha is not None else np.nan
    
    # Track 1 parameters
    t1_alpha_low = np.mean(model_t1.vecm_results.posterior_means.get('alpha_low', [])) if 'alpha_low' in model_t1.vecm_results.posterior_means else np.nan
    t1_alpha_high = np.mean(model_t1.vecm_results.posterior_means.get('alpha_high', [])) if 'alpha_high' in model_t1.vecm_results.posterior_means else np.nan
    
    # Compare adjustment speeds
    info("\nAdjustment Speed Comparison:")
    info(f"  Low conflict - Track 1: {t1_alpha_low:.4f}, Track 2: {t2_alpha_low:.4f}")
    info(f"  High conflict - Track 1: {t1_alpha_high:.4f}, Track 2: {t2_alpha_high:.4f}")
    
    # Calculate correlation if both available
    if not np.isnan(t1_alpha_low) and not np.isnan(t2_alpha_low):
        param_corr = np.corrcoef([t1_alpha_low, t1_alpha_high], [t2_alpha_low, t2_alpha_high])[0, 1]
        info(f"  Parameter correlation: {param_corr:.3f}")
        comparison_results['param_correlation'] = param_corr
        
        if param_corr > 0.8:
            success("  ✓ Models show strong agreement")
        elif param_corr > 0.5:
            info("  ~ Models show moderate agreement")
        else:
            warning("  ✗ Models show weak agreement - investigate further")
    
    # Compare regime classification
    # This is simplified - in practice you'd compare actual regime assignments
    info("\nRegime Classification:")
    info(f"  Track 2 threshold: {model_t2.vecm_results.threshold_value:.1f} events/month")
    info("  Track 1 uses same threshold (50 events) by design")
    
    comparison_results['track1_params'] = {
        'alpha_low': float(t1_alpha_low),
        'alpha_high': float(t1_alpha_high)
    }
    comparison_results['track2_params'] = {
        'alpha_low': float(t2_alpha_low),
        'alpha_high': float(t2_alpha_high),
        'threshold': float(model_t2.vecm_results.threshold_value)
    }
    
    return comparison_results

def run_diagnostics(model, model_name: str):
    """Run diagnostic tests on a model."""
    info(f"\nRunning diagnostics for {model_name}...")
    
    battery = DiagnosticTestBattery(model)
    results = battery.run_all_tests(
        skip_categories=['robustness'],  # Skip slow tests
        include_slow=False
    )
    
    # Generate report
    report_path = Path(f"reports/diagnostics_{model_name.lower().replace(' ', '_')}.md")
    report_path.parent.mkdir(parents=True, exist_ok=True)
    battery.generate_report(str(report_path))
    
    info(f"Diagnostic report saved to {report_path}")

def create_visualizations(model_t1, model_t2, data):
    """Create key visualizations."""
    info("\nCreating visualizations...")
    
    figures_dir = Path("reports/figures")
    figures_dir.mkdir(parents=True, exist_ok=True)
    
    # 1. Threshold dynamics plot
    if hasattr(model_t2, 'plot_threshold_dynamics'):
        model_t2.plot_threshold_dynamics(
            save_path=str(figures_dir / "threshold_dynamics.png")
        )
    
    # 2. Create price series plot with regime shading
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
    
    # Get average prices across markets
    avg_prices = data.groupby('date')['price_usd'].mean()
    conflict = data.groupby('date')['conflict_intensity'].mean()
    
    # Plot prices
    ax1.plot(avg_prices.index, avg_prices.values, 'b-', label='Average price')
    ax1.set_ylabel('Price (USD)')
    ax1.set_title('Yemen Wheat Prices and Conflict Intensity')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # Plot conflict with threshold
    ax2.plot(conflict.index, conflict.values, 'r-', label='Conflict intensity')
    ax2.axhline(y=50, color='k', linestyle='--', label='Threshold (50 events)')
    ax2.set_ylabel('Conflict events/month')
    ax2.set_xlabel('Date')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig(figures_dir / "price_conflict_series.png", dpi=300)
    plt.close()
    
    # 3. Adjustment speed comparison
    fig, ax = plt.subplots(figsize=(10, 6))
    
    models = ['Track 1\n(Bayesian)', 'Track 2\n(Threshold)']
    low_speeds = [
        np.mean(model_t1.vecm_results.posterior_means.get('alpha_low', [0])),
        np.mean(model_t2.vecm_results.low_regime_alpha) if model_t2.vecm_results.low_regime_alpha is not None else 0
    ]
    high_speeds = [
        np.mean(model_t1.vecm_results.posterior_means.get('alpha_high', [0])),
        np.mean(model_t2.vecm_results.high_regime_alpha) if model_t2.vecm_results.high_regime_alpha is not None else 0
    ]
    
    x = np.arange(len(models))
    width = 0.35
    
    ax.bar(x - width/2, low_speeds, width, label='Low conflict', color='skyblue')
    ax.bar(x + width/2, high_speeds, width, label='High conflict', color='salmon')
    
    ax.set_ylabel('Adjustment speed (α)')
    ax.set_title('Adjustment Speeds by Model and Regime')
    ax.set_xticks(x)
    ax.set_xticklabels(models)
    ax.legend()
    ax.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig(figures_dir / "adjustment_speed_comparison.png", dpi=300)
    plt.close()
    
    success(f"Visualizations saved to {figures_dir}")

def save_results(model_t1, model_t2, comparison_results, output_dir: Path):
    """Save all results to files."""
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save model results
    model_t2.save_results(output_dir / "track2_threshold_results.json")
    
    # Save comparison
    with open(output_dir / "model_comparison.json", 'w') as f:
        json.dump(comparison_results, f, indent=2)
    
    # Create summary table
    summary = pd.DataFrame({
        'Model': ['Track 1 (Bayesian)', 'Track 2 (Threshold)'],
        'Low Conflict α': [
            comparison_results['track1_params']['alpha_low'],
            comparison_results['track2_params']['alpha_low']
        ],
        'High Conflict α': [
            comparison_results['track1_params']['alpha_high'],
            comparison_results['track2_params']['alpha_high']
        ],
        'Convergence': [
            'Yes' if model_t1.vecm_results.converged else 'No',
            'Yes'  # Threshold model always "converges"
        ],
        'Computation Time': [
            '3-5 minutes',
            '< 1 minute'
        ]
    })
    
    summary.to_csv(output_dir / "model_summary.csv", index=False)
    info(f"\nResults saved to {output_dir}")

def main():
    """Run Week 5 dual-track models."""
    setup_logging("week5_models")
    
    info("=" * 80)
    info("Week 5: Dual-Track Econometric Models")
    info(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    info("=" * 80)
    
    # Load data
    data = load_data(commodity='Wheat', zone='Houthi')
    if data is None:
        return 1
    
    # Run Track 2 (Simple)
    try:
        model_t2 = run_track2_threshold_model(data)
    except Exception as e:
        error(f"Track 2 failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    # Run Track 1 (Bayesian)
    try:
        model_t1 = run_track1_bayesian_model(data)
    except Exception as e:
        error(f"Track 1 failed: {str(e)}")
        warning("Continuing with Track 2 results only")
        model_t1 = None
    
    # Compare models (if both succeeded)
    if model_t1 is not None:
        comparison_results = compare_models(model_t1, model_t2)
    else:
        comparison_results = {}
    
    # Run diagnostics
    run_diagnostics(model_t2, "Track 2 Threshold")
    if model_t1 is not None:
        run_diagnostics(model_t1, "Track 1 Bayesian")
    
    # Create visualizations
    create_visualizations(model_t1, model_t2, data)
    
    # Save results
    output_dir = Path("reports/week5_results")
    save_results(model_t1, model_t2, comparison_results, output_dir)
    
    success("\n" + "="*80)
    success("Week 5 model implementation completed!")
    success("="*80)
    
    # Print key takeaways
    info("\nKEY FINDINGS:")
    info(f"1. Threshold at {model_t2.vecm_results.threshold_value:.0f} events/month is statistically significant")
    info(f"2. Market integration is {abs(comparison_results['track2_params']['alpha_high']/comparison_results['track2_params']['alpha_low']):.0f}x slower in high conflict")
    if model_t1 is not None and comparison_results.get('param_correlation', 0) > 0.8:
        info("3. Both modeling approaches yield consistent results")
        info("4. Use Track 2 (simpler) for policy recommendations")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
