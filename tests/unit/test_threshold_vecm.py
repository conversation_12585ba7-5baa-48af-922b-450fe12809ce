"""Unit tests for Simple Threshold VECM implementation."""

import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from yemen_market.models.track2_simple.threshold_vecm import SimpleThresholdVECM


class TestSimpleThresholdVECM:
    """Test suite for SimpleThresholdVECM class."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample data for testing."""
        n_periods = 100
        dates = pd.date_range(start='2020-01-01', periods=n_periods, freq='M')
        
        # Create price data with regime switching behavior
        np.random.seed(42)
        prices = pd.DataFrame({
            'date': dates
        })
        
        # Create prices that switch between regimes
        regime = np.zeros(n_periods)
        regime[40:70] = 1  # High conflict regime
        
        for i in range(3):
            base_price = 100 + i * 5
            noise_low = np.random.randn(n_periods) * 1
            noise_high = np.random.randn(n_periods) * 3
            
            price = base_price + np.where(regime == 0, noise_low, noise_high)
            prices[f'market_{i+1}'] = np.cumsum(price) / 10 + base_price
        
        # Create threshold variable (conflict intensity)
        threshold_var = pd.Series(
            np.where(regime == 0, 
                    np.random.uniform(0, 5, n_periods),
                    np.random.uniform(5, 15, n_periods)),
            index=dates,
            name='conflict_intensity'
        )
        
        return prices, threshold_var
    
    @pytest.fixture
    def model(self):
        """Create model instance."""
        return SimpleThresholdVECM(
            n_regimes=2,
            bootstrap_reps=100,  # Small for testing
            conf_level=0.95
        )
    
    def test_initialization(self):
        """Test model initialization."""
        model = SimpleThresholdVECM(
            n_regimes=3,
            bootstrap_reps=1000,
            conf_level=0.90
        )
        
        assert model.n_regimes == 3
        assert model.bootstrap_reps == 1000
        assert model.conf_level == 0.90
        assert model.threshold is None
        assert model.regime_results is None
    
    def test_estimate_optimal_threshold(self, model, sample_data):
        """Test threshold estimation."""
        prices, threshold_var = sample_data
        price_cols = ['market_1', 'market_2', 'market_3']
        
        # Prepare data
        data = model.prepare_data(prices, price_cols)
        
        # Estimate threshold
        optimal_threshold = model._estimate_optimal_threshold(data, threshold_var[1:])
        
        assert isinstance(optimal_threshold, float)
        assert threshold_var.min() <= optimal_threshold <= threshold_var.max()
    
    def test_fit(self, model, sample_data):
        """Test model fitting."""
        prices, threshold_var = sample_data
        price_cols = ['market_1', 'market_2', 'market_3']
        
        # Fit model
        model.fit(prices, price_cols, threshold_var=threshold_var)
        
        assert model.threshold is not None
        assert model.regime_results is not None
        assert len(model.regime_results) == model.n_regimes
        
        # Check regime results structure
        for regime, results in model.regime_results.items():
            assert 'alpha' in results
            assert 'beta' in results
            assert 'sigma' in results
            assert 'n_obs' in results
    
    def test_predict(self, model, sample_data):
        """Test prediction generation."""
        prices, threshold_var = sample_data
        price_cols = ['market_1', 'market_2', 'market_3']
        
        # Fit model first
        model.fit(prices, price_cols, threshold_var=threshold_var)
        
        # Generate predictions for different regimes
        future_threshold = pd.Series([2.0, 8.0, 3.0, 10.0])  # Mix of regimes
        predictions = model.predict(n_periods=4, threshold_values=future_threshold)
        
        assert isinstance(predictions, pd.DataFrame)
        assert len(predictions) == 4
        assert all(col in predictions.columns for col in price_cols)
    
    def test_test_threshold_significance(self, model, sample_data):
        """Test bootstrap threshold significance testing."""
        prices, threshold_var = sample_data
        price_cols = ['market_1', 'market_2', 'market_3']
        
        # Fit model
        model.fit(prices, price_cols, threshold_var=threshold_var)
        
        # Test significance
        with patch.object(model, 'bootstrap_reps', 50):  # Reduce for speed
            test_results = model.test_threshold_significance()
        
        assert isinstance(test_results, dict)
        assert 'lr_statistic' in test_results
        assert 'p_value' in test_results
        assert 'bootstrap_distribution' in test_results
        assert 0 <= test_results['p_value'] <= 1
    
    def test_analyze_regime_dynamics(self, model, sample_data):
        """Test regime dynamics analysis."""
        prices, threshold_var = sample_data
        price_cols = ['market_1', 'market_2', 'market_3']
        
        # Fit model
        model.fit(prices, price_cols, threshold_var=threshold_var)
        
        # Analyze dynamics
        dynamics = model.analyze_regime_dynamics()
        
        assert isinstance(dynamics, dict)
        assert 'regime_characteristics' in dynamics
        assert 'transition_analysis' in dynamics
        assert 'half_lives' in dynamics
        
        # Check half-lives for each regime
        for regime in range(model.n_regimes):
            assert f'regime_{regime}' in dynamics['half_lives']
    
    def test_plot_regime_dynamics(self, model, sample_data, tmp_path):
        """Test regime dynamics plotting."""
        prices, threshold_var = sample_data
        price_cols = ['market_1', 'market_2', 'market_3']
        
        # Fit model
        model.fit(prices, price_cols, threshold_var=threshold_var)
        
        # Create plot
        save_path = tmp_path / "regime_dynamics.png"
        model.plot_regime_dynamics(save_path=str(save_path))
        
        assert save_path.exists()
    
    def test_get_regime_assignment(self, model, sample_data):
        """Test regime assignment."""
        prices, threshold_var = sample_data
        price_cols = ['market_1', 'market_2', 'market_3']
        
        # Fit model
        model.fit(prices, price_cols, threshold_var=threshold_var)
        
        # Get regime assignments
        regimes = model.get_regime_assignment(threshold_var)
        
        assert isinstance(regimes, pd.Series)
        assert len(regimes) == len(threshold_var)
        assert all(r in range(model.n_regimes) for r in regimes.unique())
    
    def test_multiple_thresholds(self):
        """Test model with multiple thresholds."""
        model = SimpleThresholdVECM(n_regimes=3)
        
        # Create data with 3 regimes
        n = 150
        dates = pd.date_range(start='2020-01-01', periods=n, freq='M')
        
        prices = pd.DataFrame({
            'date': dates,
            'market_1': 100 + np.cumsum(np.random.randn(n)),
            'market_2': 100 + np.cumsum(np.random.randn(n))
        })
        
        threshold_var = pd.Series(np.random.uniform(0, 20, n), index=dates)
        
        # Fit model
        model.fit(prices, ['market_1', 'market_2'], threshold_var=threshold_var)
        
        # Should have 2 thresholds for 3 regimes
        assert isinstance(model.threshold, (list, np.ndarray))
        assert len(model.threshold) == 2
    
    def test_invalid_regimes(self):
        """Test invalid number of regimes."""
        with pytest.raises(ValueError):
            SimpleThresholdVECM(n_regimes=1)
    
    def test_missing_threshold_variable(self, model, sample_data):
        """Test fitting without threshold variable."""
        prices, _ = sample_data
        price_cols = ['market_1', 'market_2', 'market_3']
        
        with pytest.raises(ValueError, match="threshold_var"):
            model.fit(prices, price_cols)