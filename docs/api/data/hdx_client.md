# HDXClient API Reference

The `HDXClient` class provides an interface for downloading datasets from the Humanitarian Data Exchange (HDX).

## Class: HDXClient

```python
from yemen_market.data import HDXClient

client = HDXClient(download_dir=None)
```

### Parameters

- **download_dir** (Path, optional): Directory to save downloaded files. Defaults to `RAW_DATA_DIR` from config.

### Methods

#### download_dataset()
Download a complete dataset from HDX.

```python
files = client.download_dataset(dataset_id, force_download=False)
```

**Parameters:**
- **dataset_id** (str): HDX dataset identifier
- **force_download** (bool): Force re-download even if files exist

**Returns:**
- **files** (list[Path]): List of downloaded file paths

#### download_resource()
Download a specific resource from a dataset.

```python
file_path = client.download_resource(
    dataset_id,
    resource_name,
    force_download=False
)
```

**Parameters:**
- **dataset_id** (str): HDX dataset identifier
- **resource_name** (str): Name pattern to match resource
- **force_download** (bool): Force re-download

**Returns:**
- **file_path** (Path): Path to downloaded file

#### get_wfp_food_prices()
Get WFP food price data for Yemen.

```python
df = client.get_wfp_food_prices(force_download=False)
```

**Returns:**
- **df** (DataFrame): WFP price data

#### get_acaps_control_data()
Get ACAPS areas of control data.

```python
gdf = client.get_acaps_control_data(date_str=None, force_download=False)
```

**Parameters:**
- **date_str** (str, optional): Specific date version (e.g., '20240327')
- **force_download** (bool): Force re-download

**Returns:**
- **gdf** (GeoDataFrame): Control zone geometries

### Dataset IDs

```python
# Key HDX dataset identifiers
WFP_DATASET = "wfp-food-prices-for-yemen"
ACAPS_DATASET = "yemen-areas-of-control"
ADMIN_BOUNDARIES = "cod-ab-yem"
```

### Example Usage

```python
from yemen_market.data import HDXClient

# Initialize client
client = HDXClient()

# Download WFP price data
wfp_df = client.get_wfp_food_prices()
print(f"Downloaded {len(wfp_df)} price records")

# Download specific ACAPS control data
control_zones = client.get_acaps_control_data(date_str='20241024')

# Download administrative boundaries
boundaries = client.download_dataset('cod-ab-yem')
```

### Error Handling

The client includes robust error handling:
- Retries failed downloads (3 attempts)
- Validates downloaded files
- Provides informative error messages
- Falls back to cached data when available

### Caching

- Downloads are cached in the configured `RAW_DATA_DIR`
- Use `force_download=True` to refresh cached data
- Checks file integrity before using cached versions