# 📍 QUICK REFERENCE: Where Does This Information Go?

Use this guide to quickly determine where to document different types of information.

## Progress Information

| What | Where | Example |
|------|-------|---------|
| Overall % complete | `reports/progress/README.md` | "38% Complete" |
| Milestone status | `reports/progress/README.md` | "M1: Data Pipeline ✅" |
| Completed tasks list | `reports/progress/README.md` | "1. HDXClient ✅" |
| Upcoming tasks | `reports/progress/README.md` | "Next 5 tasks..." |
| Sprint details | `reports/progress/README.md` | "Week 3-4 Goals" |
| Session notes | `reports/progress/README.md` | "2025-05-27: Fixed..." |

## Current Development

| What | Where | Example |
|------|-------|---------|
| What I'm doing NOW | `.claude/ACTIVE_CONTEXT.md` | "Currently Working On" |
| Next immediate steps | `.claude/ACTIVE_CONTEXT.md` | "Immediate Next Actions" |
| How to resume | `.claude/ACTIVE_CONTEXT.md` | "To Resume: Open..." |
| Recent fixes | `.claude/ACTIVE_CONTEXT.md` | "Recent Accomplishments" |
| Current blockers | `.claude/ACTIVE_CONTEXT.md` | "Spatial joins issue" |

## Development Rules

| What | Where | Example |
|------|-------|---------|
| Coding standards | `CLAUDE.md` | "Use NumPy docstrings" |
| Logging requirements | `CLAUDE.md` | "Always use enhanced..." |
| Critical warnings | `CLAUDE.md` | "NEVER create temp..." |
| MCP configuration | `CLAUDE.md` | "Enable filesystem..." |
| Documentation rules | `CLAUDE.md` | "Documentation Hierarchy" |

## Public Information

| What | Where | Example |
|------|-------|---------|
| Project overview | `README.md` | "This repository..." |
| Installation | `README.md` | "conda env create..." |
| Usage examples | `README.md` | "import yemen_market..." |
| Requirements | `README.md` | "Python 3.10+" |
| Citation | `README.md` | "@techreport{...}" |

## Technical Details

| What | Where | Example |
|------|-------|---------|
| Econometric methods | `METHODOLOGY.md` | "Hansen-Seo TVECM" |
| Model parameters | `METHODOLOGY.md` | "n_thresholds = 2" |
| Academic references | `METHODOLOGY.md` | "Balke & Fomby 1997" |
| Data pipeline specs | `docs/technical/` | "HDXClient caching" |

## Historical Context

| What | Where | Example |
|------|-------|---------|
| Past decisions | `.claude/project_memory.md` | "Chose monthly..." |
| Lessons learned | `.claude/project_memory.md` | "ACAPS has nested..." |
| Session history | `.claude/project_memory.md` | "2025-05-27: Created..." |
| Major changes | `.claude/project_memory.md` | "Reorganized docs..." |

## ❌ NEVER CREATE THESE FILES
- PROJECT_STATUS.md
- CURRENT_WORK.md
- PROGRESS.md
- STATUS_UPDATE.md
- TODO.md (use sprint tasks)
- NOTES.md (use appropriate location above)

## 🤔 Still Unsure?
1. Is it about progress? → `reports/progress/README.md`
2. Is it about NOW? → `.claude/ACTIVE_CONTEXT.md`
3. Is it a rule? → `CLAUDE.md`
4. Is it public? → `README.md`
5. Is it technical? → `METHODOLOGY.md` or `docs/`
6. Is it history? → `.claude/project_memory.md`

When in doubt, DON'T create a new file - add to existing ones!